// pages/login/login.js
Page({

  /**
   * 页面的初始数据
   */







  data: {
    getPhone: false,
    bindKeyPhone: '',
    intText: '获取验证码',
    color: '#1AAD19',
    intNum: 60,
    isSend: false,
    checkd: false,
    dsiabled: true
  },
  checkboxChange: function (e) {
    let a = "";
    let dsiabled = e.detail.value.length == 0 ? true : false;
    this.setData({
      dsiabled: dsiabled
    })
  },
  /*微信获取号码*/
  getPhoneNumber: function (e) {
    let self = this;
    var app = getApp();
    var url = app.globalData.requestUrl;
    debugger
    if (e.detail.iv) {
      console.log(e)
      wx.login({
        success: res => {
          console.log(res)
          let jscode = res.code
          let encryptedData = e.detail.encryptedData
          let iv = e.detail.iv
          wx.request({
            method: 'POST',
            url: url + 'wechat/login',
            data: {
              app_id: app.globalData.app_id,
              app_key: app.globalData.app_key,
              js_code: jscode,
              iv: iv,
              encrypted_data: encryptedData
            },
            header: {
              'content-type': 'application/x-www-form-urlencoded' // 默认值
            },
            success(res) {
              if (res.data.error_code == 0) {
                wx.setStorageSync('token', res.data.data.token)
                wx.setStorageSync('auth', res.data.data.auth)
                wx.setStorageSync('userPhone', res.data.data.mobile)
                wx.setStorageSync('userPhoto', res.data.data.photo)
                wx.setStorageSync('userName', res.data.data.name)
                wx.setStorageSync('car', res.data.data.car)
                wx.setStorageSync('last_car', res.data.data.last_car)
                wx.setStorageSync('admin', res.data.data.is_admin)
                wx.setStorageSync('address', res.data.data.address)
                wx.setStorageSync('occupation', res.data.data.occupation)
                wx.setStorageSync('user_info', res.data.data.user_info)
                setTimeout(function () {
                  wx.redirectTo({
                    url: '../index/index'
                  })
                }, 500)
              } else if (res.data.error_code == 211) {
                wx.showToast({
                  title: '授权失败，请重新尝试',
                  icon: 'none',
                  duration: 2000
                })
              } else {
                wx.showToast({
                  title: res.data.error_msg,
                  icon: 'none',
                  duration: 2000
                })
              }
            }
          })
        }
      })
    } else {
      wx.showToast({
        title: '获取信息失败',
        icon: 'none',
        duration: 2000
      })
    }
  },
  toIndex: function () {
    wx.redirectTo({
      url: '../index/index'
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {},

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },


})