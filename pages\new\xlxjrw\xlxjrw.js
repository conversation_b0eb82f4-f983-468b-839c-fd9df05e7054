// pages/new/xlxjrw/xlxjrw.js
const http = require("../utils/httpUtils.js");
Page({

  /**
   * 页面的初始数据
   */
  data: {
    "patrol_name": "", //任务名称
    "police_station": "", //派出所
    "start_time": "", //开始时间
    "end_time": "", //结束时间
    'itemList': ['嘉北派出所', '塘汇派出所', '城南派出所', '长水派出所', '城西派出所', '巡特警大队'],
  },
  bindKeyInput: function (e) {
    // 获取输入框内容
    console.log(e.detail.value, '输入内容');
    // this.setData({
    //   inputValue: e.detail.value
    // })
  },
  scrollToBottom() {
    console.log('确认创建按钮');
    if (!this.data.patrol_name) {

      wx.showToast({
        title: '请输入任务名称',
        icon: 'none',
        duration: 5000
      })
      return
    }
    if (!this.data.police_station) {

      wx.showToast({
        title: '请选择所在部门',
        icon: 'none',
        duration: 5000
      })
      return
    }
    if (!this.data.start_time) {

      wx.showToast({
        title: '请选择开始时间',
        icon: 'none',
        duration: 5000
      })
      return
    }
    if (!this.data.end_time) {
      wx.showToast({
        title: '请选择结束时间',
        icon: 'none',
        duration: 5000
      })
      return
    }
    http.jsonpost('company/addPatrolRecord', {
      "patrol_name": this.data.patrol_name, //任务名称
      "police_station": this.data.police_station, //派出所
      "start_time": this.data.start_time, //开始时间
      "end_time": this.data.end_time, //结束时间
    }, res => {
      if (res.data.success == 1) {
        wx.navigateBack({ //返回上一页  
          delta: 1
        })
        let pages = getCurrentPages(); //获取小程序页面栈
        let beforePage = pages[pages.length - 1]; //获取上个页面的实例对象
        beforePage.getPatrolRecordList(); //触发上个页面自定义的shuaxin方法

      }
    })

  },
  patrol_nameClick(e) {
    var that = this
    that.data.patrol_name = e.detail.value
    that.setData({
      patrol_name: e.detail.value
    })
  },

  actionSheetTap() {
    var that = this
    wx.showActionSheet({
      itemList: that.data.itemList,
      success(res) {
        console.log(res.tapIndex)
        console.log(that.data.itemList[res.tapIndex])
        that.data.police_station = that.data.itemList[res.tapIndex]
        console.log(that.data)
        that.setData({
          police_station: that.data.itemList[res.tapIndex]
        })
      },
    })
  },
  //时间选择
  bindDateChange(e) {
    console.log(e, '时间选择');
    this.data.start_time = e.detail.value
    this.setData({
      start_time: e.detail.value,
    })
  },
  endDateChange(e) {
    console.log(e, '时间选择');
    this.data.end_time = e.detail.value
    this.setData({
      end_time: e.detail.value,
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})