/* pages/new/xlmap/xlmap.wxss */
/* pages/map/index.wxss */
/* pages/map/map.wxss */
.map_container {
  height: 60%;
  width: 100%;
}
 
.map {
  height: 100%;
  width: 100%;
}
.list-guide{
  display: flex;  
  flex-direction: row; 
  justify-content:space-around;
  border-top: 1px solid #ededed;
  height: 80rpx;
}
.list-guide-imgae{
  height: 70rpx;
  width: 70rpx;
  margin-right: 20px;
  vertical-align: middle;
}
.list-guide-text{
  vertical-align: middle;
  line-height: 90rpx;
  font-size: 35rpx;
}
/* tips 部分*/
.tips{
  left: 10%;
  width: 80%;
  background-color: #fff;
  border-radius: 15rpx;
  position: absolute;
  top: 50rpx;
  padding:25rpx;
  display: flex;
  justify-content: space-between;
  box-shadow:0px 0px 10rpx #8E9397;
}

/* 停车场*/
.title {
  width: 100%;
  height: 104rpx;
  background: white;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
 
.title-sel {
  background-color: #B1B1B1;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx 30rpx;
  border-radius: 7px;
  font-size: 24rpx;
  margin-right: 25rpx;
}
 
.title-sel  .line-style{
  background: #fff;
  height: 6rpx;
  width: 40rpx;
  position: relative;
  margin-top: 10rpx;
}
 
.title-sel-selected{
  background-color: #0C84B6;
  padding: 10rpx 30rpx;
  border-radius: 7px;
  color: #fff;
  font-size: 26rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 25rpx;
}
.title-sel-selected .line-style{
  background: #006bff;
  height: 6rpx;
  width: 90rpx;
  position: relative;
  margin-top: 10rpx;
}
 
.swiper {
  width: 100%;
  height:100%;
  overflow: scroll;
}
.mapparkingbg{
  width: 100%;
  position: absolute;
  height: 100%;
  z-index: -5;
}
.green{
  color:#71BD51;
}
.yellow{
  color:#F7BA0A ;
}
.red{
  color: #EF5742;
}
.tabbox{
  background-attachment: #B1B1B1;
  color: #fff;
  border-radius: 10px;
  padding: 0 20rpx;
}
.contentbox{
  padding: 20rpx 10rpx;
  width: 100%;
  height: 100%;
  font-size:26rpx;
}
.content-box{
  display: flex;
  justify-content:space-between;
}
.content{
  background-color: #fff;
  border-radius: 10px;
  padding: 20rpx 10rpx;
  margin-bottom: 20rpx;
}
.dingwei{
  width: 50rpx;
  height: 50rpx;
}
.parkingname{
  font-size: 40rpx;
}
.ku{
  color: #B0B0B0;
  font-size: 24rpx;
  padding-top: 15rpx;
}
.km{
  color: #B0B0B0;
  font-size: 24rpx;
}
.carmap{
  width: 66rpx;
  height: 30rpx;
}
.lable{
  color: #4CC6A1;
  font-size: 26rpx;
}
.carnumber{
  font-size: 26rpx;
  text-align: center;
}
.bottom-box{
  margin-top: 40rpx;
 display: flex;
 justify-content: space-between;
}

.view-row-show{
  /* margin: upx; */
  bottom: 0;
  z-index: 200;
  /* border-radius : 25rpx; */
  position: absolute;
  /* left: 70rpx; */
  font-size: 26rpx;
  width: 100%;
  color: #000000;
  height: 35%;
  background-color: #ffffff;
  padding:20rpx 10rpx;
}
.view-detail-title{
  display: flex;
  flex-direction: column;
}
.distanceFlex{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.view-detail-content{
  display: flex;
  justify-content: space-between;
}
.pklName{
  font-size: 36rpx;
}
.pklType{
  border: 1px solid #000;
  text-align: center;
  font-size: 28rpx;
}
.carLoft-box{
  margin-top: 40rpx;
  display: flex;
  justify-content: space-between;
  background-color: #ededed;
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
}
.view-rule-box{
  margin: 20rpx 0;
  padding: 20rpx 0;
  border-top: 1px solid #ededed;
}
.btn_cha{
  width: 40px;
  height: 20px;
  color: #fff;
  font-size: 24rpx;
  border-radius: 5px;
  text-align: center;
  background: #0C84B6;
}