const http = require("../new/utils/httpUtils.js");
var user = require('../../pages/new/utils/user.js')
var app = getApp();
var url = app.globalData.requestUrl;
Page({
  data: {
    police_status: 0,
    sbhc_status: 0,
    wgyr_status: 0,
    xfrw_status: 0,
    wycj_status: 0,
    is_fkgzz_state: 0,
    op_status: 0,
    userInfo: null,
    indicatorDots: true,
    vertical: false,
    showPrivacy: false,
    autoplay: true,
    circular: true,
    interval: 3000,
    duration: 500,
    previousMargin: 0,
    nextMargin: 0,
    SwiperHeight: '',
    toDoNum: 0,
    toDoNumwdqy: 0,
    winWid: '',
    noticeList: [],
    imgs: [],
    topImgs: [],
    modalHidden: true,
    search_param: '',
    buttonTop: 0,
    buttonLeft: 0,
    buttonRight: 0,
    windowHeight: '',
    windowWidth: '',
    startPoint: '',
    endPoint: '',
    ldWorkToken: '',
    ldrkToken: '',
    isForeign: '',
    // ------------------------------
    show0: true,
    show1: false,
    show2: false,
    show3: false,
    contentheight: "1700rpx",
    company_id: 1,
    company_name: "经开公安分局",
    police_type_id: 1,
    police_type_name: "反恐",
    newOptions: [{
        text: '工作站',
        active: true,
        bg: "",
      },
      {
        text: '团队管理',
        active: false,
        bg: "",
      },
      {
        text: '网约房管理',
        active: false,
        bg: "",
      }
    ],
    options: [{
        text: '工作站',
        active: true,
        bg: "",
      },
      {
        text: '团队管理',
        active: false,
        bg: "",
      },
      {
        text: '网约房管理',
        active: false,
        bg: "",
      }
    ],
    workstation: [{
        url: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/QyDucha.png',
        text: '企业督查'
      },
      {
        url: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/TianJIaDuCha1.png',
        text: '物资督查'
      },
      {
        url: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/GengDuo.png',
        text: '进入工作站'
      },
    ],
    teamgl: [{
      url: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/GengDuo.png',
      text: '进入团队管理'
    }],
    wyfgl: [{
      url: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/MenJing.png',
      text: '门禁审核'
    }],
    url: {
      url1: "https://zzsbui.jxjkga.cn/#/dcgz?company_id=1&name=经开公安分局&police_type_id=1&police_type_name=反恐",
      url2: "https://zzsbui.jxjkga.cn/#/DrugControlTask/ControlTask?company_id=1&police_type_id=1&police_type_name=反恐"
    },
    ssmzStatistics: "https://zzsbapi.jxjkga.cn/storemgmt/base/ssmzStatistics",
    getExamineStatistics: "https://zzsbapi.jxjkga.cn/storemgmt/company/getExamineStatistics",
    getCompanyCount: "https://zzsbapi.jxjkga.cn/storemgmt/company/getCompanyCountByPoliceStation",
    total_ssmz_num: 0, //少数民族总数
    ssmz_list: [], //少数民族列表
    total_company_num: 0, //重点行业总数
    examine_list: [],
    total_company_count: 0, //物资单位总数
    company_list: [],
    // https://zzsbapi.jxjkga.cn/storemgmt/company/getNoSubmitOnlineRoomCompanyList?page_size=10&page_num=1&day=7
    //七天待清查企业
  },
  //跳转到首页
  gotoIndex:function(e){
    wx.switchTab({
      url: '/pages/new/homeNew/homeNew',
    })
    // console.log("模式切换");
  },

  getInputValue(e) {
    this.setData({
      search_param: e.detail.value
    })
    console.log("search_param",this.data.search_param);
  },

  //搜索跳转
  toPage4: function (e) {
    var search_param = this.data.search_param;
    if (search_param.trim().length < 2) {
      wx.showToast({
        title: '请至少输入两个字！',
        icon: 'none',
        duration: 2000
      })
      return
    }
    let url = e.currentTarget.dataset.type == 'h5' ? '../myWebview/myWebview?h5=' + encodeURIComponent(e.currentTarget.dataset.url + '?search_param=' + search_param.trim()) + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor;
    wx.navigateTo({
      url: url,
    })
  },

  selectSet() {
    // console.log("show3_1",this.__data__.show3 );
    this.setData({
      show0: false,
      show1: false,
      show2: false,
      show3: true,
      contentheight: "800rpx"
    })
    // console.log("show3_2",this.__data__.show3 );
  },
  selectOption(event) {
    // console.log(this.setData)
    console.log(this.__data__.options)
    console.log(event)
    // var _this=this;
    this.__data__.options.forEach((option, i) => {
      let index = event.target.dataset.index;
      option.active = (i === index);

      switch (this.__data__.options[index].text) {
        case "工作站":
          this.__data__.show0 = true;
          this.__data__.show1 = false;
          this.__data__.show2 = false;
          break;
        case "团队管理":
          this.__data__.show0 = false;
          this.__data__.show1 = true;
          this.__data__.show2 = false;
          break;
        case "网约房管理":
          this.__data__.show0 = false;
          this.__data__.show1 = false;
          this.__data__.show2 = true;
          break;
      }
    })
    for (let i = 0; i < this.__data__.options.length; i++) {
      if (this.__data__.options[i].active === true) {
        this.setData({
          [`options[${i}].bg`]: "white"
        })
      } else {
        this.setData({
          [`options[${i}].bg`]: "#F3F9FF"
        })
      }
    }

    this.setData({
      show0: this.__data__.show0,
      show1: this.__data__.show1,
      show2: this.__data__.show2,
      show3: false,
    })
  },
  submitFun() {
    // let optionsValue = Array.from(this.__data__.newOptions)
    this.setData({
      options: this.__data__.newOptions
    })
  },
  toUp(event) {

    let index = event.target.dataset.index;
    console.log(index);
    if (index > 0) {
      let spliceNum = this.__data__.newOptions.splice(index, 1, this.__data__.newOptions[index - 1])[0]
      console.log(spliceNum);
      this.__data__.newOptions.splice(index - 1, 1, spliceNum);
      console.log(this.__data__.newOptions);
    }
    this.setData({
      newOptions: this.__data__.newOptions
    })

  },
  getLdrkToken() {
    let self = this;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/base/getLdrkToken',
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          let ldrkToken = res.data.data
          // console.log(ldWorkToken)
          self.setData({
            ldrkToken: ldrkToken
          })
        } else {

        }
      },
      fail(res) {
        console.log(res)
      }
    })

  },
  getLdWorkToken() {
    let self = this;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/base/getLdWorkToken',
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          let ldWorkToken = res.data.data
          // console.log(ldWorkToken)
          self.setData({
            ldWorkToken: ldWorkToken
          })
        } else {

        }
      },
      fail(res) {
        console.log(res)
      }
    })

  },
  /**跳转页面 */
  toPage: function (e) {
    let self = this;
    if (!e.currentTarget.dataset.url) {
      return
    }
    let url = e.currentTarget.dataset.type == 'h5' ? '../myWebview/myWebview?h5=' + e.currentTarget.dataset.url + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor;
    console.log(url)
    if (e.currentTarget.dataset.type == 'alert') {
      wx.showToast({
        title: e.currentTarget.dataset.url,
        icon: 'none',
        duration: 2000
      })
    } else {
      if (self.data.userInfo.isLogin) {
        if (self.data.userInfo.isAuth) {
          wx.navigateTo({
            url: url,
          })
        } else {
          wx.navigateTo({
            url: '/pages/new/userCheck/userCheck'
          })
        }
      } else {
        wx.showModal({
          title: '提示',
          content: '请先注册并登录',
          success(res) {
            if (res.confirm) {
              wx.redirectTo({
                url: '/pages/new/user/login'
              })
            } else if (res.cancel) {
              wx.showToast({
                title: '登录失败',
                icon: 'none',
                duration: 2000
              })
            }
          }
        })
      }
    }
  },
  toPage11: function (e) {
    let self = this;
    if (!e.currentTarget.dataset.url) {
      return
    }
    let url = e.currentTarget.dataset.type == 'h5' ? '../myWebview/myWebview?h5=' + e.currentTarget.dataset.url + '&company_id=' + e.currentTarget.dataset.company_id + '&name=' + e.currentTarget.dataset.name + '&police_type_name=' + e.currentTarget.dataset.police_type_name + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor;
    console.log(url)
    if (e.currentTarget.dataset.type == 'alert') {
      wx.showToast({
        title: e.currentTarget.dataset.url,
        icon: 'none',
        duration: 2000
      })
    } else {
      if (self.data.userInfo.isLogin) {
        if (self.data.userInfo.isAuth) {
          wx.navigateTo({
            url: url,
          })
        } else {
          wx.navigateTo({
            url: '/pages/new/userCheck/userCheck'
          })
        }
      } else {
        wx.showModal({
          title: '提示',
          content: '请先注册并登录',
          success(res) {
            if (res.confirm) {
              wx.redirectTo({
                url: '/pages/new/user/login'
              })
            } else if (res.cancel) {
              wx.showToast({
                title: '登录失败',
                icon: 'none',
                duration: 2000
              })
            }
          }
        })
      }
    }
  },
  toPage2: function (e) {
    if (this.data.wgyr_status == 1) {
      this.toPage(e);
    } else {
      wx.showToast({
        title: '您没有操作权限，请联系负责的外国人联络员！',
        icon: 'none',
        duration: 2000
      })
    }
  },
  toPageworksp(e) {
    if (this.data.police_status == 1) {
      this.toPage(e);
    } else {
      wx.showToast({
        title: '您没有操作权限！',
        icon: 'none',
        duration: 2000
      })
    }
  },
  toPageworksp11(e) {
    if (this.data.police_status == 1) {
      this.toPage11(e);
    } else {
      wx.showToast({
        title: '您没有操作权限！',
        icon: 'none',
        duration: 2000
      })
    }
  },

  /**验证是否人证核实 */
  doAuth: function () {
    user.doAuth();
  },
  doLogin: function () {
    var that = this;
    user.getUserInfo().then((res) => {
      that.setData({
        userInfo: getApp().globalData.userInfo
      });
      if (user.checkPermissionByFuncName("house-check")) {
        that.setData({
          op_status: 1
        });
      } else {
        that.setData({
          op_status: 0
        });
      }
      if (user.checkPermissionByFuncName("foreigner-manage")) {
        that.setData({
          wgyr_status: 1
        });
      } else {
        that.setData({
          wgyr_status: 0
        });
      }
      if (user.checkPermissionByFuncName("person-check")) {
        that.setData({
          sbhc_status: 1
        });
      } else {
        that.setData({
          sbhc_status: 0
        });
      }
      if (user.checkPermissionByFuncName("police-check")) {
        that.setData({
          police_status: 1
        });
      } else {
        that.setData({
          police_status: 0
        });
      }

      if (user.checkPermissionByFuncName("epidemic-free")) {
        that.setData({
          wycj_status: 1
        });
      } else {
        that.setData({
          wycj_status: 0
        });
      }
      if (user.get_is_fkgzz_state()) {
        that.setData({
          fkgzz_status: 1
        });
      } else {
        that.setData({
          fkgzz_status: 0
        });
      }
      if (user.checkPermissionByFuncName("publish-building-task")) {
        that.setData({
          xfrw_status: 1
        });
      } else {
        that.setData({
          xfrw_status: 0
        });
      }

    });
    console.log(this.data);
  },
  bindHouseSelectRole: function () {
    var that = this;
    wx.showToast({
      title: '请选择角色',
      icon: 'none',
      duration: 2000
    })
    wx.showActionSheet({
      itemList: ['我是房东', '我是租客', '自住'],
      success(res) {
        if (that.data.userInfo.isLogin) {
          if (that.data.userInfo.isAuth) {
            if (res.tapIndex == 0) {
              wx.navigateTo({
                url: '/pages/new/house/declareFangDong/declareFangDong/declareFangDong?type=1',
              })
            } else if (res.tapIndex == 2) {
              wx.navigateTo({
                url: '/pages/new/house/declareFangDong/declareFangDong/declareFangDong?type=2',
              })
            } else {
              wx.navigateTo({
                url: '/pages/new/house/declareZuKe/searchHouse/searchHouse',
              })
            }
          } else {
            that.doAuth();
          }
        } else {
          that.doLogin();
        }
      },
      fail(res) {
        console.log(res.errMsg)
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    that.getLdWorkToken()
    that.getLdrkToken()
    user.getUserInfo(false).then((res) => {
      this.setData({
        userInfo: getApp().globalData.userInfo
      });
      wx.request({
        method: "POST",
        url: url + "/storemgmt/base/getMyHomePage",
        header: {
          token: wx.getStorageSync('token')
        },
        success: (res) => {
          if (res.data.success == 1 && res.data.data && res.data.data.url) {
            console.log(res.data.data.url)
            wx.navigateTo({
              url: "../myWebview/myWebview?h5=" + res.data.data.url + `&utoken=${wx.getStorageSync('token')}`
            })
          }

        }
      })

      if (user.checkPermissionByFuncName("house-check")) {
        that.setData({
          op_status: 1
        });
      } else {
        that.setData({
          op_status: 0
        });
      }
      if (user.checkPermissionByFuncName("foreigner-manage")) {
        that.setData({
          wgyr_status: 1
        });
      } else {
        that.setData({
          wgyr_status: 0
        });
      }
      if (user.checkPermissionByFuncName("publish-building-task")) {
        that.setData({
          xfrw_status: 1
        });
      } else {
        that.setData({
          xfrw_status: 0
        });
      }

      if (user.checkPermissionByFuncName("person-check")) {
        that.setData({
          sbhc_status: 1
        });
      } else {
        that.setData({
          sbhc_status: 0
        });
      }

      if (user.checkPermissionByFuncName("epidemic-free")) {
        that.setData({
          wycj_status: 1
        });
      } else {
        that.setData({
          wycj_status: 0
        });
      }
      if (user.get_is_fkgzz_state()) {
        that.setData({
          fkgzz_status: 1
        });
      } else {
        that.setData({
          fkgzz_status: 0
        });
      }
      if (user.checkPermissionByFuncName("police-check")) {
        that.setData({
          police_status: 1
        });
      } else {
        that.setData({
          police_status: 0
        });
      }
    });
    // 用户隐私信息认证代码
    // if (wx.getStorageSync("SixTip") == "") {
    //   this.alertSix();
    //   console.log("SixTip用戶沒有登錄");
    // }else{
    //   wx.redirectTo({
    //     url: '/pages/new/homeNew/homeNew'
    //   })
    //   console.log("SixTip用户已登录");
    // }
    
    wx.getPrivacySetting({
      success: res => {
        console.log(res) // 返回结果为: res = { needAuthorization: true/false, privacyContractName: '《xxx隐私保护指引》' }
        if (res.needAuthorization) {
          // 需要弹出隐私协议
          this.alertSix();
        }
      },
      fail: () => {},
      complete: () => {}
    }),

    // 获取购物车控件适配参数
    wx.getSystemInfo({
      success: function (res) {
        console.log(res);
        // 屏幕宽度、高度
        console.log('height=' + res.windowHeight);
        console.log('width=' + res.windowWidth);
        // 高度,宽度 单位为px
        that.setData({
          windowHeight: res.windowHeight,
          windowWidth: res.windowWidth,
          buttonTop: res.windowHeight * 0.8, //这里定义按钮的初始位置
          buttonLeft: res.windowWidth * 0.73, //这里定义按钮的初始位置
        })
      }
    })

    //请求统计接口
    wx.request({
      url: this.data.ssmzStatistics,
      header: {
        'content-type': 'application/json' // 默认值
      },
      success(res) {
        console.log("data", res.data.data)
        that.setData({
          ssmz_list: res.data.data.result_list,
          total_ssmz_num: res.data.data.total_ssmz_num
        })
        console.log("ssmz_list", that.data.ssmz_list)
      }
    })
    wx.request({
      url: this.data.getExamineStatistics,
      header: {
        'content-type': 'application/json' // 默认值
      },
      success(res) {
        console.log("data", res.data.data)
        that.setData({
          examine_list: res.data.data.result_list,
          total_company_num: res.data.data.total_company_num
        })
        console.log("total_company_num", that.data.total_company_num)
      }
    })
    wx.request({
      url: this.data.getCompanyCount,
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        console.log("data3", res.data.data)
        that.setData({
          company_list: res.data.data.result_list,
          total_company_count: res.data.data.total_company_count
        })
        // console.log("total_company_num",that.data.total_company_num)
      }
    })
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

    var that = this;
    // this.getimgs();
    // this.getDbCount();
    // if(wx.getStorageSync('token') ) {
    //   this.getDbCountWdyq();
    //   this.getnotice();
    // }

    if (user.checkPermissionByFuncName("foreigner-manage")) {
      that.setData({
        wgyr_status: 1
      });
    } else {
      that.setData({
        wgyr_status: 0
      });
    }
    if (user.checkPermissionByFuncName("publish-building-task")) {
      that.setData({
        xfrw_status: 1
      });
    } else {
      that.setData({
        xfrw_status: 0
      });
    }
    if (user.checkPermissionByFuncName("police-check")) {
      that.setData({
        police_status: 1
      });
    } else {
      that.setData({
        police_status: 0
      });
    }
    if (user.checkPermissionByFuncName("epidemic-free")) {
      that.setData({
        wycj_status: 1
      });
    } else {
      that.setData({
        wycj_status: 0
      });
    }

    if (user.get_is_fkgzz_state()) {
      that.setData({
        fkgzz_status: 1
      });
    } else {
      that.setData({
        fkgzz_status: 0
      });
    }
    if (user.checkPermissionByFuncName("person-check")) {
      that.setData({
        sbhc_status: 1
      });
    } else {
      that.setData({
        sbhc_status: 0
      });
    }
  },
})