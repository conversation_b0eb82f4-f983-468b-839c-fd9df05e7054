// pages/meetingQrcode/meetingQrcode.js
import QRCode from '../../utils/weapp-qrcode.js'
const W = wx.getSystemInfoSync().windowWidth;
const rate = 750.0 / W;
const qrcode_w = W * 0.35;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    setInter: '',
    qrcode_w: qrcode_w,
    name: wx.getStorageSync('userName'),
    img: wx.getStorageSync('userPhoto'),
    token: wx.getStorageSync('token'),
    vid: ''
  },
  getQrcode:function(){
    let self = this;
    let timestamp = Date.parse(new Date()) / 1000;
    console.log(self.data.token, timestamp, self.data.vid)
    let qrUrl = 'https://jkrms.jsycloud.com/twosessions/?token=' + self.data.token + '&time=' + timestamp + '&vid=' + self.data.vid
    let qrcode = new QRCode('canvas', {
      // usingIn: this,
      text: qrUrl,
      image: '/images/bg.jpg',
      padding: 12,
      width: qrcode_w,
      height: qrcode_w,
      colorDark: "#000",
      colorLight: "white",
      correctLevel: QRCode.CorrectLevel.H,
      callback: (res) => {
        // 生成二维码的临时文件
        console.log(res)
      }
    });
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let self = this;
    self.setData({
      vid: options.vid
    },()=>{
      self.getQrcode();
    })
    self.startSetInter();
  },
  startSetInter: function () {
    var self = this;
    //将计时器赋值给setInter
    self.data.setInter = setInterval(function () {
      self.getQrcode();
    }, 30000)
  },
  endSetInter: function () {
    var self = this;
    //清除计时器  即清除setInter
    clearInterval(self.data.setInter)
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    let self = this;
    //清除计时器  即清除setInter
    clearInterval(self.data.setInter)
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

 
})