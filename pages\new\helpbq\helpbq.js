// pages/new/helpbq/helpbq.js
const http = require("../utils/httpUtils.js");
var user = require('../../../pages/new/utils/user.js')
var app = getApp();
var url = app.globalData.requestUrl;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    winWid: '',
    SwiperHeight:'',
    userInfo: null,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  /**跳转页面 */
  toPage: function (e) {
    let self = this;
    
    if (!e.currentTarget.dataset.url) {
      return
    }
    let url = e.currentTarget.dataset.type == 'h5' ? '../../myWebview/myWebview?h5=' + e.currentTarget.dataset.url + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor;
    if (e.currentTarget.dataset.type == 'alert') {
      wx.showToast({
        title: e.currentTarget.dataset.url,
        icon: 'none',
        duration: 2000
      })
    } else {

      if (self.data.userInfo.isLogin) {
        if (self.data.userInfo.isAuth) {
          wx.navigateTo({
            url: url,
          })
        } else {
          // self.doAuth();
          wx.navigateTo({
            url: '/pages/new/userCheck/userCheck'
          })
        }
      } else {
        wx.showModal({
          title: '提示',
          content: '请先注册并登录',
          success(res) {
            if (res.confirm) {
              wx.redirectTo({
                url: '/pages/new/user/login'
              })
            } else if (res.cancel) {
              wx.showToast({
                title: '登录失败',
                icon: 'none',
                duration: 2000
              })
            }
          }
        })
      }
    }
  },
  /**验证是否人证核实 */
  doAuth: function () {
    user.doAuth();
  },
  toFlMiniProgram(){
    wx.navigateToMiniProgram({
      appId: 'wx1533b3098e52492a',
      path: '/pages/qa/main',
      extraData: {
        foo: 'bar'
      },
      envVersion: 'release',
      success(res) {
        // 打开成功
  }
    })
  },
  toZxMiniProgram(){
    wx.navigateToMiniProgram({
      appId: 'wx182be45f90ffbf3d',
      path: '/pages/webwiew/index',
      extraData: {
        foo: 'bar'
      },
      envVersion: 'release',
      success(res) {
        // 打开成功
  }
    })
  },
  bindSwiperHeight: function (e) {
    var that = this;
    var winWid = wx.getSystemInfoSync().windowWidth; //获取当前屏幕的宽度
    var imgh = e.detail.height; //图片高度
    var imgw = e.detail.width;
    var swiperH = winWid * imgh / imgw + "px"
    //等比设置swiper的高度。  即 屏幕宽度 / swiper高度 = 图片宽度 / 图片高度    ==》swiper高度 = 屏幕宽度 * 图片高度 / 图片宽度
    that.setData({
      winWid: winWid,
      SwiperHeight: swiperH
    })
  },
  onLoad: function (options) {
    var that = this;
    user.getUserInfo(false).then((res) => {
      this.setData({
        userInfo: getApp().globalData.userInfo
      });
      wx.request({
        method:"POST",
        url:url+"/storemgmt/base/getMyHomePage",
        header: {
          token: wx.getStorageSync('token')
        },
        success:(res)=>{
          if(res.data.success==1 && res.data.data && res.data.data.url){
            console.log(res.data.data.url)
            wx.navigateTo({
              url: "../../myWebview/myWebview?h5="+res.data.data.url+`&utoken=${wx.getStorageSync('token')}`
            })
          }
          
        }
      })
      
      if (user.checkPermissionByFuncName("house-check")) {
        that.setData({
          op_status: 1
        });
      } else {
        that.setData({
          op_status: 0
        });
      }
      if (user.checkPermissionByFuncName("foreigner-manage")) {
        that.setData({
          wgyr_status: 1
        });
      } else {
        that.setData({
          wgyr_status: 0
        });
      }
      if (user.checkPermissionByFuncName("publish-building-task")) {
        that.setData({
          xfrw_status: 1
        });
      } else {
        that.setData({
          xfrw_status: 0
        });
      }
  
      if (user.checkPermissionByFuncName("person-check")) {
        that.setData({
          sbhc_status: 1
        });
      } else {
        that.setData({
          sbhc_status: 0
        });
      }
    });
    
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})