// pages/new/house/viewHouseInfo/viewHouseInfo.js
var common = require('../../utils/common.js');
var http = require('../../utils/httpUtils.js');
var user = require('../../utils/user.js')
var app = getApp();
var url = app.globalData.requestUrl;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    swiper: {
      indicatorDots: true,
      vertical: false,
      autoplay: true,
      circular: true,
      interval: 3000,
      duration: 500,
      previousMargin: 0,
      nextMargin: 0,
      height: 'auto',
      image: []
    },
    apply: {},
    house: {
      id: 0,
      uuid: '',
      area_location_id: 0,
      area_location_name: '请选择',
      address: '',
      house_type_name: '',
      is_rent: 0,
      remark: '',
      owner_id: 0,
      owner_photo: '',
      owner_realname: '',
      owner_mobilephone_number: '',
      status: -2
    },
    op_status: -3,
    members: [],
    families: [],
    checks: [],
    checkData: {
      status: -1,
      content: ''
    }
  },
  changeIndicatorDots: function(e) {
    this.setData({
      indicatorDots: !this.data.indicatorDots
    })
  },
  changeAutoplay: function(e) {
    this.setData({
      autoplay: !this.data.autoplay
    })
  },
  intervalChange: function(e) {
    this.setData({
      interval: e.detail.value
    })
  },
  durationChange: function(e) {
    this.setData({
      duration: e.detail.value
    })
  },
  bindSwiperHeight: function(e) {
    var that = this;
    var winWid = wx.getSystemInfoSync().windowWidth; //获取当前屏幕的宽度
    var imgh = e.detail.height;　　　　　　　　　　　　　　　　 //图片高度
    var imgw = e.detail.width;
    var swiperH = winWid * imgh / imgw + "px";
    //等比设置swiper的高度。  即 屏幕宽度 / swiper高度 = 图片宽度 / 图片高度    ==》swiper高度 = 屏幕宽度 * 图片高度 / 图片宽度
    var swiper = that.data.swiper;
    swiper.height = swiperH;
    that.setData({
      swiper: swiper
    })
  },
  bindCheck: function(e) {
    var that = this;
    var status = e.currentTarget.dataset.status;
    var checkData = that.data.checkData;
    checkData.status = status;
    that.setData({
      checkData: checkData
    })
    var uuid = that.options.uuid;
    if (status == -2) {
      wx.showToast({
        title: '该功能暂未开放',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    if (status == 0) {
      if (that.data.checkData.content == '') {
        wx.showToast({
          title: '请输入拒绝理由',
          icon: 'none',
          duration: 2000
        });
        return;
      }
    }
    var that = this;
    console.log(that.data);
    wx.request({
      method: 'POST',
      url: url + '/storemgmt/house/apply/verify',
      data: {
        uuid: that.data.apply.uuid,
        status: status,
        content: that.data.checkData.content
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          wx.switchTab({
            url: '/pages/new/declareRecord/declareRecord/declareRecord',
          })
        } else {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      }
    })
  },
  setCheckContent: function(e) {
    var that = this;
    var checkData = that.data.checkData;
    checkData.content = e.detail.value;
    that.setData({
      checkData: checkData
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    var that = this;
    var data = {
      house: that.data.house,
      members: that.data.members,
      families: that.data.families,
      swiper: that.data.swiper,
      apply: that.data.apply
    };
    var house = that.data.house;
    var families = that.data.families;
    var members = that.data.members;
    var swiper = that.data.swiper;
    var apply = that.data.apply;
    var op_status = that.data.op_status;
    var checks = that.data.checks;
    //applyid = options.applyid;



    wx.request({
      method: 'GET',
      url: url + '/storemgmt/house/detail?uuid=' + options.uuid,
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        token: wx.getStorageSync('token')
      },
      success(res) {
        
        house = res.data.data;
        user.getUserInfo().then((res) => {
          var userInfo = getApp().globalData.userInfo;
          if (house.status == -2 && userInfo.id == house.owner_id) {
            //本人可以提交
            op_status = -2;
          } else if (house.status == -1) {
            //管理员可以审核
            
            if(user.checkPermission(house.building_id,"house-check")){
              op_status = -1;
            }
          } else {
            //禁止操作
            op_status = -3;
          }
          data.op_status = op_status;
          that.setData(data);
        });
        if (house.owner_photo == null || house.owner_photo == '') {
          house.owner_photo = '/images/avatar_null.png';
        } else {
          house.owner_photo = url + house.owner_photo;
        }
        data.house = house;
        //租客
        wx.request({
          method: 'GET',
          url: url + '/storemgmt/house/member/detail?type=1&house_uuid=' + options.uuid,
          header: {
            'content-type': 'application/x-www-form-urlencoded',
            token: wx.getStorageSync('token')
          },
          success(res) {
            if (res.data.success == 1) {
              for (let i = 0; i < res.data.data.length; i++) {
                if (res.data.data[i].member_photo == null || res.data.data[i].member_photo == '') {
                  res.data.data[i].member_photo = '/images/avatar_null.png';
                } else {
                  res.data.data[i].member_photo = url + res.data.data[i].member_photo;
                }

                if( res.data.data[i].type == 1){
                  members.push(res.data.data[i]);
                }else if(res.data.data[i].type == 3){
                  families.push(res.data.data[i]);
                }
               
              }
            }
            data.house = house;
            data.members = members;
            data.families = families;
            data.apply = apply;
            data.op_status = op_status;
            that.setData(data);
            console.log(that.data);
          }
        })

        //审核记录
        wx.request({
          method: 'GET',
          url: url + '/storemgmt/house/applyCheck/list?table=house&table_uuid=' + options.uuid,
          header: {
            'content-type': 'application/x-www-form-urlencoded',
            token: wx.getStorageSync('token')
          },
          success(res) {
            if (res.data.success == 1 && res.data.data.length > 0) {
              apply.uuid = res.data.data[0].parent_uuid;
              for (let i = 0; i < res.data.data.length; i++) {
                if (res.data.data[i].status == -2) {
                  checks.push({
                    icon: 'wait',
                    colorClass: 'qwui-status-1',
                    step_name: res.data.data[i].step_name,
                    time: '',
                    username: '等待提交',
                    content: ''
                  });
                } else if (res.data.data[i].status == -1) {
                  checks.push({
                    icon: 'wait',
                    colorClass: 'qwui-status-1',
                    step_name: res.data.data[i].step_name,
                    time: '',
                    username: '等待审核',
                    content: ''
                  });
                } else if (res.data.data[i].status == 1) {
                  checks.push({
                    icon: 'success',
                    colorClass: 'qwui-status1',
                    step_name: res.data.data[i].step_name,
                    time: res.data.data[i].intime.replace('T', ' ').replace('.000+0000', ''),
                    username: res.data.data[i].handler_user_name + ' : ',
                    content: res.data.data[i].content
                  });
                } else if (res.data.data[i].status == 0) {
                  checks.push({
                    icon: 'fail',
                    colorClass: 'qwui-status0',
                    step_name: res.data.data[i].step_name,
                    time: res.data.data[i].intime.replace('T', ' ').replace('.000+0000', ''),
                    username: res.data.data[i].handler_user_name + ' : ',
                    content: res.data.data[i].content
                  });
                }
              }
            }
            data.apply = apply;
            data.checks = checks;
            that.setData(data);
            console.log(that.data);
          }
        })
        //图片
        wx.request({
          method: 'GET',
          url: url + '/storemgmt/file/list?page_num=1&page_size=0&table=house&table_uuid=' + options.uuid,
          header: {
            'content-type': 'application/x-www-form-urlencoded',
            token: wx.getStorageSync('token')
          },
          success(res) {
            console.log(res);
            if (res.data.data.data_list.length > 0) {
              for (let i = 0; i < res.data.data.data_list.length; i++)
                swiper.image.push(url + res.data.data.data_list[i].file_path);
            }
            that.setData({
              swiper: swiper
            });
            console.log(that.data.swiper);
          }
        })
        // data.member = member;
        // that.setData(data);
        // console.log(that.data);
      }
    })
  },
  bindInvite: function(e) {
    wx.navigateTo({
      url: '/pages/new/house/inviteJoin/inviteJoin?type=fdyq&phone=',
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

 
})