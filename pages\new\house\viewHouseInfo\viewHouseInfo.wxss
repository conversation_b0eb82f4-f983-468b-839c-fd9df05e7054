/* pages/new/house/viewHouseInfo/viewHouseInfo.wxss */

.qwui-timeline {
  margin-bottom: 100rpx;
}

.check-btn-box {
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 99999;
}

.check-btn-line {
  height: 100rpx;
}

.check-btn-txt {
  width: 20%;
  float: left;
  text-align: center;
  line-height: 100rpx;
  height: 100rpx;
  color: #333;
  background-color: #ddd;
}

.check-btn-status1 {
  width: 40%;
  float: left;
  text-align: center;
  line-height: 100rpx;
  height: 100rpx;
  color: #fff;
  background-color: #11d111;
}
.check-btn-status-2 {
  width: 40%;
  float: left;
  text-align: center;
  line-height: 100rpx;
  height: 100rpx;
  color: #fff;
  background-color: #f60;
}
.check-btn-status0 {
  width: 40%;
  float: left;
  text-align: center;
  line-height: 100rpx;
  height: 100rpx;
  color: #fff;
  background-color: #e30000;
}

.check-btn-input {
  height: 100rpx;
  line-height: 100rpx;
  background-color: #ddd;
}

.check-btn-input input {
  height: 100rpx;
  line-height: 100rpx;
  background-color: #fff;
  width: 60%;
  margin: 0 auto;
  padding-left: 10%;
  padding-right: 10%;
}
