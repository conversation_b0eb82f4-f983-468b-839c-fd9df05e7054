// pages/new/xldkdt/xldkdt.js
const http = require("../new/utils/httpUtils.js");
Page({

  /**
   * 页面的初始数据
   */
  data: {
    // date:new Date().toJSON().substring(0, 10) + ' ' + new Date().toTimeString().substring(0,8),
    date:new Date().toTimeString().substring(0,8),
    clearTime:'',
    latitude:30.74744,
    longitude:120.78483,
    show_dk:true,
    scale: 15,
    markers:[],
    marker:{},
    PatrolMembers:[],
    patrol_record_id:'',
    patrol_record_detail_id:'',
    map:{
      mapObj:{},
      latitude:null,
      longitude:null,
      scale: 15,
      markers:[],
      marker:{},
    },
  },
  dk_Click(){
    this.startPatrol()

  },

   bindNormal(){
    this.setData({
      viewShow:false
    })
  },
  bindmarkertap(e){
    var that = this;
    console.log(e)
    let marker = this.data.markers.find(item => {return item.id == e.detail.markerId});
    this.setData({
      mapDetail:marker,
      viewShow:true
    })
  },
  collageFun(){
    let _this = this;    
    _this.data.clearTime = setInterval(()=>{
        _this.getLocation();
    },1000*60) //1分钟请求接口一次
},
getLocation() {
  const that = this
  wx.getLocation({
    success(res) {
      that.data.longitude=res.longitude,
      that.data.latitude=res.latitude
      that.reportPatrolLocInfo()
    }
  })

},
// .接收巡逻定位上报
// 地址：host+company/reportPatrolLocInfo
reportPatrolLocInfo(){
  let that = this
  http.get('company/reportPatrolLocInfo', {
    patrol_record_detail_id:this.data.patrol_record_detail_id,
    longitude:this.data.longitude,
    latitude:this.data.latitude
  }, res => {
    if (res.data.success == 1) {
       wx.showToast({
        title: '获取到当前定位',
        icon: 'none',
        duration: 5000
      })
    }
  })
},

// .队员巡逻任务签到
// 地址：host+company/startPatrol
startPatrol(){
  let that = this
  http.get('company/startPatrol', {
    patrol_record_id:this.data.patrol_record_id,
  }, res => {
    if (res.data.success == 1) {
      this.data.patrol_record_detail_id =res.data.data.id
     console.log(res.data.data);
     that.getLocation();
     that.collageFun()
     this.setData({
      show_dk:false
     })
    }else{
      wx.showToast({
        title: '不在任务期间',
        icon: 'none',
        duration: 5000
      })
      this.setData({
        show_dk: true 
      })
    }
  })
},
// 队员巡逻任务签退
// 地址：host+company/completePatrol
dK_new(){
console.log(this.data.show_dk);

  if (!this.data.show_dk) {
    this.completePatrol()
  }else{
    this.dk_Click()
  }
},


completePatrol(){
  let that = this
  http.get('company/completePatrol', {
    patrol_record_detail_id:this.data.patrol_record_detail_id
  }, res => {
    if (res.data.success == 1) {
      this.setData({
        show_dk:true
       })
       clearInterval(that.data.clearTime) 
      //  this.getLocation()
       wx.showToast({
        title: '签退成功',
        icon: 'none',
        duration: 5000
      })
    }
  })
},

  // 获取巡逻人员
// 地址：host+company/getPatrolMembers
getPatrolMembers(){
  let that = this
  http.get('company/getPatrolMembers', {
    patrol_record_id:this.data.patrol_record_id
  }, res => {
    if (res.data.success == 1) {
      that.setData({
        PatrolMembers:res.data.data 
      })
    }
  })
},


// 队员获取任务签到情况
// 地址：host+company/getPatrolRecordDetail
getPatrolRecordDetail(){
  let that = this
  http.get('company/getPatrolRecordDetail', {
    patrol_record_id:this.data.patrol_record_id
  }, res => {
    if (res.data.success == 1) {
      let is_data= res.data.data
      if (is_data == null) {
        this.setData({
          show_dk: true 
        })
      }else{
        this.setData({
          show_dk: false 
        })
        this.data.patrol_record_detail_id = is_data.id
        that.getLocation();
        that.collageFun()
      }
   
     console.log(res.data.data);
    }
  })
},

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.data.patrol_record_id = options.patrol_record_id
    console.log(options,'patrol_record_id');
    this.getPatrolRecordDetail() 
    this.getPatrolMembers()
    let _this = this;    
    _this.data.clearTime = setInterval(()=>{
      _this.setData({
        date:new Date().toTimeString().substring(0,8),
      }) 
    },1000) //1分钟请求接口一次
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
   
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    let that = this
    clearInterval(that.data.clearTime) 
    let pages = getCurrentPages(); //获取小程序页面栈
    let beforePage = pages[pages.length - 2]; //获取上个页面的实例对象
    beforePage.getPatrolRecordList(); //触发上个页面自定义的shuaxin方法
    beforePage.getUserOnlinePatrol(); //触发上个页面自定义的shuaxin方法
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})