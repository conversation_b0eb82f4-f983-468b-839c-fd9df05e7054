<view class="container">
  <watermark></watermark>
  <view class="top">
    <view class="msg-setting">
      <view class="msg" bindtap="toPage" data-url="{{msg_link.url}}" data-type="{{msg_link.type}}" data-bgcolor="{{msg_link.bgcolor}}" data-fontcolor="{{msg_link.fontcolor}}" data-title="{{msg_link.title}}">
        <image src="../../images/dianpu.png" mode="widthFix"></image>
        <text wx:if="{{msg_num!=0}}" class="msg-num">{{msg_num}}</text>
      </view>
      <view class="setting" bindtap="toPage" data-url="{{setting_link.url}}" data-type="{{setting_link.type}}" data-bgcolor="{{setting_link.bgcolor}}" data-fontcolor="{{setting_link.fontcolor}}" data-title="{{setting_link.title}}">
        <image src="../../images/setting.png" mode="widthFix"></image>
      </view>
    </view>
    <view class="weather-address">
      <view class="address">
        <text>{{address}}</text>
      </view>
      <view class="weather">
        <text>{{weather}}</text>
        <text>{{degree}}℃</text>
        <text class="bg">{{air}}</text>
      </view>
    </view>
    <view class="top-img">
      <image src="../../images/building.png" mode="widthFix"></image>
    </view>
  </view>
  <view class="person-info" bindtap="toMine">
    <view class="avatar-name">
      <view class="avatar">
        <open-data type="userAvatarUrl"></open-data>
      </view>
      <view class="name">
        <open-data type="userNickName"></open-data>
      </view>
    </view>
    <view class="status">
      {{user_status == 0?'请登录':(user_status == -1?'未认证':'')}}
      <view class='arrow' wx:if="{{user_status == 1}}"></view>
    </view>
  </view>
  <!-- <view class='notice-wrap' hidden='{{hideNotice}}'>
    <view class='notice ovh font28 relative'>
      <view class="marquee_text" style="left:{{marqueeDistance}}px;">
        {{notice}}
      </view>
    </view>
    <icon class="close-icon" type="cancel" size="20" color="black" bindtap="closeNotice" />
  </view> -->
  <view class="link-list">
    <view class="link-item" wx:for="{{link_list}}" wx:key="key" bindtap="toPage" data-url="{{item.url}}" data-type="{{item.type}}" data-bgcolor="{{item.bgcolor}}" data-fontcolor="{{item.fontcolor}}" data-title="{{item.title}}">
      <view class="img">
        <image src="../../images/dianpu2.png" mode="widthFix"></image>
      </view>
      <view class="text">
        <view class="title">{{item.title}}</view>
        <view class="sub-title">{{item.subTitle}}</view>
      </view>
      <view class='arrow'></view>
    </view>
  </view>
</view>











<!-- <view class="container">
  <view class="banner">
    <image src="../../images/index-bg.png" mode="widthFix"></image>
  </view>
  <view class="bar"></view>
  <view class="plus">
    <icon bindtap="toRent" type="clear" size="40" color="white"/>
  </view>
  <view class="environment">
    <view class="weather-block">
      <text class="degree">{{degree}}</text>
      <text class="degree-icon">℃</text>
      <text class="weather">{{weather}}</text>
    </view>
    <view class="address">
      <text>{{address}}</text>
    </view>
    <view class="air-wet">
      <text>室外空气 {{air}} </text>
      <text>/ 环境湿度{{wet}}%</text>
    </view>
  </view>
  <view wx:if="{{tenantArr.length>0}}" class="tenant-content">
    <view class="tenant-list" wx:for="{{tenantArr}}" wx:key="key">
      <view class="tenant-block" bindtap="toPwd" data-id="{{item.house.id}}">
        <view class="address">
          <text>{{item.house.name}}</text><text>{{item.house.info}}</text>
        </view>
        <view class="tenant">
          <text wx:if="{{item.status.code != '1'}}">房屋状态：{{item.status.text}}</text>
        </view>
      </view>
    </view>
  </view>
  <view wx:else class="tenant-content">
    <view class="tenant-list">
      <view class="tenant-block">
        <view class="address">
          <text>您暂无租房</text>
        </view>
      </view>
    </view>
  </view>
</view> -->