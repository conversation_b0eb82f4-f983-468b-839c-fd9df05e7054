  
const http = require("../../utils/httpUtils.js"); 
var user = require('../../utils/user.js');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    phone:"" ,
    type:""
   
  },
  bindPhone: function (e) {
    var newphone = e.detail.value;
    this.setData({
      phone: newphone
    });
  },
  bindInvite:function(e){ 
    if(user.checkPhone(this.data.phone) ){
      var userInfo = getApp().globalData.userInfo;
      let messageInfo = ""
      if(this.data.type == "fdyq"){
        messageInfo = "房东["+userInfo.realname+"]邀请您注册个人信息,请进入嘉兴公安自主申报小程序注册"
      }else if(this.data.type == "zkyq"){
        messageInfo = "租客["+userInfo.realname+"]邀请您注册房东房屋信息,请进入嘉兴公安自主申报小程序注册"
      }

      http.jsonpost("base/message/send",{
      "phone": this.data.phone,
      "content": messageInfo,
      "type": 2
      },res=>{
        if(res.data.success == 1){
          wx.showToast({
            title: '邀请短信已发送至'+this.data.phone + "...",
            icon: 'none',
            duration: 2000
          })
        }
      })
    }else{
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none',
        duration: 2000
      })
    }
  

  },
      
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) { 
    this.setData({
      type:options.type,
      phone:options.phone
    }
    )
    
  },
  getAreaList: function (data) {
    if (!data.children) return [{
      name: '',
      code: data.code
    }]; // 有可能某些县级市没有区
    return this.areaFormat(data.children);
  },
  areaFormat: function (data) {
    var result = [];
    for (var i = 0; i < data.length; i++) {
      var d = data[i];
      if (/^请选择|全部/.test(d.name)) continue;
      result.push(d);
    }
    if (result.length) return result;
    return [];
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

 
})