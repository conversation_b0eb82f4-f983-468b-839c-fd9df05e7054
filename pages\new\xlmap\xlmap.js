// pages/new/xlmap/xlmap.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    event:null,
    currentIndex: 0,
    addmissage: '选的位置',
    // markers	 Array	标记点
    mapDetail:{
      distance:'',
      pklName:'',
      latitude:'',
      longitude:'',
      pkl_in_out:'',
      pklRuleDetail:''
    },
    companyList:{},
    circleParkList:{},
    roadParkList:[],
    viewShow:false,
    mapObj:{},
    latitude:30.76,
    longitude:120.71,
    // latitude:null,
    // longitude:null,
    scale: 15,
    markers:[],
    marker:{},
    map:{
      mapObj:{},
      latitude:null,
      longitude:null,
      scale: 15,
      markers:[],
      marker:{},
    },
    //controls控件 是左下角圆圈小图标,用户无论放大多少,点这里可以立刻回到当前定位(控件（更新一下,即将废弃，建议使用 cover-view 代替）)
    controls: [{
      id: 1,
      iconPath: '../../images/img/controls.png',
      position: {
        left: 15,
        top: 260 - 50,
        width: 40,
        height: 40
      },
      clickable: true
    }],
    distanceArr: []
  },
  loadData(la1,lo1){
    let that = this
    // console.log(la1)
    wx.request({
      url: 'https://zzsbapi.jxjkga.cn/storemgmt/company/getAllManageLedCompanyList',
      header: {
        token: wx.getStorageSync('token'),
      },
      data: {
        page_num: 1,
        page_size: 999,
      },
      method: 'get',
      success: (res) => {
      let dataListall =[] 
      let tempMarkers =[]
      dataListall=res.data.data.data_list
    
        that.setData({
          companyList:dataListall,
        })
        dataListall.forEach((item, index) => {
          // console.log(item.pklId)
          let marker={
            "markerId":item.id,
            "id":item.id,
            "latitude":item.longitude,
            "longitude":item.latitude,
            "width": '60rpx',
            "height": '60rpx',
            "companyName":item.company_name,
            "address":item.address,
            "iconPath":"https://zzsbapi.jxjkga.cn/wxzzsb/new/dingwei.png",
            "callout":{},
            "img_url":item.img_url,
//             "pklRuleDetail":item.pklRuleDetail,
            "label": {
               content: '1',
               fontSize: 16,
              color:'transparent',
               anchorX: -5,
               anchorY: -25
            }
          }
          
          tempMarkers.push(marker);
         
        });
        
        this.setData({
          markers:tempMarkers
        },()=>{
          // console.log(this.data.markers)
        })
     
       
      }
    })
  },
  bindNormal(){
    this.setData({
      viewShow:false
    })
  },
  bindmarkertap(e){
    var that = this;
    console.log(e)
    let marker = this.data.markers.find(item => {return item.id == e.detail.markerId});
    this.setData({
      mapDetail:marker,
      viewShow:true
    })
  },
  distanceData(la1, lo1, la2, lo2) {
        var La1 = la1 * Math.PI / 180.0
        var La2 = la2 * Math.PI / 180.0
        var La3 = La1 - La2
        var Lb3 = lo1 * Math.PI / 180.0 - lo2 * Math.PI / 180.0
        var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(La3 / 2), 2) + Math.cos(La1) * Math.cos(La2) * Math.pow(Math.sin(Lb3 / 2), 2)))
        s = s * 6378.137*1000;//地球半径
        s = (parseInt(Math.round(s * 10000) / 10000)/1000).toFixed(2)
        return s;
  },
  distance(la1, lo1, la2, lo2) {
        var La1 = la1 * Math.PI / 180.0
        var La2 = la2 * Math.PI / 180.0
        var La3 = La1 - La2
        var Lb3 = lo1 * Math.PI / 180.0 - lo2 * Math.PI / 180.0
        var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(La3 / 2), 2) + Math.cos(La1) * Math.cos(La2) * Math.pow(Math.sin(Lb3 / 2), 2)))
        s = s * 6378.137*1000;//地球半径
        s = (parseInt(Math.round(s * 10000) / 10000)/1000).toFixed(2)
        if(s<1){
          s = s*1000 + '米'
        }else{
          s = s + '公里'
        }
        return s;
  },
  getMapDetail:function(e){
    
    var id=e.currentTarget.dataset.id;
    var name=e.currentTarget.dataset.name;
    var address =e.currentTarget.dataset.address
    wx.navigateTo({
      url: '../xldetail/xldetail?id='+id+'&name='+name+'&address='+address
    })
  },
  onGuideTap: function (event) {
    console.log(event)
    var lat = Number(event.currentTarget.dataset.latitude);
    var lon = Number(event.currentTarget.dataset.longitude);
    var pklName = event.currentTarget.dataset.pklName;
    console.log(lat);
    console.log(lon);
    wx.openLocation({
      type: 'gcj02',
      latitude: lat,
      longitude: lon,
      name: pklName,
      scale: 28
    })
  },
  //用户点击tab时调用
  titleClick: function (e) {
    let currentPageIndex =
      this.setData({
        //拿到当前索引并动态改变
        currentIndex: e.currentTarget.dataset.idx
      })
  },
  //swiper切换时会调用
  pagechange: function (e) {
    if ("touch" === e.detail.source) {
      let currentPageIndex = this.data.currentIndex
      currentPageIndex = (currentPageIndex + 1) % 3
      this.setData({
        currentIndex: currentPageIndex
      })
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this
    //获取当前的地理位置、速度
    wx.getLocation({
      type: 'gcj02', // 默认为 wgs84 返回 gps 坐标，gcj02 返回可用于 wx.openLocation 的坐标
      success: function (res) {
        //赋值经纬度
        that.setData({
          latitude: res.latitude,
          longitude: res.longitude,
        })
        console.log(res.latitude)
        that.loadData(res.latitude,res.longitude)  
      }
    }) 
    // console.log(this.data.latitude)
    // console.log(this.data.longitude)
    that.loadData(this.data.latitude,this.data.longitude)  
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})