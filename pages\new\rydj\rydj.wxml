<!--pages/new/zkdj/zkdj.wxml-->
<!--pages/building/visit.wxml-->
<view class="container">
  <watermark></watermark>
  <!-- <view class="title_list" bind:tap="ckldClick">查看流动人口登记>></view> -->
  <view class="title">个人信息</view>
  <view>
    <form catchsubmit="formSubmit">
      <view><span class="red">*</span>本人头像：(请拍摄肩部以上的人象照片)</view>
      <view class='load-img'>
        <view class='load-box'>
          <view class='img-item' wx:for="{{face_file}}" wx:key="index">
            <image src="{{item.path}}" data-src="{{item}}" mode="aspectFill" data-list="{{face_file}}"></image>
            <icon class='icon' type="clear" size="20" color='#EF4444' catchtap='_onDelFace' data-idx="{{index}}" wx:if="{{!prevent}}" />
          </view>
          <image class='img-add' bindtap='getface' wx:if="{{!face_file.length}}"></image>
        </view>
      </view>

      <view><span class="red">*</span>姓名：</view>
      <view>
        <input name="user_name" value="{{user_name}}" bindinput="getname" class="ipt" placeholder="请输入姓名" />
      </view>
      <view><span class="red">*</span>证件号码：</view>
      <view>
        <input name="user_identity" value="{{user_identity}}" bindblur="getidentity" class="ipt" type="idcard" placeholder="请填写身份证号" />
      </view>
      <view><span class="red">*</span>手机号码：</view>
      <view>
        <input name="user_phone" value="{{user_phone}}" bindblur="getphone" class="ipt" type="number" placeholder="请输入11位手机号" />
      </view>
      <view>
      <!-- <span class="red">*</span>行业名称：</view>
      <view>
        <picker mode="selector" bindchange="companyChange" range="{{companyList}}" value="{{companyIndex}}">
          <input class="ipt" style="pointer-events:none;" name="industry_name" value="{{industry_name}}"  disabled="true" placeholder="请选择行业类型" />
        </picker>
      </view> -->

      <span class="red">*</span>社区名称：</view>
      <view>
        <picker mode="selector" bindchange="communityChange" range="{{communityList}}" value="{{communityIndex}}" range-key="name">
          <input class="ipt" style="pointer-events:none;" name="industry_name" value="{{communityList[communityIndex].name}}"  disabled="true" placeholder="请选择社区" />
        </picker>
      </view>

      <view><span class="red">*</span>详细地址：（例：某某小区2幢501室）</view>
      <view>
        <textarea name="address_detail" value="{{address_detail}}" bindblur="getaddress_detail" class="ipt texrar" 	auto-height="true"  placeholder="请输详细地址" />
      </view>

      <view>
        <span class="red">*</span>入住时间：</view>
      <view>
        <picker mode="date" bindchange="live_dateChange"  value="{{live_date}}" range-key="name">
          <input class="ipt" style="pointer-events:none;" name="live_date" value="{{live_date}}"  disabled="true" placeholder="请选择时间" />
        </picker>
      </view>
      <view>
        <span class="red">*</span>离开时间：</view>
      <view>
        <picker mode="date" bindchange="leave_dateChange"  value="{{leave_date}}" range-key="name">
          <input class="ipt" style="pointer-events:none;" name="leave_date" value="{{leave_date}}"  disabled="true" placeholder="请选择时间" />
        </picker>
      </view>

      <!-- <view><span class="red">*</span>场所名称：</view>
      <view>
        <input name="company_name" value="{{company_name}}" bindblur="getcompany" class="ipt"  placeholder="请输入场所名称" />
      </view> -->
      <view class="btn-area" >
        <button style="margin: 30rpx 0;background:#0081ff;color:#fff;" formType="submit" disabled="{{disabled}}">提交信息</button>
      </view>
      <br/> 
      <br/> 
    </form>
  </view>
</view>