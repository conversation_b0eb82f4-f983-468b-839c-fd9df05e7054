// pages/new/manage/view.js
var app = getApp();
var url = app.globalData.requestUrl;
var user = require('../../../pages/new/utils/user.js')
var common = require('../../../pages/new/utils/common.js')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    user: {},
    url: url,
    dataShopShow: false,
    dataHouseShow: false,
    roleShow: false,
    functionShow: false,
    dataShopTree: [],
    dataShopList: [],
    dataHouseTree: [],
    dataHouseList: [],
    roleTree: [],
    roleList: [],
    functionTree: [],
    functionList: []
  },
  getUserInfo: function(e) {
    var that = this;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/back/user/detail/byId?id=' + that.options.id,
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          var data = {
            user: that.data.user
          }
          data.user = res.data.data;
          that.setData(data);
          that.getRoleList();
          that.getFunctionList();
          that.getDataHouseList();
          that.getDataShopList();
        } else {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },
  removeByValue: function(arr, val) {
    for (var i = 0; i < arr.length; i++) {
      if (arr[i].id == val.id) {
        arr.splice(i, 1);
        break;
      }
    }
  },
  bindDataHouseShow: function(e) {
    var temp = false;
    if (e.currentTarget.dataset.status == "1") {
      temp = true;
    }
    this.setData({
      dataHouseShow: temp
    });
  },
  bindDataShopShow: function(e) {
    var temp = false;
    if (e.currentTarget.dataset.status == "1") {
      temp = true;
    }
    this.setData({
      dataShopShow: temp
    });
  },
  bindFunctionShow: function(e) {
    var temp = false;
    if (e.currentTarget.dataset.status == "1") {
      temp = true;
    }
    this.setData({
      functionShow: temp
    });
  },
  bindRoleShow: function(e) {
    var temp = false;
    if (e.currentTarget.dataset.status == "1") {
      temp = true;
    }
    this.setData({
      roleShow: temp
    });
  },
  bindFunctionCancel: function(e) {
    this.bindSetFunction(e.currentTarget.dataset.item, 0);
  },
  bindRoleCancel: function(e) {
    this.bindSetRole(e.currentTarget.dataset.item, 0);
  },
  bindDataHouseCancel: function(e) {
    this.bindSetDataHouse(e.currentTarget.dataset.item, 0);
  },
  bindFunctionClick: function(e) {
    this.bindSetFunction(e.currentTarget.dataset.item, -1);
  },
  bindRoleClick: function(e) {
    this.bindSetRole(e.currentTarget.dataset.item, -1);
  },
  bindDataHouseClick: function(e) {
    console.log('click')
    this.bindSetDataHouse(e.currentTarget.dataset.item, -1);
  },
  bindSetFunction: function(item, checked) {
    if (item.code == '') {
      return;
    }
    var functionList = this.data.functionList;
    var functionTree = this.data.functionTree;
    for (let i = 0; i < functionList.length; i++) {
      if (functionList[i].id == item.id) {
        if (checked == -1) {
          if (functionList[i].checked == false) {
            functionList[i].checked = true;
          } else {
            functionList[i].checked = false;
          }
        } else if (checked == 1) {
          functionList[i].checked = true;
        } else if (checked == 0) {
          functionList[i].checked = false;
        } else {}
        break;
      }
    }
    functionTree = common.toTree(functionList);
    this.setData({
      functionList: functionList,
      functionTree: functionTree
    });
  },
  bindSetRole: function(item, checked) {
    if (item.code == '') {
      return;
    }
    var roleList = this.data.roleList;
    var roleTree = this.data.roleTree;
    for (let i = 0; i < roleList.length; i++) {
      if (roleList[i].id == item.id) {
        if (checked == -1) {
          if (roleList[i].checked == false) {
            roleList[i].checked = true;
          } else {
            roleList[i].checked = false;
          }
        } else if (checked == 1) {
          roleList[i].checked = true;
        } else if (checked == 0) {
          roleList[i].checked = false;
        } else {}
        break;
      }
    }
    roleTree = common.toTree(roleList);
    this.setData({
      roleList: roleList,
      roleTree: roleTree
    });
  },
  bindSetDataHouse: function(item, checked) {
    var dataHouseList = this.data.dataHouseList;
    var dataHouseTree = this.data.dataHouseTree;
    for (let i = 0; i < dataHouseList.length; i++) {
      if (dataHouseList[i].id == item.id) {
        if (checked == -1) {
          if (dataHouseList[i].checked == false) {
            dataHouseList[i].checked = true;
          } else {
            dataHouseList[i].checked = false;
          }
        } else if (checked == 1) {
          dataHouseList[i].checked = true;
        } else if (checked == 0) {
          dataHouseList[i].checked = false;
        } else {}
        break;
      }
    }
    dataHouseTree = common.toTree(dataHouseList);
    this.setData({
      dataHouseList: dataHouseList,
      dataHouseTree: dataHouseTree
    });
  },
  getRoleList: function(e) {
    var that = this;
    var currentUserRoles = that.data.user.roles == null ? [] : that.data.user.roles.split(',');
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/back/role/list',
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          var roleList = that.data.roleList;
          var roleTree = that.data.roleTree;
          for (let i = 0; i < res.data.data.length; i++) {
            var code = '';
            if (res.data.data[i].code != null && res.data.data[i].code != '') {
              code = res.data.data[i].code;
            }
            console.log(code)
            console.log(currentUserRoles.indexOf(code))
            let checked = false;
            if (currentUserRoles.indexOf(code) != -1) {
              checked = true;
            }
            roleList.push({
              id: res.data.data[i].id,
              parent_id: res.data.data[i].parent_id,
              name: res.data.data[i].name,
              code: code,
              status: res.data.data[i].status,
              checked: checked
            });
          }
          roleTree = common.toTree(roleList);
          that.setData({
            roleList: roleList,
            roleTree: roleTree
          });
        } else {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },
  getFunctionList: function(e) {
    var that = this;
    var currentUserFunctions = that.data.user.functions == null ? [] : that.data.user.functions.split(',');
    console.log(currentUserFunctions);
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/back/function/list',
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          var functionList = that.data.functionList;
          var functionTree = that.data.functionTree;
          for (let i = 0; i < res.data.data.length; i++) {
            
            let code = '';
            if (res.data.data[i].code != null && res.data.data[i].code != '') {
              code = res.data.data[i].code;
            }
            console.log(code)
            console.log(currentUserFunctions.indexOf(code))
            let checked = false;
            if (currentUserFunctions.indexOf(code) != -1) {
              checked = true;
            }
            functionList.push({
              id: res.data.data[i].id,
              parent_id: res.data.data[i].parent_id,
              name: res.data.data[i].name,
              code: code,
              status: res.data.data[i].status,
              checked: checked
            });
          }
          functionTree = common.toTree(functionList);
          that.setData({
            functionList: functionList,
            functionTree: functionTree
          });
        } else {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },
  getDataHouseList: function(e) {
    var that = this;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/base/area/list?parent_id=330499',
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          var dataHouseList = that.data.dataHouseList;
          var dataHouseTree = that.data.dataHouseTree;
          // dataHouseList.push({
          //   id: 330499,
          //   parent_id: 3304,
          //   name: '经开',
          //   checked: false
          // });
          // for (let i = 0; i < res.data.data.length; i++) {
          //   dataHouseList.push({
          //     id: res.data.data[i].id,
          //     parent_id: res.data.data[i].parent_id,
          //     name: res.data.data[i].name,
          //     checked: false
          //   });
          // }
          dataHouseList = [{
            "id": "330499",
            "name": "经开区",
            "parent_id": "330400",
            "checked": false
          }, {
            "id": "330499001",
            "name": "城南街道",
            "parent_id": "330499",
            "checked": false
          }, {
            "id": "330499002",
            "name": "长水街道",
            "parent_id": "330499",
            "checked": false
          }, {
            "id": "330499003",
            "name": "嘉北街道",
            "parent_id": "330499",
            "checked": false
          }, {
            "id": "330499004",
            "name": "塘汇街道",
            "parent_id": "330499",
            "checked": false
          }, {
            "id": "330499005",
            "name": "商交园[城西所]",
            "parent_id": "330499",
            "checked": false
          }, {
            "id": "330499001001",
            "name": "百妙社区",
            "parent_id": "330499001",
            "checked": false
          }, {
            "id": "330499001002",
            "name": "金穗社区",
            "parent_id": "330499001",
            "checked": false
          }, {
            "id": "330499001003",
            "name": "良秀社区",
            "parent_id": "330499001",
            "checked": false
          }, {
            "id": "330499001004",
            "name": "禾源社区",
            "parent_id": "330499001",
            "checked": false
          }, {
            "id": "330499001005",
            "name": "长新社区",
            "parent_id": "330499001",
            "checked": false
          }, {
            "id": "330499001006",
            "name": "八字桥村",
            "parent_id": "330499001",
            "checked": false
          }, {
            "id": "330499002001",
            "name": "府南社区",
            "parent_id": "330499002",
            "checked": false
          }, {
            "id": "330499002002",
            "name": "中南社区",
            "parent_id": "330499002",
            "checked": false
          }, {
            "id": "330499002003",
            "name": "石堰社区",
            "parent_id": "330499002",
            "checked": false
          }, {
            "id": "330499002004",
            "name": "长水社区",
            "parent_id": "330499002",
            "checked": false
          }, {
            "id": "330499002005",
            "name": "由拳社区",
            "parent_id": "330499002",
            "checked": false
          }, {
            "id": "330499002006",
            "name": "联星社区",
            "parent_id": "330499002",
            "checked": false
          }, {
            "id": "330499002007",
            "name": "庆丰社区",
            "parent_id": "330499002",
            "checked": false
          }, {
            "id": "330499002008",
            "name": "槜李社区",
            "parent_id": "330499002",
            "checked": false
          }, {
            "id": "330499003005",
            "name": "常秀社区",
            "parent_id": "330499003",
            "checked": false
          }, {
            "id": "330499003006",
            "name": "嘉州美都社区",
            "parent_id": "330499003",
            "checked": false
          }, {
            "id": "330499003007",
            "name": "阳光社区",
            "parent_id": "330499003",
            "checked": false
          }, {
            "id": "330499003008",
            "name": "金都社区",
            "parent_id": "330499003",
            "checked": false
          }, {
            "id": "330499003010",
            "name": "阳海社区",
            "parent_id": "330499003",
            "checked": false
          }, {
            "id": "330499003011",
            "name": "昌盛社区",
            "parent_id": "330499003",
            "checked": false
          }, {
            "id": "330499003012",
            "name": "友谊社区",
            "parent_id": "330499003",
            "checked": false
          }, {
            "id": "330499003013",
            "name": "振兴社区",
            "parent_id": "330499003",
            "checked": false
          }, {
            "id": "330499004005",
            "name": "华玉社区",
            "parent_id": "330499004",
            "checked": false
          }, {
            "id": "330499004006",
            "name": "永政社区",
            "parent_id": "330499004",
            "checked": false
          }, {
            "id": "330499004007",
            "name": "锦绣社区",
            "parent_id": "330499004",
            "checked": false
          }, {
            "id": "330499004008",
            "name": "新禾家苑社区",
            "parent_id": "330499004",
            "checked": false
          }, {
            "id": "330499004009",
            "name": "茶香坊社区",
            "parent_id": "330499004",
            "checked": false
          }, {
            "id": "330499005001",
            "name": "水果市场",
            "parent_id": "330499005",
            "checked": false
          }, {
            "id": "330499005002",
            "name": "蔬菜批发交易市场",
            "parent_id": "330499005",
            "checked": false
          }, {
            "id": "330499005003",
            "name": "建陶市场",
            "parent_id": "330499005",
            "checked": false
          }];
          dataHouseTree = common.toTree(dataHouseList);
          that.setData({
            dataHouseList: dataHouseList,
            dataHouseTree: dataHouseTree
          });
        } else {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },
  getCommunityList: function(e) {
    var that = this;
    let area_location_id = e.currentTarget.dataset.item.id;
    let level = e.currentTarget.dataset.level;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/base/community/area?area_location_id=' + area_location_id + '&level=' + level,
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          var dataHouseList = that.data.dataHouseList;
          var dataHouseTree = that.data.dataHouseTree;
          // dataHouseList.push({
          //   id: 330499,
          //   parent_id: 3304,
          //   name: '经开',
          //   checked: false
          // });
          for (let i = 0; i < res.data.data.length; i++) {
            if (common.getObjectsFromJson(dataHouseList, 'id', res.data.data[i].id).length > 0) {
              that.removeByValue(dataHouseList, res.data.data[i]);
              console.log(dataHouseList);
              console.log(res.data.data[i].id);
              that.setData({
                dataHouseList: dataHouseList
              });
            } else {
              let parent_id = 0;
              if (level == 1) {
                parent_id = area_location_id;
              } else {
                parent_id = res.data.data[i].parent_id;
              }
              dataHouseList.push({
                id: res.data.data[i].id,
                parent_id: parent_id,
                name: res.data.data[i].name,
                checked: false
              });
            }
          }
          dataHouseTree = common.toTree(dataHouseList);
          that.setData({
            dataHouseList: dataHouseList,
            dataHouseTree: dataHouseTree
          });
        } else {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },
  getDataShopList: function(e) {
    var that = this;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/base/area/list?parent_id=330499',
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          var dataShopList = that.data.dataShopList;
          var dataShopTree = that.data.dataShopTree;
          // dataHouseList.push({
          //   id: 330499,
          //   parent_id: 3304,
          //   name: '经开',
          //   checked: false
          // });
          // for (let i = 0; i < res.data.data.length; i++) {
          //   dataHouseList.push({
          //     id: res.data.data[i].id,
          //     parent_id: res.data.data[i].parent_id,
          //     name: res.data.data[i].name,
          //     checked: false
          //   });
          // }
          dataShopList = [{
            "id": "330499",
            "name": "经开区",
            "parent_id": "330400",
            "checked": false
          }, {
            "id": "330499001",
            "name": "城南街道",
            "parent_id": "330499",
            "checked": false
          }, {
            "id": "330499002",
            "name": "长水街道",
            "parent_id": "330499",
            "checked": false
          }, {
            "id": "330499003",
            "name": "嘉北街道",
            "parent_id": "330499",
            "checked": false
          }, {
            "id": "330499004",
            "name": "塘汇街道",
            "parent_id": "330499",
            "checked": false
          }, {
            "id": "330499005",
            "name": "商交园[城西所]",
            "parent_id": "330499",
            "checked": false
          }, {
            "id": "330499001001",
            "name": "百妙社区",
            "parent_id": "330499001",
            "checked": false
          }, {
            "id": "330499001002",
            "name": "金穗社区",
            "parent_id": "330499001",
            "checked": false
          }, {
            "id": "330499001003",
            "name": "良秀社区",
            "parent_id": "330499001",
            "checked": false
          }, {
            "id": "330499001004",
            "name": "禾源社区",
            "parent_id": "330499001",
            "checked": false
          }, {
            "id": "330499001005",
            "name": "长新社区",
            "parent_id": "330499001",
            "checked": false
          }, {
            "id": "330499001006",
            "name": "八字桥村",
            "parent_id": "330499001",
            "checked": false
          }, {
            "id": "330499002001",
            "name": "府南社区",
            "parent_id": "330499002",
            "checked": false
          }, {
            "id": "330499002002",
            "name": "中南社区",
            "parent_id": "330499002",
            "checked": false
          }, {
            "id": "330499002003",
            "name": "石堰社区",
            "parent_id": "330499002",
            "checked": false
          }, {
            "id": "330499002004",
            "name": "长水社区",
            "parent_id": "330499002",
            "checked": false
          }, {
            "id": "330499002005",
            "name": "由拳社区",
            "parent_id": "330499002",
            "checked": false
          }, {
            "id": "330499002006",
            "name": "联星社区",
            "parent_id": "330499002",
            "checked": false
          }, {
            "id": "330499002007",
            "name": "庆丰社区",
            "parent_id": "330499002",
            "checked": false
          }, {
            "id": "330499002008",
            "name": "槜李社区",
            "parent_id": "330499002",
            "checked": false
          }, {
            "id": "330499003005",
            "name": "常秀社区",
            "parent_id": "330499003",
            "checked": false
          }, {
            "id": "330499003006",
            "name": "嘉州美都社区",
            "parent_id": "330499003",
            "checked": false
          }, {
            "id": "330499003007",
            "name": "阳光社区",
            "parent_id": "330499003",
            "checked": false
          }, {
            "id": "330499003008",
            "name": "金都社区",
            "parent_id": "330499003",
            "checked": false
          }, {
            "id": "330499003010",
            "name": "阳海社区",
            "parent_id": "330499003",
            "checked": false
          }, {
            "id": "330499003011",
            "name": "昌盛社区",
            "parent_id": "330499003",
            "checked": false
          }, {
            "id": "330499003012",
            "name": "友谊社区",
            "parent_id": "330499003",
            "checked": false
          }, {
            "id": "330499003013",
            "name": "振兴社区",
            "parent_id": "330499003",
            "checked": false
          }, {
            "id": "330499004005",
            "name": "华玉社区",
            "parent_id": "330499004",
            "checked": false
          }, {
            "id": "330499004006",
            "name": "永政社区",
            "parent_id": "330499004",
            "checked": false
          }, {
            "id": "330499004007",
            "name": "锦绣社区",
            "parent_id": "330499004",
            "checked": false
          }, {
            "id": "330499004008",
            "name": "新禾家苑社区",
            "parent_id": "330499004",
            "checked": false
          }, {
            "id": "330499004009",
            "name": "茶香坊社区",
            "parent_id": "330499004",
            "checked": false
          }, {
            "id": "330499005001",
            "name": "水果市场",
            "parent_id": "330499005",
            "checked": false
          }, {
            "id": "330499005002",
            "name": "蔬菜批发交易市场",
            "parent_id": "330499005",
            "checked": false
          }, {
            "id": "330499005003",
            "name": "建陶市场",
            "parent_id": "330499005",
            "checked": false
          }];
          dataShopTree = common.toTree(dataShopList);
          that.setData({
            dataShopList: dataShopList,
            dataShopTree: dataShopTree
          });
        } else {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },
  getAreaList: function(e) {
    var that = this;
    let area_location_id = e.currentTarget.dataset.item.id;
    let level = e.currentTarget.dataset.level;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/base/community/area?area_location_id=' + area_location_id + '&level=' + level,
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          var dataHouseList = that.data.dataHouseList;
          var dataHouseTree = that.data.dataHouseTree;
          // dataHouseList.push({
          //   id: 330499,
          //   parent_id: 3304,
          //   name: '经开',
          //   checked: false
          // });
          for (let i = 0; i < res.data.data.length; i++) {
            if (common.getObjectsFromJson(dataHouseList, 'id', res.data.data[i].id).length > 0) {
              that.removeByValue(dataHouseList, res.data.data[i]);
              console.log(dataHouseList);
              console.log(res.data.data[i].id);
              that.setData({
                dataHouseList: dataHouseList
              });
            } else {
              let parent_id = 0;
              if (level == 1) {
                parent_id = area_location_id;
              } else {
                parent_id = res.data.data[i].parent_id;
              }
              dataHouseList.push({
                id: res.data.data[i].id,
                parent_id: parent_id,
                name: res.data.data[i].name,
                checked: false
              });
            }
          }
          dataHouseTree = common.toTree(dataHouseList);
          that.setData({
            dataHouseList: dataHouseList,
            dataHouseTree: dataHouseTree
          });
        } else {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.getUserInfo();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

 
})