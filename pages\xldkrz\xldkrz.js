// pages/new/xldkrz/xldkrz.js
const http = require("../new/utils/httpUtils.js");
Page({

  /**
   * 页面的初始数据
   */
  data: {
    end_time:new Date().toJSON().substring(0, 10),
    start_time :new Date(new Date()-24*60*60*1000).toJSON().substring(0, 10),
    patrol_name: '',
    PatrolCount:{},
    RecordList:[]
  },
  //时间选择
  bindDateChange(e) {
    console.log(e, '时间选择');
    this.data.start_time =  e.detail.value,
    this.setData({
      start_time: e.detail.value,
    })
  },
  bindKeyInput: function (e) {
    // 获取输入框内容
    this.data.patrol_name=e.detail.value
    console.log(e.detail.value, '输入内容', this.data);
    // this.setData({
    //   inputValue: e.detail.value
    // })
  },

  endDateChange(e) {
    console.log(e, '时间选择');
    this.data.end_time =  e.detail.value,
    this.setData({
      end_time: e.detail.value,
    })
  },
  openClick(e) {
    wx.navigateTo({
      url: "/pages/new/zhlxdkhzt/zhxldkhzt?Record="+ JSON.stringify(this.data.RecordList[e.currentTarget.id])
    })
  },

  resetting_getMyPatrolCount(){
    this.setData({
      end_time:'',
      start_time:'',
      patrol_name:'',
    })
    this.data.end_time=''
    this.data.start_time=''
    this.data.patrol_name=''
    this.getMyPatrolCount()
  },
  // 获取我的巡逻统计
// 地址：host+company/getMyPatrolCount
getMyPatrolCount(){
  var date = new Date();

console.log(date);
  var systemInfo = wx.getSystemInfoSync();

  console.log(systemInfo);

  let that = this
  http.get('company/getMyPatrolCount', {
    start_time:this.data.start_time,
    end_time:this.data.end_time,
    patrol_name:this.data.patrol_name,
  }, res => {
    if (res.data.success == 1) {
      that.setData({
        PatrolCount:res.data.data 
      })
    }
  })
  this.getPersonalPatrolRecordList()
},
// 获取个人巡逻日志
// 地址：host+company/getPersonalPatrolRecordList
getPersonalPatrolRecordList(){
  let that = this
  http.get('company/getPersonalPatrolRecordList', {
    start_time:this.data.start_time,
    end_time:this.data.end_time,
    patrol_name:this.data.patrol_name,
    page_num:1,
    page_size:999,
  }, res => {
    if (res.data.success == 1) {
      that.setData({
        RecordList:res.data.data.data_list
      })
    }
  })
},
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getMyPatrolCount()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})