// pages/loginSystem/loginSystem.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    phone: '',
    ssid: ''
  },
  login:function(){
    let self = this;
    var app = getApp();
    var url = app.globalData.requestUrl;
    wx.request({
      method: 'POST',
      url: url + 'admin_login/loginByScan',
      data: {
        app_id: app.globalData.app_id,
        app_key: app.globalData.app_key,
        token: wx.getStorageSync('token'),
        ssid: self.data.ssid
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      success(res) {
        if (res.data.error_code == 0) {
          wx.showToast({
            title: '登录成功',
            icon: 'none',
            duration: 2000,
            success(){
              setTimeout(function(){
                wx.redirectTo({
                  url: '../index/index'
                })
              },2000)
            }
          })
        } else {
          wx.showToast({
            title: res.data.error_msg,
            icon: 'none',
            duration: 2000
          })
        }
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (query) {
    let self = this;
    let mobile = wx.getStorageSync('userPhone').substr(0, 3) + '****' + wx.getStorageSync('userPhone').substr(7, 4);
    let scene = decodeURIComponent(query.scene)
    console.log(scene)
    self.setData({
      phone: mobile,
      ssid: scene
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

 
})