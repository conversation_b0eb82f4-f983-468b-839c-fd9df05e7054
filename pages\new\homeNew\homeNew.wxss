.html {
  background-color: #ffffff;

}

.modeSwitch {
  padding-left: 10px;
  font-size: 20rpx;
  color: white;
}

.modeSwitch image {
  padding-left: 20rpx;
  height: 40rpx;
  width: 40rpx;
}

.top-paper {
  width: 100%;
  position: relative;
}

.top-image {
  width: 100%;
  z-index: -1;
}

.fourButtonBar {
  position: absolute;
  top: 10px;
  width: 100%;
}

.fourButton_1 {
  padding-left: 6.25%;
}

.fourButton_2 {
  padding-left: 12.5%;
}

.fiveButton_1 {
  padding-top: 15px;
  padding-bottom: 15px;
}

.fiveButton_2 {
  padding-top: 15px;
  padding-left: 5.5%;
  margin-left: -5px;
  padding-bottom: 15px;
}

.dianjiButtonBar {
  width: 100%;
  margin: 0px auto;
  text-align: center;
  margin-top: -30px;
}

.mid-paper {
  margin-top: 15px;
}

.mid-paper-bottonBar {
  position: relative;
  width: 100%;
  margin: 0px auto;
  text-align: center;
  margin-top: 12px;

}

.bfmImage {
  position: absolute;
  top: 110%;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  z-index: 99999;
}

.paper_box {
  display: flex;
  justify-content: space-between;
}

.mid-paper-botton {
  margin-left: 3%;
  margin-right: 3%;
}

.swiperPaper {
  width: 100%;
  text-align: center;
  margin-top: 10px;
}

.img-box {
  width: 100%;
  height: 100%;
  position: relative;
}

.tag {
  height: 40rpx;
  width: 40rpx;
  line-height: 40rpx;
  border-radius: 40rpx;
  background-color: red;
  position: absolute;
  /* right: -20rpx; */
  right: 0;
  /* top: -20rpx; */
  top: 0;
  font-size: 25rpx;
  color: #ffffff;
}

.myjb {
  position: absolute;
  top: -10px;
  right: 10px;
  margin: 5px;
  min-width: 26px;
  padding: 0 3px;
  color: #fafafa;
  font-weight: 500;
  font-size: 12px;
  font-family: PingFang SC, Helvetica Neue, Arial, sans-serif;
  line-height: 14px;
  text-align: center;
  background-color: #e63e3e;
  border: 1px solid #e63e3e;
  border-radius: 16px;
}

.mdqy {
  position: absolute;
  top: -10px;
  left: 135px;
  margin: 5px;
  min-width: 26px;
  padding: 0 3px;
  color: #fafafa;
  font-weight: 500;
  font-size: 12px;
  font-family: PingFang SC, Helvetica Neue, Arial, sans-serif;
  line-height: 14px;
  text-align: center;
  background-color: #e63e3e;
  border: 1px solid #e63e3e;
  border-radius: 16px;
}

.myjb1 {
  position: absolute;
  top: -10px;
  right: 20px;
  margin: 5px;
  min-width: 16px;
  padding: 0 3px;
  color: #fafafa;
  font-weight: 500;
  font-size: 12px;
  font-family: PingFang SC, Helvetica Neue, Arial, sans-serif;
  line-height: 14px;
  text-align: center;
  background-color: #e63e3e;
  border: 1px solid #e63e3e;
  border-radius: 16px;
}

.CommonFuncPaper {
  width: 93%;
  background-color: #c7c7c711;
  border-radius: 4px;
  margin: 0px auto;
  text-align: center;
}

.newsPaper {
  width: 93%;
  background-color: #c7c7c711;
  border-radius: 4px;
  margin: 0px auto;
  text-align: center;
  margin: 0 24px;
}

.search {
  width: 80%;
  font-size: 14px;
}

.search2 {
  width: 68%;
  font-size: 14px;
}

.search_arr {
  border: 1px solid #d0d0d0;
  border-radius: 20rpx;
  margin-left: 30rpx;
}

.search_arr2 {
  border: 1px solid #d0d0d0;
  border-radius: 20rpx;
  margin-left: 30rpx;
}

.search_arr input {
  margin-left: 70rpx;
  height: 60rpx;
  border-radius: 5px;
}

.bc_text {
  line-height: 68rpx;
  height: 68rpx;
  margin-top: 34rpx;
}

.sousuo {
  margin-left: 15rpx;
  width: 13%;
  line-height: 190%;
  text-align: center;
  border: 2px solid #e3e4e4;

  border-radius: 20rpx;
  color: #ffffff;
  font-size: 16px;
  height: 56rpx;
  font-weight: 700;
}

.page_row {
  display: flex;
  flex-direction: row;

  padding-top: 15px;
  padding-bottom: 10px;
}

.searchcion {
  margin: 10rpx 10rpx 10rpx 10rpx;
  position: absolute;
  left: 40rpx;
  z-index: 2;
  width: 20px;
  height: 20px;
  text-align: center;
}

.searchcion2 {
  margin: 10rpx 10rpx 10rpx 10rpx;
  position: absolute;
  left: 140rpx;
  z-index: 2;
  width: 20px;
  height: 20px;
  text-align: center;
}

.search_arr {
  background-color: #ffffff;
}

.bottomPaper {
  position: relative;
}

.bottomPaper {
  position: relative;
}

/* .btn_Suspension{
  position: fixed;
  height: 100rpx;
  width: 100rpx;
  border-radius: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999;
}
.Suspension_logo{
  position:absolute;
  height:50%;
  width:50%;
  left:23%;
  top:27%
} */