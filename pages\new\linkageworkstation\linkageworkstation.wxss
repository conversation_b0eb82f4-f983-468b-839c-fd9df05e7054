/* pages/new/linkageworkstation/linkageworkstation.wxss */
.container{
  width: 100%;
}

.page-top {
  /* width: 100%; */
  /* position: relative; */
  /* padding:20px; */
  padding:20px;
  background-color: #fff;
}

.top-image {
  width: 100%;
  z-index: -1;
  
}

.fourButtonBar {
  position: absolute;
  top: 10px;
  width: 100%;
}

.fourButton_1 {
  padding-left: 6.25%;
}

.fourButton_2 {
  padding-left: 12.5%;
}

.fiveButton_1 {
  padding-top: 15px;
  padding-bottom: 15px;
}

.fiveButton_2 {
  padding-top: 15px;
  padding-left: 5.5%;
  margin-left: -5px;
  padding-bottom: 15px;
}

.dianjiButtonBar {
  width: 100%;
  margin: 0px auto;
  text-align: center;
  margin-top: -30px;
}

.mid-paper {
  /* margin-top: 15px; */
  padding: 8px;
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  /* width: 100%; */
}
.title_station{
  background-color: #fff;
  margin-top: 20px;
  padding: 10px 20px;
}
.mid-paper-bottonBar {
  /* position: relative; */
  /* width: 100%; */
  /* margin: 0px auto; */
  /* text-align: center; */
  /* margin-top: 20px; */
  width: 50%;
  display: flex;
  /* justify-content: space-around; */
  align-items: center;
  padding:0 20px;
}

.swiperPaper {
  width: 100%;
  text-align: center;
  margin-top: 10px;
}
.bottomPaper {
  position: relative;
}