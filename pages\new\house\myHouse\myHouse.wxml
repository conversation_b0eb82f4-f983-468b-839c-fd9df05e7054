<!--pages/new/shop/info.wxml-->
<watermark></watermark>
<wxs module="splits" src="./myHouse.wxs"></wxs>
<view class="page">
  <view class="weui-search-bar">
    <view class="weui-search-bar__form">
      <view class="weui-search-bar__box">
        <icon class="weui-icon-search_in-box" type="search" size="14"></icon>
        <input type="text" class="weui-search-bar__input" placeholder="房屋门牌号" value="{{inputVal}}" focus="{{inputShowed}}" bindinput="inputTyping" bindconfirm="getHouseList" />
        <view class="weui-icon-clear" wx:if="{{inputVal.length > 0}}" bindtap="bindSearchClear">
          <icon type="clear" size="14"></icon>
        </view>
      </view>
      <label class="weui-search-bar__label" hidden="{{inputShowed}}" bindtap="showInput">
        <icon class="weui-icon-search" type="search" size="14"></icon>
        <view class="weui-search-bar__text">模糊搜索</view>
      </label>
    </view>
    <view class="weui-search-bar__cancel-btn" hidden="{{!inputShowed}}" bindtap="hideInput">取消</view>
  </view>
  <view class="page__bd">
    <view class="weui-panel weui-panel_access myweui-row">
      <view class="weui-panel__bd myweui-row-body">
        <block wx:for="{{house}}" wx:for-item="item" wx:key="this">
          <navigator url="/pages/new/house/viewHouseInfo/viewHouseInfo?uuid={{item.uuid}}&_p=" class="weui-media-box weui-media-box_appmsg" hover-class="weui-cell_active">
            <view wx:if="{{item.house_type_name=='出租'}}" style="border-color:transparent transparent red;" class="corner-tag">
              <label class="corner-tag-text">{{item.house_type_name}}</label>
            </view>
            <view wx:if="{{item.house_type_name=='自住'}}" style="border-color:transparent transparent blue;" class="corner-tag">
              <label class="corner-tag-text">{{item.house_type_name}}</label>
            </view>
         
            <view class="weui-media-box__hd weui-media-box__hd_in-appmsg">
              <image class="weui-media-box__thumb" src="{{item.img_url}}" />
            </view>
            <view class="weui-media-box__bd weui-media-box__bd_in-appmsg">
              <view class="weui-media-box__title">{{ splits.splitAddress(item)}}
              </view>
              <view class="weui-media-box__desc">
                <label class='product-sku'>{{item.owner_realname}} {{item.owner_mobilephone_number}}</label>
              </view>
              <view class="weui-media-box__info">
                <view wx:if="{{item.status==-2}}" class="weui-media-box__info__meta">
                  <label class="myweui-tag myweui-tag-yellow">待提交</label>
                </view>
                <view wx:if="{{item.status==-1}}" class="weui-media-box__info__meta">
                  <label class="myweui-tag myweui-tag-gray">待审核</label>
                </view>
                <view wx:if="{{item.status==1}}" class="weui-media-box__info__meta">
                  <label class="myweui-tag myweui-tag-green">已通过</label>
                </view>
                <view wx:if="{{item.status==0}}" class="weui-media-box__info__meta">
                  <label class="myweui-tag myweui-tag-red">未通过</label>
                </view>
                <view class="weui-media-box__info__meta weui-media-box__info__meta_extra">
                  <label class="myweui-tag">{{item.intime}}</label>
                </view>
              </view>
            </view>
            <view class="weui-cell__ft weui-cell__ft_in-access"></view>
          </navigator>
        </block>
      </view>
    </view>
    <view class="weui-loadmore">
      <view class="weui-loadmore__tips weui-loadmore__tips_in-line">
        <label class='tips-icon'>小贴士</label>: 此页面只显示审核通过的房屋信息：）</view>
    </view>
  </view>
</view>