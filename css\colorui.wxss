page {
	/* Color 可以自定义相关配色 */
	/* var属性兼容性 --> https://www.caniuse.com/#feat=css-variables */
	/* 标准色 */
	--red: #e54d42;
	--orange: #f37b1d;
	--yellow: #fbbd08;
	--olive: #8dc63f;
	--green: #39b54a;
	--cyan: #1cbbb4;
	--blue: #0081ff;
	--purple: #6739b6;
	--mauve: #9c26b0;
	--pink: #e03997;
	--brown: #a5673f;
	--grey: #8799a3;
	--black: #333333;
	--darkGray: #666666;
	--gray: #aaaaaa;
	--ghostWhite: #f1f1f1;
	--white: #ffffff;
	/* 浅色 */
	--redLight: #fadbd9;
	--orangeLight: #fde6d2;
	--yellowLight: #fef2ce;
	--oliveLight: #e8f4d9;
	--greenLight: #d7f0db;
	--cyanLight: #d2f1f0;
	--blueLight: #cce6ff;
	--purpleLight: #e1d7f0;
	--mauveLight: #ebd4ef;
	--pinkLight: #f9d7ea;
	--brownLight: #ede1d9;
	--greyLight: #e7ebed;
	/* 渐变色 */
	--gradualRed: linear-gradient(45deg, #f43f3b, #ec008c);
	--gradualOrange: linear-gradient(45deg, #ff9700, #ed1c24);
	--gradualGreen: linear-gradient(45deg, #39b54a, #8dc63f);
	--gradualPurple: linear-gradient(45deg, #9000ff, #5e00ff);
	--gradualPink: linear-gradient(45deg, #ec008c, #6739b6);
	--gradualBlue: linear-gradient(45deg, #0081ff, #1cbbb4);
	/* 阴影透明色 */
	--ShadowSize: 6rpx 6rpx 8rpx;
	--redShadow: rgba(204, 69, 59, 0.2);
	--orangeShadow: rgba(217, 109, 26, 0.2);
	--yellowShadow: rgba(224, 170, 7, 0.2);
	--oliveShadow: rgba(124, 173, 55, 0.2);
	--greenShadow: rgba(48, 156, 63, 0.2);
	--cyanShadow: rgba(28, 187, 180, 0.2);
	--blueShadow: rgba(0, 102, 204, 0.2);
	--purpleShadow: rgba(88, 48, 156, 0.2);
	--mauveShadow: rgba(133, 33, 150, 0.2);
	--pinkShadow: rgba(199, 50, 134, 0.2);
	--brownShadow: rgba(140, 88, 53, 0.2);
	--greyShadow: rgba(114, 130, 138, 0.2);
	--grayShadow: rgba(114, 130, 138, 0.2);
	--blackShadow: rgba(26, 26, 26, 0.2);
}

/* ==================
          徽章
 ==================== */

.cu-tag {
	font-size: 24rpx;
	vertical-align: middle;
	position: relative;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
	padding: 0rpx 16rpx;
	height: 48rpx;
	font-family: Helvetica Neue, Helvetica, sans-serif;
	white-space: nowrap;
}

.cu-tag:not([class*="bg"]):not([class*="line"]) {
	background-color: var(--ghostWhite);
}

.cu-tag[class*="line-"]::after {
	content: " ";
	width: 200%;
	height: 200%;
	position: absolute;
	top: 0;
	left: 0;
	border: 1rpx solid currentColor;
	transform: scale(0.5);
	transform-origin: 0 0;
	box-sizing: border-box;
	border-radius: inherit;
	z-index: 1;
	pointer-events: none;
}

.cu-tag.radius[class*="line"]::after {
	border-radius: 12rpx;
}

.cu-tag.round[class*="line"]::after {
	border-radius: 1000rpx;
}

.cu-tag[class*="line-"]::after {
	border-radius: 0;
}

.cu-tag+.cu-tag {
	margin-left: 10rpx;
}

.cu-tag.sm {
	font-size: 20rpx;
	padding: 0rpx 12rpx;
	height: 32rpx;
}

.cu-capsule {
	display: inline-flex;
	vertical-align: middle;
}

.cu-capsule+.cu-capsule {
	margin-left: 10rpx;
}

.cu-capsule .cu-tag {
	margin: 0;
}

.cu-capsule .cu-tag[class*="line-"]:last-child::after {
	border-left: 0rpx solid transparent;
}

.cu-capsule .cu-tag[class*="line-"]:first-child::after {
	border-right: 0rpx solid transparent;
}

.cu-capsule.radius .cu-tag:first-child {
	border-top-left-radius: 6rpx;
	border-bottom-left-radius: 6rpx;
}

.cu-capsule.radius .cu-tag:last-child::after,
.cu-capsule.radius .cu-tag[class*="line-"] {
	border-top-right-radius: 12rpx;
	border-bottom-right-radius: 12rpx;
}

.cu-capsule.round .cu-tag:first-child {
	border-top-left-radius: 200rpx;
	border-bottom-left-radius: 200rpx;
	text-indent: 4rpx;
}

.cu-capsule.round .cu-tag:last-child::after,
.cu-capsule.round .cu-tag:last-child {
	border-top-right-radius: 200rpx;
	border-bottom-right-radius: 200rpx;
	text-indent: -4rpx;
}

.cu-tag.badge {
	border-radius: 200rpx;
	position: absolute;
	top: -10rpx;
	right: -10rpx;
	font-size: 20rpx;
	padding: 0rpx 10rpx;
	height: 28rpx;
	color: var(--white);
}

.cu-tag.badge:not([class*="bg-"]) {
	background-color: #dd514c;
}

.cu-tag:empty:not([class*="cuIcon-"]) {
	padding: 0rpx;
	width: 16rpx;
	height: 16rpx;
	top: -4rpx;
	right: -4rpx;
}

.cu-tag[class*="cuIcon-"] {
	width: 32rpx;
	height: 32rpx;
	top: -4rpx;
	right: -4rpx;
}

/* ==================
         时间轴
 ==================== */

.cu-timeline {
	display: block;
	background-color: var(--white);
}

.cu-timeline .cu-time {
	width: 120rpx;
	text-align: center;
	padding: 20rpx 0;
	font-size: 26rpx;
	color: #888;
	display: block;
}

.cu-timeline>.cu-item {
	padding: 30rpx 30rpx 30rpx 120rpx;
	position: relative;
	display: block;
	z-index: 0;
}

.cu-timeline>.cu-item:not([class*="text-"]) {
	color: #ccc;
}

.cu-timeline>.cu-item::after {
	content: "";
	display: block;
	position: absolute;
	width: 1rpx;
	background-color: #ddd;
	left: 60rpx;
	height: 100%;
	top: 0;
	z-index: 8;
}

.cu-timeline>.cu-item::before {
	font-family: "cuIcon";
	display: block;
	position: absolute;
	top: 36rpx;
	z-index: 9;
	background-color: var(--white);
	width: 50rpx;
	height: 50rpx;
	text-align: center;
	border: none;
	line-height: 50rpx;
	left: 36rpx;
}

.cu-timeline>.cu-item:not([class*="cuIcon-"])::before {
	content: "\e763";
}

.cu-timeline>.cu-item[class*="cuIcon-"]::before {
	background-color: var(--white);
	width: 50rpx;
	height: 50rpx;
	text-align: center;
	border: none;
	line-height: 50rpx;
	left: 36rpx;
}

.cu-timeline>.cu-item>.content {
	padding: 30rpx;
	border-radius: 6rpx;
	display: block;
	line-height: 1.6;
}

.cu-timeline>.cu-item>.content:not([class*="bg-"]) {
	background-color: var(--ghostWhite);
	color: var(--black);
}

.cu-timeline>.cu-item>.content+.content {
	margin-top: 20rpx;
}

/* ==================
          背景
 ==================== */

.line-red::after,
.lines-red::after {
	border-color: var(--red);
}

.line-orange::after,
.lines-orange::after {
	border-color: var(--orange);
}

.line-yellow::after,
.lines-yellow::after {
	border-color: var(--yellow);
}

.line-olive::after,
.lines-olive::after {
	border-color: var(--olive);
}

.line-green::after,
.lines-green::after {
	border-color: var(--green);
}

.line-cyan::after,
.lines-cyan::after {
	border-color: var(--cyan);
}

.line-blue::after,
.lines-blue::after {
	border-color: var(--blue);
}

.line-purple::after,
.lines-purple::after {
	border-color: var(--purple);
}

.line-mauve::after,
.lines-mauve::after {
	border-color: var(--mauve);
}

.line-pink::after,
.lines-pink::after {
	border-color: var(--pink);
}

.line-brown::after,
.lines-brown::after {
	border-color: var(--brown);
}

.line-grey::after,
.lines-grey::after {
	border-color: var(--grey);
}

.line-gray::after,
.lines-gray::after {
	border-color: var(--gray);
}

.line-black::after,
.lines-black::after {
	border-color: var(--black);
}

.line-white::after,
.lines-white::after {
	border-color: var(--white);
}

.bg-red {
	background-color: var(--red);
	color: var(--white);
}

.bg-orange {
	background-color: var(--orange);
	color: var(--white);
}

.bg-yellow {
	background-color: var(--yellow);
	color: var(--black);
}

.bg-olive {
	background-color: var(--olive);
	color: var(--white);
}

.bg-green {
	background-color: var(--green);
	color: var(--white);
}

.bg-cyan {
	background-color: var(--cyan);
	color: var(--white);
}

.bg-blue {
	background-color: var(--blue);
	color: var(--white);
}

.bg-purple {
	background-color: var(--purple);
	color: var(--white);
}

.bg-mauve {
	background-color: var(--mauve);
	color: var(--white);
}

.bg-pink {
	background-color: var(--pink);
	color: var(--white);
}

.bg-brown {
	background-color: var(--brown);
	color: var(--white);
}

.bg-grey {
	background-color: var(--grey);
	color: var(--white);
}

.bg-gray {
	background-color: #f0f0f0;
	color: var(--black);
}

.bg-black {
	background-color: var(--black);
	color: var(--white);
}

.bg-white {
	background-color: var(--white);
	color: var(--darkGray);
}

.bg-shadeTop {
	background-image: linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 0.01));
	color: var(--white);
}

.bg-shadeBottom {
	background-image: linear-gradient(rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 1));
	color: var(--white);
}

.bg-red.light {
	color: var(--red);
	background-color: var(--redLight);
}

.bg-orange.light {
	color: var(--orange);
	background-color: var(--orangeLight);
}

.bg-yellow.light {
	color: var(--yellow);
	background-color: var(--yellowLight);
}

.bg-olive.light {
	color: var(--olive);
	background-color: var(--oliveLight);
}

.bg-green.light {
	color: var(--green);
	background-color: var(--greenLight);
}

.bg-cyan.light {
	color: var(--cyan);
	background-color: var(--cyanLight);
}

.bg-blue.light {
	color: var(--blue);
	background-color: var(--blueLight);
}

.bg-purple.light {
	color: var(--purple);
	background-color: var(--purpleLight);
}

.bg-mauve.light {
	color: var(--mauve);
	background-color: var(--mauveLight);
}

.bg-pink.light {
	color: var(--pink);
	background-color: var(--pinkLight);
}

.bg-brown.light {
	color: var(--brown);
	background-color: var(--brownLight);
}

.bg-grey.light {
	color: var(--grey);
	background-color: var(--greyLight);
}

.bg-gradual-red {
	background-image: var(--gradualRed);
	color: var(--white);
}

.bg-gradual-orange {
	background-image: var(--gradualOrange);
	color: var(--white);
}

.bg-gradual-green {
	background-image: var(--gradualGreen);
	color: var(--white);
}

.bg-gradual-purple {
	background-image: var(--gradualPurple);
	color: var(--white);
}

.bg-gradual-pink {
	background-image: var(--gradualPink);
	color: var(--white);
}

.bg-gradual-blue {
	background-image: var(--gradualBlue);
	color: var(--white);
}

.shadow[class*="-red"] {
	box-shadow: var(--ShadowSize) var(--redShadow);
}

.shadow[class*="-orange"] {
	box-shadow: var(--ShadowSize) var(--orangeShadow);
}

.shadow[class*="-yellow"] {
	box-shadow: var(--ShadowSize) var(--yellowShadow);
}

.shadow[class*="-olive"] {
	box-shadow: var(--ShadowSize) var(--oliveShadow);
}

.shadow[class*="-green"] {
	box-shadow: var(--ShadowSize) var(--greenShadow);
}

.shadow[class*="-cyan"] {
	box-shadow: var(--ShadowSize) var(--cyanShadow);
}

.shadow[class*="-blue"] {
	box-shadow: var(--ShadowSize) var(--blueShadow);
}

.shadow[class*="-purple"] {
	box-shadow: var(--ShadowSize) var(--purpleShadow);
}

.shadow[class*="-mauve"] {
	box-shadow: var(--ShadowSize) var(--mauveShadow);
}

.shadow[class*="-pink"] {
	box-shadow: var(--ShadowSize) var(--pinkShadow);
}

.shadow[class*="-brown"] {
	box-shadow: var(--ShadowSize) var(--brownShadow);
}

.shadow[class*="-grey"] {
	box-shadow: var(--ShadowSize) var(--greyShadow);
}

.shadow[class*="-gray"] {
	box-shadow: var(--ShadowSize) var(--grayShadow);
}

.shadow[class*="-black"] {
	box-shadow: var(--ShadowSize) var(--blackShadow);
}

.shadow[class*="-white"] {
	box-shadow: var(--ShadowSize) var(--blackShadow);
}

.text-shadow[class*="-red"] {
	text-shadow: var(--ShadowSize) var(--redShadow);
}

.text-shadow[class*="-orange"] {
	text-shadow: var(--ShadowSize) var(--orangeShadow);
}

.text-shadow[class*="-yellow"] {
	text-shadow: var(--ShadowSize) var(--yellowShadow);
}

.text-shadow[class*="-olive"] {
	text-shadow: var(--ShadowSize) var(--oliveShadow);
}

.text-shadow[class*="-green"] {
	text-shadow: var(--ShadowSize) var(--greenShadow);
}

.text-shadow[class*="-cyan"] {
	text-shadow: var(--ShadowSize) var(--cyanShadow);
}

.text-shadow[class*="-blue"] {
	text-shadow: var(--ShadowSize) var(--blueShadow);
}

.text-shadow[class*="-purple"] {
	text-shadow: var(--ShadowSize) var(--purpleShadow);
}

.text-shadow[class*="-mauve"] {
	text-shadow: var(--ShadowSize) var(--mauveShadow);
}

.text-shadow[class*="-pink"] {
	text-shadow: var(--ShadowSize) var(--pinkShadow);
}

.text-shadow[class*="-brown"] {
	text-shadow: var(--ShadowSize) var(--brownShadow);
}

.text-shadow[class*="-grey"] {
	text-shadow: var(--ShadowSize) var(--greyShadow);
}

.text-shadow[class*="-gray"] {
	text-shadow: var(--ShadowSize) var(--grayShadow);
}

.text-shadow[class*="-black"] {
	text-shadow: var(--ShadowSize) var(--blackShadow);
}

.bg-img {
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
}

.bg-mask {
	background-color: var(--black);
	position: relative;
}

.bg-mask::after {
	content: "";
	border-radius: inherit;
	width: 100%;
	height: 100%;
	display: block;
	background-color: rgba(0, 0, 0, 0.4);
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	top: 0;
}

.bg-mask view,
.bg-mask cover-view {
	z-index: 5;
	position: relative;
}

.bg-video {
	position: relative;
}

.bg-video video {
	display: block;
	height: 100%;
	width: 100%;
	-o-object-fit: cover;
	object-fit: cover;
	position: absolute;
	top: 0;
	z-index: 0;
	pointer-events: none;
}
