<wxs src="../wxs/utils.wxs" module="utils" />

<van-overlay
  wx:if="{{ overlay }}"
  mask
  show="{{ show }}"
  z-index="{{ zIndex }}"
  custom-style="{{ overlayStyle }}"
  duration="{{ duration }}"
  bind:click="onClickOverlay"
/>
<view
  wx:if="{{ inited }}"
  class="custom-class {{ classes }} {{ utils.bem('popup', [position, { safe: isIPhoneX && safeAreaInsetBottom }]) }}"
  style="z-index: {{ zIndex }}; -webkit-transition-duration:{{ currentDuration }}ms; transition-duration:{{ currentDuration }}ms; {{ display ? '' : 'display: none;' }} {{ customStyle }}"
  bind:transitionend="onTransitionEnd"
>
  <view wx:if="{{ safeAreaInsetTop }}" class="van-popup__safe-top" style="padding-top: {{ statusBarHeight }}px;"></view>
  <slot />
</view>
