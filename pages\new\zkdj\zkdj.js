// pages/new/zkdj/zkdj.js
const http = require("../utils/httpUtils.js");
const FormData = require("../utils/formData.js");
var user = require('../../../pages/new/utils/user.js')
var app = getApp();
var url = app.globalData.requestUrl;
var columnsArr = app.globalData.columnsArr;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    disabled:false,
    identitytype:false,
    data_type:1,
    rent_unrent_time:'',
    rent_contract_img_file:'',
    face_img_file:'',
    identity:'',
    phone:'',
    detail_address:'',
    name:'',
    house_type:'',
    columnsArr:[],
    defaultarray:[
      [],[],[],[]
    ],
    multiIndex: [0, 0, 0, 0],
    area_location_name:'嘉兴市经开区城南街道文博社区',
    area_location_id:'330499001008'
  },
  formSubmit:function(e){
    console.log(this.data.identitytype);
    console.log(e.detail.value)
    let that = this;
    if (!this.data.identitytype) {
      wx.showToast({
        title: "请填写正確身份证号",
        icon: "none",
      });
      return false;
    }
  if (e.detail.value.rent_unrent_time == null || e.detail.value.rent_unrent_time == '') {
    wx.showToast({
      title: "请选择日期",
      icon: "none",
    });
    return false;
  }
  if (e.detail.value.house_type == '') {
    wx.showToast({
      title: "请选择房屋性质",
      icon: "none",
    });
    return false;
  }
  if (that.data.rent_contract_img_file == '') {
    wx.showToast({
      title: "请上传租房合同",
      icon: "none",
    });
    return false;
  }
  if (that.data.face_img_file == '') {
    wx.showToast({
      title: "请上传头像",
      icon: "none",
    });
    return false;
  }
  if (e.detail.value.name == '') {
    wx.showToast({
      title: "请填写姓名",
      icon: "none",
    });
    return false;
  }
  if (e.detail.value.identity == '') {
    wx.showToast({
      title: "请填写身份证号",
      icon: "none",
    });
    return false;
  }
  if (e.detail.value.phone == "") {
    wx.showToast({
      title: "请输入联系电话",
      icon: "none",
    });
    return false;
  }
  if (e.detail.value.detail_address == "") {
    wx.showToast({
      title: "请输入详细住址",
      icon: "none",
    });
    return false;
  }
  // 验证电话格式
  if (!/^(\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}$/.test(e.detail.value.phone)) {
    wx.showToast({
      title: "手机号码有误",
      duration: 2000,
      icon: "none",
    });

    return false;
  }
  that.setData({
    disabled:true
  })
let formData = new FormData();
formData.append('rent_unrent_time', e.detail.value.rent_unrent_time) 
formData.append("house_type", e.detail.value.house_type); 
formData.appendFile('rent_contract_img_file', that.data.rent_contract_img_file[0].path)
formData.appendFile('face_img_file', that.data.face_img_file[0].path) 
formData.append("name", e.detail.value.name); 
formData.append('identity', e.detail.value.identity)
formData.append('phone', e.detail.value.phone) 
formData.append("detail_address", e.detail.value.detail_address); 
formData.append('area_location_id', that.data.area_location_id)
formData.append('area_location_name', that.data.area_location_name) 
formData.append("data_type", 1); 
let datas = formData.getData();
wx.request({
  method: 'post',
  url: 'https://zzsbapi.jxjkga.cn/storemgmt/OpenApi/rentUnrentPush',
  header: {
    token: wx.getStorageSync('token'),
    'content-type': datas.contentType
  },
  data: datas.buffer,
  success(res) {
    if (res.data.success == 1) {
      that.setData({
        disabled:false,
        rent_unrent_time:'',
        rent_contract_img_file:'',
        face_img_file:'',
        name:'',
        identity:'',
        phone:'',
        detail_address:''
      })
      wx.showToast({
        title: '提交成功',
        icon: 'success',
        duration: 2000,
        success:function(){
          wx.switchTab({
            url: '../homeNew/homeNew'
          })
        }
      })
    } else {
      that.setData({
        disabled:false
      })
      wx.showToast({
        title: '提交失败',
        icon: 'none',
        duration: 2000
      })
    }
  },
  fail(res) {
    console.log(res)
  }
})
},
  getaddress: function(e) {
    this.setData({
      detail_address:e.detail.value
    })
  },
  getphone: function(e) {
    this.setData({
      phone:e.detail.value
    })
  },
  getidentity: function(e) {
    this.setData({
      identity:e.detail.value
    })
    this.verifyIdentity_PD(e.detail.value)
  },
  getname: function(e) {
    this.setData({
      name:e.detail.value
    })
  },
  radioChange: function(e) {
    this.setData({
      house_type:e.detail.value
    })
  },
  getface:function(){
    let _this = this;
   wx.chooseImage({
      count: 1,
      success(res) {
        //此处会返回图片暂存路径和文件大小
        const data = res.tempFiles;
        console.log(data)
        _this.setData({
          face_img_file:data
        })
      }
    })
  },
  getrent:function(){
    let _this = this;
   wx.chooseImage({
      count: 1,
      success(res) {
        //此处会返回图片暂存路径和文件大小
        const data = res.tempFiles;
        const file = res.tempFilePaths
        console.log(file)
        _this.setData({
          rent_contract_img_file:data
        })
      }
    })
  },
  _onDelTab(e) {
    // 获取图片索引
    let idx = e.currentTarget.dataset.idx;
    let delFile = this.data.rent_contract_img_file[idx];
    console.log(delFile);
    this.data.rent_contract_img_file.splice(idx, 1);
    this.setData({
      rent_contract_img_file: this.data.rent_contract_img_file
    })
  },
  _onDelFace(e) {
    // 获取图片索引
    let idx = e.currentTarget.dataset.idx;
    let delFile = this.data.face_img_file[idx];
    console.log(delFile);
    this.data.face_img_file.splice(idx, 1);
    this.setData({
      face_img_file: this.data.face_img_file
    })
  },
  upload:function(){
    let _this = this;
   wx.chooseImage({
      count: 1,
      success(res) {
        //此处会返回图片暂存路径和文件大小
        const data = res.tempFiles;
      }
    })
  },
  bindDateChange: function(e) {
    console.log('picker发送选择改变，携带值为', e.detail.value)
    this.setData({
      rent_unrent_time: e.detail.value
    })
  },
  transAreaData(arrData) {
    let arr = [];
    let obj = { text: '', children: [] }
    if (arrData.length > 0) {
      arrData.forEach(item => {
        if (item.childTrees && item.childTrees.length > 0) {
          arr.push({ id: item.id, text: item.name, children: this.transAreaData(item.childTrees) })
        } else {
          arr.push({ id: item.id, text: item.name });
        }
      });
    } else {
      arr.push({ text: '' })
    }
    return arr;
  },
  bindMultiPickerChange: function (e) {
    console.log('picker发送选择改变，携带值为', e.detail.value)
    // var that = this;
    // var defaultarray = that.data.defaultarray,
    // var area_location_name =defaultarray[0][multiIndex[0]].text+defaultarray[1][multiIndex[1]].text+defaultarray[2][multiIndex[2]].text+defaultarray[3][multiIndex[3]].text
    this.setData({
        multiIndex: e.detail.value,
        // area_location_name:area_location_name
    })
},
bindMultiPickerColumnChange: function (e) {
  console.log('修改的列为', e.detail.column, '，（列下标）值为', e.detail.value);
  var that = this;
  var defaultarray = that.data.defaultarray,
  array = that.data.columnsArr,
  multiIndex = that.data.multiIndex

  // 更改multiIndex
  multiIndex[e.detail.column] = e.detail.value
  var searchColumn = () => {
    for (var i = 0; i < array.length; i++) {
        var arr1 = []; //defaultarray[1]
        var arr2 = []; //defaultarray[2]
        var arr3 = []; //defaultarray[3]
        if (i == multiIndex[0]) { //找到修改的列号i
            for (var j = 0; j < array[i].children.length; j++) {
                arr1.push({text:array[i].children[j].text,id:array[i].children[j].id});
                if (j == multiIndex[1]) {
                    for (var k = 0; k < array[i].children[j].children.length; k++) {
                        arr2.push({text:array[i].children[j].children[k].text,id:array[i].children[j].children[k].id});
                        if (k == multiIndex[2]) {
                            for (var g = 0; g < array[i].children[j].children[k].children.length; g++) {
                                arr3.push({text:array[i].children[j].children[k].children[g].text,id:array[i].children[j].children[k].children[g].id});
                            }
                            defaultarray[3] = arr3;
                        }
                    }
                    defaultarray[2] = arr2;
                }
            }
            defaultarray[1] = arr1;
        }
    };
}
switch (e.detail.column) {
  case 0:
      multiIndex[1] = 0;
      multiIndex[2] = 0;
      multiIndex[3] = 0;
      searchColumn();
      break;
  case 1:
      multiIndex[2] = 0;
      multiIndex[3] = 0;
      searchColumn();
      break;
  case 2:
      multiIndex[3] = 0;
      searchColumn();
      break;

}

this.setData({
  defaultarray: defaultarray,
  multiIndex: multiIndex,
  area_location_name:defaultarray[0][multiIndex[0]].text+defaultarray[1][multiIndex[1]].text+defaultarray[2][multiIndex[2]].text+defaultarray[3][multiIndex[3]].text,
  area_location_id:defaultarray[3][multiIndex[3]].id,
});
console.log(defaultarray[3][multiIndex[3]].id,)
},
  getAreaData() {
    http.get('base/getJKArea', {
    }, res => {
      if (res.data.success == 1) {
        let tempData = res.data.data;
        let resultObj = { id: tempData.id, text: tempData.name, children: this.transAreaData(tempData.childTrees) }
          let resultArr = [resultObj];
          this.setData({
            columnsArr: resultArr, 
          });
          var data = {
            columnsArr: this.data.columnsArr,
            multiIndex: this.data.multiIndex,
            defaultarray: this.data.defaultarray,
          };
          // console.log(data.columnsArr)
          for (var i = 0; i < resultArr.length; i++) {
            data.defaultarray[0].push({text:resultArr[i].text,id:resultArr[i].id});
          }
          for (var j = 0; j < resultArr[data.multiIndex[0]].children.length; j++) {
            data.defaultarray[1].push({text:resultArr[data.multiIndex[0]].children[j].text,id:resultArr[data.multiIndex[0]].children[j].id});
          }
          for (var k = 0; k < resultArr[data.multiIndex[0]].children[data.multiIndex[1]].children.length; k++) {
            data.defaultarray[2].push({text:resultArr[data.multiIndex[0]].children[data.multiIndex[1]].children[k].text,id:resultArr[data.multiIndex[0]].children[data.multiIndex[1]].children[k].id});
          }
          for (var o = 0; o < resultArr[data.multiIndex[0]].children[data.multiIndex[1]].children[data.multiIndex[2]].children.length; o++) {
            data.defaultarray[3].push({text:resultArr[data.multiIndex[0]].children[data.multiIndex[1]].children[data.multiIndex[2]].children[o].text,id:resultArr[data.multiIndex[0]].children[data.multiIndex[1]].children[data.multiIndex[2]].children[o].id});
          }
          this.setData({
            defaultarray:data.defaultarray,
            // area_location_name:data.defaultarray[0][data.multiIndex[0]].text+data.defaultarray[1][data.multiIndex[1]].text+data.defaultarray[2][data.multiIndex[2]].text+data.defaultarray[3][data.multiIndex[3]].text,
            // area_location_id:data.defaultarray[3][data.multiIndex[3]].id
          });




      }
    });
  },
  verifyIdentity_PD(val) {
    var that = this;
    if (val.length == 18) {
      let param = new FormData() // 创建form对象
      param.append('identity', val) // 通过append向form对象添加数据
      let datas = param.getData();
        wx.request({
          method: 'post',
          url: 'https://zzsbapi.jxjkga.cn/storemgmt/base/verifyIdentity',
          header: {
            token: wx.getStorageSync('token'),
            'content-type': datas.contentType
          },
          data: datas.buffer,
          success(res) {
            if (res.data.success == 1) {
              wx.showToast({
                title: '身份证格式正确',
                icon: 'success',
                duration: 2000
              }),
              that.setData({
                identitytype: true
              });
            } else {
              wx.showToast({
                title: '身份证格式错误',
                icon: 'none',
                duration: 2000
              }),
              that.setData({
                identitytype: false
              });
            }
          },
          fail(res) {
            console.log(res)
          }
        })
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
  this.getAreaData()
  
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
  //   var defaultarray = this.data.defaultarray
  //   var array = this.data.columnsArr
  //  console.log(this.data.columnsArr)
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

 
})