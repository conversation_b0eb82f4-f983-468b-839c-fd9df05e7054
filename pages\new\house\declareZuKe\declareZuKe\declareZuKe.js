// pages/new/house/declareZuKe/declareZuKe/declareZuKe.js
var common = require('../../../utils/common.js');
var user = require('../../../utils/user.js');
const http = require("../../../utils/httpUtils.js");
const date = require("../../../utils/dateUtils.js");
var app = getApp();
var url = app.globalData.requestUrl;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    swiper: {
      indicatorDots: true,
      vertical: false,
      autoplay: true,
      circular: true,
      interval: 3000,
      duration: 500,
      previousMargin: 0,
      nextMargin: 0,
      height: 'auto',
      image: []
    },
    house: {
      id: 0,
      uuid: '',
      area_location_id: 0,
      area_location_name: '请选择',
      address: '',
      house_type_name: '',
      is_rent: 0,
      remark: '',
      owner_id: 0,
      owner_photo: '',
      owner_realname: '',
      owner_mobilephone_number: '',
      status: -2
    },
    renter: {
      type:1,
      id: 0,
      uuid: '',
      house_uuid: '',
      img_url: '',
      member_id: 0,
      member_photo: '',
      member_realname: '',
      member_mobilephone_number: '',
      room_number: '',
      live_date_begin: date.getDate(null,0),
      live_date_end: date.getDate(null,365),
      remark: '',
      status: -2,
      user_id: 0,
      ip: '',
      intime: ''
    }
  },
  changeIndicatorDots: function (e) {
    this.setData({
      indicatorDots: !this.data.indicatorDots
    })
  },
  changeAutoplay: function (e) {
    this.setData({
      autoplay: !this.data.autoplay
    })
  },
  intervalChange: function (e) {
    this.setData({
      interval: e.detail.value
    })
  },
  durationChange: function (e) {
    this.setData({
      duration: e.detail.value
    })
  },
  bindSwiperHeight: function (e) {
    var that = this;
    var winWid = wx.getSystemInfoSync().windowWidth; //获取当前屏幕的宽度
    var imgh = e.detail.height; //图片高度
    var imgw = e.detail.width;
    var swiperH = winWid * imgh / imgw + "px";
    //等比设置swiper的高度。  即 屏幕宽度 / swiper高度 = 图片宽度 / 图片高度    ==》swiper高度 = 屏幕宽度 * 图片高度 / 图片宽度
    var swiper = that.data.swiper;
    swiper.height = swiperH;
    that.setData({
      swiper: swiper
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    var data = {
      house: that.data.house,
      renter: that.data.renter
    };
    var house = that.data.house;
    var renter = that.data.renter;
    var swiper = that.data.swiper;
    if (options.uuid == undefined || options.uuid == '') {
      renter.uuid = common.wxuuid();
    }
    user.getUserInfo().then((res) => {
      var userInfo = getApp().globalData.userInfo;
      renter.member_id = userInfo.id;
      renter.member_mobilephone_number = userInfo.mobilephone_number;
      renter.member_realname = userInfo.realname;
      renter.member_photo = userInfo.photo;
      wx.request({
        method: 'GET',
        url: url + '/storemgmt/house/detail?uuid=' + options.houseid,
        header: {
          'content-type': 'application/x-www-form-urlencoded',
          token: wx.getStorageSync('token')
        },
        success(res) {
          house = res.data.data;
          if (house.owner_photo == null || house.owner_photo == '') {
            house.owner_photo = '/images/avatar_null.png';
          } else {
            house.owner_photo = url + house.owner_photo;
          } 
          renter.house_uuid = house.uuid;
          data.house = house;
          data.renter = renter;
          that.setData(data);
          console.log(that.data);
        }
      })
      //图片
      wx.request({
        method: 'GET',
        url: url + '/storemgmt/file/list?page_num=1&page_size=0&table=house&table_uuid=' + options.houseid,
        header: {
          'content-type': 'application/x-www-form-urlencoded',
          token: wx.getStorageSync('token')
        },
        success(res) {
          if (res.data.data.data_list.length > 0) {
            for (let i = 0; i < res.data.data.data_list.length; i++)
              swiper.image.push(url + res.data.data.data_list[i].file_path);
          }
          that.setData({
            swiper: swiper
          });
          console.log(that.data.swiper);
        }
      })
    });
  },
  bindSave: function () {
    var that = this;
    // if (that.data.renter.room_number == '') {
    //   wx.showToast({
    //     title: '请输入房间号',
    //     icon: 'none',
    //     duration: 2000
    //   })
    //   return;
    // }
    // if (that.data.renter.live_date_begin == '') {
    //   wx.showToast({
    //     title: '请选择租赁时间起',
    //     icon: 'none',
    //     duration: 2000
    //   })
    //   return;
    // }
    // if (that.data.renter.live_date_end == '') {
    //   wx.showToast({
    //     title: '请选择租赁时间止',
    //     icon: 'none',
    //     duration: 2000
    //   })
    //   return;
    // }
    that.data.renter.member_photo= that.data.renter.member_photo.replace(url, '');
    that.data.renter.img_url = that.data.renter.member_photo;
    that.data.renter.room_number = that.data.house.address;
    console.log(that.data.renter);
    http.jsonpost('house/renter/add', 
       that.data.renter
    , res => { 
        let applyid = res.data.data.apply_uuid
        wx.showModal({
          title: '提示',
          content: '保存成功，是否马上提交审核？',
          cancelText: '暂不提交',
          cancelColor: '#ff0000',
          confirmText: '提交审核',
          confirmColor: '#008000',
          success(res) {
            if (res.confirm) {
              wx.request({
                method: 'POST',
                url: url + '/storemgmt/house/apply/verify',
                data: {
                  uuid: applyid,
                  status: -1,
                  content: ''
                },
                header: {
                  'content-type': 'application/x-www-form-urlencoded',
                  token: wx.getStorageSync('token')
                },
                success(res) {
                  if (res.data.success == 1) {
                    wx.showModal({
                      title: '提示',
                      content: '提交成功，请耐心等待管理员审核',
                      showCancel: false,
                      success(res) {
                        if (res.confirm) {
                          wx.switchTab({
                            url: '/pages/new/declareRecord/declareRecord/declareRecord',
                          })
                        }
                      }
                    })
                  } else {
                    wx.showModal({
                      title: '提示',
                      content: '提交失败',
                      showCancel: false,
                      success(res) {
                        if (res.confirm) {
                          wx.switchTab({
                            url: '/pages/new/declareRecord/declareRecord/declareRecord',
                          })
                        }
                      }
                    })
                  }
                }
              })
            } else if (res.cancel) {
              wx.switchTab({
                url: '/pages/new/declareRecord/declareRecord/declareRecord'
              })
            }
          }
        })
    });

  },
  bindRoomInfo: function (e) {
    var renter = this.data.renter;
    renter.room_number = e.detail.value;
    this.setData({
      renter: renter
    });
  },
  bindDateBeginChange: function (e) {
    console.log(e);
    var renter = this.data.renter;
    renter.live_date_begin = e.detail.value;
    this.setData({
      renter: renter
    });
  },
  bindDateEndChange: function (e) {
    console.log(e);
    var renter = this.data.renter;
    renter.live_date_end = e.detail.value;
    this.setData({
      renter: renter
    });
    console.log(this.data);
  },
  bindRemark: function (e) {
    var renter = this.data.renter;
    renter.remark = e.detail.value;
    this.setData({
      renter: renter
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

 
})