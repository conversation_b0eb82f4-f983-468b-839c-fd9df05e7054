// pages/expandWebView/expandWebView.js
// const http = require("../new/utils/httpUtils.js");
// var user = require('../../pages/new/utils/user.js')

const user = require("../new/utils/user");

// var app = getApp();
var app = getApp();
var url = app.globalData.requestUrl;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    // sbhc_status: 0,
    // wgyr_status: 0,
    // xfrw_status: 0,
    // op_status: 0,
    // userInfo:null,
    web_url: '',
  },

  /**
   * 生命周期函数--监听页面加载
   */
   /**验证是否人证核实 */
  doAuth: function () {
    user.doAuth();
  },
  onLoad: function(query) {
    let self = this;
    
    let token = wx.getStorageSync('token');
    let scene = decodeURIComponent(query.scene)

  if (token) {
    new Promise(function (resolve, reject) {
     
      if (wx.getStorageSync('token') && wx.getStorageSync('token') != '') {
        wx.request({
          method: 'GET',
          url: url + '/storemgmt/base/user/info/byToken',
          header: {
            token: wx.getStorageSync('token')
          },
          success(res) {
            if (res.data.success == 1) {
              console.log(res.data);
              // wx.setStorageSync('function_set', JSON.stringify(res.data.data.function_set) );
              app.globalData.userInfo.isLogin = true;
              if (res.data.data.identity != null && res.data.data.identity != '') {
                app.globalData.userInfo.isAuth = true;
              } else {
                app.globalData.userInfo.isAuth = false;
              }
              //获取加密过的身份证号码 用ASCII码互转成身份证号码
              if (res.data.data.asc_num != null && res.data.data.asc_num != '') {
                let new_asc_num = "";
                var array = res.data.data.asc_num.split('');
                for(var i=0;i<array.length;i++){
                  new_asc_num += String.fromCharCode(array[i].charCodeAt() -20);
                }
                app.globalData.userInfo.asc_num = new_asc_num;
                //console.log("转码的身份证"+new_asc_num)
              } 
  

              // if (wx.getStorageSync('accept') == "1") {
              //   app.globalData.userInfo.isOpenNotice = true;
              // }
              // wx.getSetting({
              //   success: (res) => { 
              //     if (res.authSetting['scope.userLocation']) {
              //       app.globalData.userInfo.isOpenLocation = true;
              //     }else{
              //       // wx.getLocation({
              //       //   type: 'wgs84',
              //       //   success (res) {
              //       //     app.globalData.userInfo.isOpenLocation = true;
              //       //   }
              //       //  })
              //     }
              //   }
              // }) 
              app.globalData.userInfo.data_permission = res.data.data.data_permission == null ? '' : res.data.data.data_permission;
              if (app.globalData.userInfo.data_permission != '') {
                let strArray = app.globalData.userInfo.data_permission.split(',');
                let permissionArray = [];
                for (let item in strArray) {
                  if (strArray[item].indexOf('xq') > -1) {
                    permissionArray.push(strArray[item].split('xq')[1])
                  }
                }
                app.globalData.userInfo.permissionArray = permissionArray;
              }
              app.globalData.userInfo.all_function = res.data.data.all_function == null ? '' : res.data.data.all_function;
              app.globalData.userInfo.id = res.data.data.id == null ? '' : res.data.data.id;
              app.globalData.userInfo.realname = res.data.data.name == null ? '' : res.data.data.name;
              app.globalData.userInfo.id_number = res.data.data.identity == null ? '' : res.data.data.identity;
              if (res.data.data.best_frame_url != null && res.data.data.best_frame_url != '') {
                app.globalData.userInfo.photo = app.globalData.requestUrl + res.data.data.best_frame_url;
              }
              app.globalData.userInfo.mobilephone_number = res.data.data.phone == null ? '' : res.data.data.phone;
              app.globalData.userInfo.car_number = res.data.data.car_number == null ? '' : res.data.data.car_number;
              app.globalData.userInfo.address_reg = res.data.data.address == null ? '' : res.data.data.address;
              app.globalData.userInfo.professional = res.data.data.occupation == null ? '' : res.data.data.occupation;
              app.globalData.userInfo.company = res.data.data.user_org_name == null ? '' : res.data.data.user_org_name;
              resolve();
            } else {
              resolve();
              if (isRedirect != false) {
                wx.redirectTo({
                  url: '/pages/new/user/login?_info=登录已过期,请重新登录'
                })
              }
            }
          }
        })
      } else {
        resolve();
        if (isRedirect != false) {
          wx.redirectTo({
            url: '/pages/new/user/login',
          })
        }
      }
    }).then(()=>{
      if (app.globalData.userInfo.isAuth) {
        let targetUrl = "https://zzsbui.jxjkga.cn/#/newdpzzsb?utoken=" + encodeURIComponent(token);
        let paramArray = scene.split("&");
        for (var i = 0; i < paramArray.length; i++) {
          let key = paramArray[i].split("=")[0]; 
          let value = paramArray[i].split("=")[1];
          console.log(paramArray)
          targetUrl = targetUrl + "&" + key + "=" + value
          console.log(targetUrl)
        }
        self.setData({
          web_url: targetUrl
        })
        
      }else{
        user.doAuth(()=>{
          let targetUrl = "https://zzsbui.jxjkga.cn/#/newdpzzsb?utoken=" + encodeURIComponent(token);
          let paramArray = scene.split("&");
          for (var i = 0; i < paramArray.length; i++) {
            let key = paramArray[i].split("=")[0]; 
            let value = paramArray[i].split("=")[1];
            console.log(paramArray)
            targetUrl = targetUrl + "&" + key + "=" + value
            console.log(targetUrl)
          }
          
          self.setData({
            web_url: targetUrl
          })
        })
      }
    })
      
    }else{
      wx.showModal({
        title: '提示',
        content: '请先注册并登录',
        success(res) {
          if (res.confirm) {
            wx.redirectTo({
              url: '/pages/new/user/login'
            })
          } else if (res.cancel) {
            wx.showToast({
              title: '登录失败',
              icon: 'none',
              duration: 2000
            })
          }
        }
      })
    }
    
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

   
})