<!--pages/new/xlxjrw/xlxjrw.wxml-->
<watermark></watermark>
<view class="container">
  <view class="module" bindtap="gotoxl">
    <view class='Ovalpage'>
      <image class='Ovalpage_img' mode="scaleToFill" src="https://zzsbapi.jxjkga.cn/wxzzsb/new/Oval.png" />
      <view style="flex-direction:column;">
        <view style="font-size: 32px;">{{PatrolCount.mileage}}KM</view>
        <view style="text-align: center; font-weight: 100;">累计里程</view>
      </view>
    </view>
    <view style="display: flex;justify-content: space-between; ">
      <view style="flex-direction:column;width: 40%;">
        <view class='hy_Num'>{{PatrolCount.duration}}小时</view>
        <view class='hy_text'>在线时长</view>
      </view>
      <view style="flex-direction:column;width: 40%;">
        <view class='hy_Num'>{{PatrolCount.num}}次</view>
        <view class='hy_text'>出勤次数</view>
      </view>
    </view>
    <view style="flex-direction:row;">
      <view class='p10 section__title'>巡逻日期: </view>
      <picker data-field="date" mode="date" start="2024-01-01" end="2097-09-01" bindchange="bindDateChange">
        <view class="weui-input">开始时间: {{start_time}}</view>
      </picker>
      <picker data-field="date" mode="date" start="2024-01-01" end="2097-09-01" bindchange="endDateChange">
        <view class="weui-input">结束时间: {{end_time}}</view>
      </picker>
    </view>
    <view>
      <view class='p10'>任务名称</view>
      <view class="weui-input "> <input value="{{patrol_name}}" bindinput="bindKeyInput" placeholder="请输入任务名称" /></view>
    </view>
    <view style="display: flex; justify-content: space-around; padding-top: 20px;">
      <view style='width: 45%;' bind:tap="getMyPatrolCount">
        <button style="background: linear-gradient(90deg, #287BFF 0%, #4DCDFF 100%); color:aliceblue;"> 搜索</button>
      </view>
      <view style='width: 45%;'  bind:tap="resetting_getMyPatrolCount">
        <button style="opacity: 1;background: linear-gradient(90deg, #F98383 0%, #FFC978 98%); color:aliceblue; "> 重置</button>
      </view>
    </view>
  </view>
  <view class="list_css" wx:for="{{RecordList}}" wx:key="{{index}}" id="{{index}}"  bindtap="openClick">
    <view style="width: 90%;" >
      <view>任务名称:  {{item.patrol_name}}</view>
      <view>签到时间:{{item.sign_in_time}}</view>
      <view>签退时间:{{item.sign_out_time}}</view>
    </view>
    <view>
      <van-icon name="arrow" />
    </view>
  </view>
  <br/>
  <br/>
  <br/>
</view>