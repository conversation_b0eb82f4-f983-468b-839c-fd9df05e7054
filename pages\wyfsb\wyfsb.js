// pages/wyfsb/wyfsb.js
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    active: 0,
    visitCommunity: {},
    fhObj: {},
    fhIndex: 0,
    BookOnlineRoomList: [],
    CompanyList: [
      {
        name: '本人住宿登记',
        text: '点击授权个人信息用于公安实有人口管理',
        img: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/ruzhu.png'
      },
      {
        name: '帮助登记',
        text: '帮助小孩及不会智能手机的老人进行登记',
        img: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/bzdj.png'
      },
      {
        name: '网约房客服',
        text: '遇到住宿登记等问题及时联系网约房客服',
        img: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/kefu.png'
      }
    ],
    HOST: 'https://zzsbapi.jxjkga.cn' // 请根据实际情况修改API地址
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getMyBookOnlineRoomList()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 从其他页面返回时刷新数据
    this.getMyBookOnlineRoomList()
  },

  /**
   * 房间选择器变化
   */
  onRoomChange(e) {
    const index = e.detail.value
    this.setData({
      fhIndex: index,
      fhObj: this.data.BookOnlineRoomList[index]
    })
    this.ck_clcick(this.data.BookOnlineRoomList[index])
  },

  /**
   * 刷新二维码
   */
  sxewm_Click() {
    if (this.data.fhObj && this.data.fhObj.id) {
      this.ck_clcick(this.data.fhObj)
    }
  },

  /**
   * 获取我的网约房房间列表
   */
  getMyBookOnlineRoomList() {
    const that = this
    wx.request({
      url: that.data.HOST + '/company/getMyBookOnlineRoomList',
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success(res) {
        if (res.data.success == 1) {
          const lineRoomList = res.data.data
          if (lineRoomList.length == 0) {
            that.setData({
              BookOnlineRoomList: []
            })
            return
          }

          const roomList = []
          for (let i = 0; i < lineRoomList.length; i++) {
            roomList.push({
              id: lineRoomList[i].address_id,
              text: lineRoomList[i].address_name
            })
          }

          that.setData({
            BookOnlineRoomList: roomList,
            fhObj: roomList[0],
            fhIndex: 0
          })

          if (roomList.length > 0) {
            that.ck_clcick(roomList[0])
          }
        } else {
          wx.showToast({
            title: res.data.dsc || '获取房间列表失败',
            icon: 'none'
          })
        }
      },
      fail(err) {
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 生成房间楼层二维码
   */
  ck_clcick(item) {
    const that = this
    wx.request({
      url: that.data.HOST + '/company/generateOnlineRoomFloorQrCode',
      method: 'GET',
      data: {
        address_id: item.id
      },
      header: {
        'content-type': 'application/json'
      },
      success(res) {
        if (res.data.success == 1) {
          const visitCommunity = res.data.data
          visitCommunity.address_name = item.address_name
          visitCommunity.pass_code = "data:image/jpeg;base64," + visitCommunity.pass_code

          that.setData({
            visitCommunity: visitCommunity
          })
        } else {
          wx.showToast({
            title: res.data.dsc || '生成二维码失败',
            icon: 'none'
          })
        }
      },
      fail(err) {
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 点击功能按钮
   */
  djClick(e) {
    const index = e.currentTarget.dataset.index
    const that = this

    if (index === 0) {
      wx.showModal({
        title: '提醒',
        content: '我授权我的头像、手机、身份证等个人信息用于公安实有人口管控',
        confirmText: '同意',
        cancelText: '不同意',
        success(res) {
          if (res.confirm) {
            setTimeout(() => {
              wx.navigateTo({
                url: '/pages/wyfsb/wyfbzdj?apply_type=0&company_id=' + (that.data.company_id || '')
              })
            }, 300)
          }
        }
      })
    } else if (index === 1) {
      wx.navigateTo({
        url: '/pages/wyfsb/wyfbzdj?apply_type=1&company_id=' + (this.data.company_id || '')
      })
    } else if (index === 2) {
      wx.showToast({
        title: '功能暂未开通',
        icon: 'none'
      })
    }
  }
})