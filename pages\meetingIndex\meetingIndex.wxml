<!--pages/meetingIndex/meetingIndex.wxml-->
<view class="container">
  <watermark></watermark>
  <view class="bg">
    <image class="" src="../../images/bg.jpg"></image>
  </view>
  <view class="title">
    <text class="top">关注民生·为民解忧</text>
    <text class="bottom">——访客人员申请系统——</text>
  </view>
  <view class="btn">
    <button class="myBtn" bindtap="sub" bindtap="toApply" type="primary" style="background: #fff;color:#ff0000">访客申请</button>
  </view>
  <view class="applicationList">
    <view class="list" wx:for="{{applictionArr}}" wx:key="key" data-status="{{item.visit_status}}" data-vid="{{item.visit_id}}" bindtap="tolook">
      <view class="visitInfo">
        <view class="tt">
          <text>被访者：</text>
        </view>
        <view class="cc">
          <text>{{item.visit_person_name}}</text>
          <text>{{item.visit_person_mobile}}</text>
        </view>
      </view>
      <view class="visitTime">
        <view class="tt">
          <text>拜访时间：</text>
        </view>
        <view class="cc">
          <text>{{item.visit_start_time}} 至 {{item.visit_end_time}}</text>
        </view>
      </view>
      <view class="visitTime">
        <view class="tt">
          <text>拜访原因：</text>
        </view>
        <view class="cc">
          <text>{{item.visit_person_reason}}</text>
        </view>
      </view>
      <view class="visitStatus">
        <view class="tt">
          <text>审核状态：</text>
        </view>
        <view class="cc">
          <text>{{status[item.visit_status]}}</text>
          <button size="mini" type="primary" style="background: #fff;color: #ff0000;">点击查看详情</button>
        </view>
      </view>
    </view>
  </view>
</view>
