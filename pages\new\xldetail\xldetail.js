// pages/new/xldetail/xldetail.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    id:'',
    name:'',
    address:'',
    ledlist:[]
  },
  getDetail:function(e){
    
    var id=e.currentTarget.dataset.id;
    var companyid=e.currentTarget.dataset.companyid;
    var ledname =e.currentTarget.dataset.ledname
    wx.navigateTo({
      url: '../xldetailsb/xldetailsb?id='+id+'&companyid='+companyid+'&ledname='+ledname
    })
  },
  getCompanyLedList(id){
    let that = this
    wx.request({
      url: 'https://zzsbapi.jxjkga.cn/storemgmt/company/getCompanyLedList',
      header: {
        token: wx.getStorageSync('token'),
      },
      data: {
        page_num: 1,
        page_size: 999,
        status: 0,
        company_id: id,
      },
      method: 'get',
      success: (res) => {
      let dataListall =[] 
      dataListall =res.data.data.data_list
      if(dataListall){
        dataListall=dataListall.filter(item=>{
          return item.status == 1
        })
      }
      that.setData({
        ledlist:dataListall
      })
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // console.log(options)
    this.setData({
      id:options.id,
      name:options.name,
      address:options.address
    })
    this.getCompanyLedList(options.id)
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})