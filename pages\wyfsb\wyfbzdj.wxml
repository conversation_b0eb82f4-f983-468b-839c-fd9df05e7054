<!--pages/wyfsb/wyfbzdj.wxml-->
<view>
  <view class="container">
    <view class="pt10 fz15 fw">人员信息</view>

    <view class="pt10">
      <view wx:if="{{ParentData.apply_type == 0}}">
        <image
          class="avatar-image"
          mode="aspectFill"
          src="{{pageData.best_frame_url}}"
        />
      </view>

      <view wx:else class="upload-section">
        <view class="uploader-wrapper">
          <view wx:if="{{fileList.length === 0}}" class="upload-placeholder" bindtap="chooseImage">
            <text class="upload-text">点击上传照片</text>
          </view>
          <view wx:else class="uploaded-image">
            <image src="{{fileList[0].url}}" mode="aspectFill" class="uploaded-img" />
            <view class="delete-btn" bindtap="deleteImage">×</view>
          </view>
        </view>
      </view>
    </view>

    <view class="pt15">
      <view class="form-group">
        <view class="form-item">
          <text class="form-label">姓名:</text>
          <input class="form-input" placeholder="请输入姓名" value="{{pageData.user_name}}" bindinput="onInputChange" data-field="user_name" />
        </view>

        <view class="form-item">
          <text class="form-label">联系方式:</text>
          <input class="form-input" type="number" placeholder="请输入联系方式" value="{{pageData.user_phone}}" bindinput="onInputChange" data-field="user_phone" bindblur="blurClick" />
        </view>

        <view class="form-item" bindtap="showIdentityTypePicker">
          <text class="form-label">证件类型:</text>
          <view class="picker-wrapper">
            <text class="picker-text">{{pageData.user_identity_type || '请选择证件类型'}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </view>

        <view class="form-item">
          <text class="form-label">证件号码:</text>
          <input class="form-input" placeholder="请输入身份证号码" value="{{pageData.user_identity}}" bindinput="onInputChange" data-field="user_identity" bindblur="verifyIdentity" />
        </view>
      </view>
    </view>

    <view class="pt15" wx:if="{{BytokenObj.is_wcn === 1}}">
      <view class="divider"></view>
      <view class="fz15 fw pt20 red pb10">监护人信息</view>

      <view class="form-group">
        <view class="form-item">
          <text class="form-label">监护人姓名:</text>
          <input class="form-input" placeholder="请输入监护人姓名" value="{{pageData.jhr_name}}" bindinput="onInputChange" data-field="jhr_name" />
        </view>

        <view class="form-item">
          <text class="form-label">监护人关系:</text>
          <input class="form-input" placeholder="请输入监护人关系" value="{{pageData.jhr_relation}}" bindinput="onInputChange" data-field="jhr_relation" />
        </view>

        <view class="form-item">
          <text class="form-label">监护人电话:</text>
          <input class="form-input" type="number" placeholder="请输入监护人电话" value="{{pageData.jhr_phone}}" bindinput="onInputChange" data-field="jhr_phone" bindblur="blurClick" />
        </view>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-buttons">
    <view class="button-group">
      <button class="submit-btn" bindtap="onSubmit" data-type="1">
        <text class="btn-icon">✓</text>
        <text>提交</text>
      </button>

      <button class="cancel-btn" bindtap="onSubmit" data-type="0">
        <text class="btn-icon">×</text>
        <text>取消</text>
      </button>
    </view>
  </view>

  <!-- 证件类型选择器 -->
  <picker wx:if="{{showPicker}}" mode="selector" range="{{columns}}" value="{{identityTypeIndex}}" bindchange="onIdentityTypeConfirm" bindcancel="hideIdentityTypePicker">
  </picker>

  <!-- 遮罩层 -->
  <view wx:if="{{showPicker}}" class="picker-mask" bindtap="hideIdentityTypePicker"></view>

  <!-- 选择器弹窗 -->
  <view wx:if="{{showPicker}}" class="picker-popup">
    <view class="picker-header">
      <text class="picker-cancel" bindtap="hideIdentityTypePicker">取消</text>
      <text class="picker-title">选择证件类型</text>
      <text class="picker-confirm" bindtap="confirmIdentityType">确定</text>
    </view>
    <picker-view class="picker-view" value="{{[identityTypeIndex]}}" bindchange="onPickerChange">
      <picker-view-column>
        <view wx:for="{{columns}}" wx:key="*this" class="picker-item">{{item}}</view>
      </picker-view-column>
    </picker-view>
  </view>
</view>