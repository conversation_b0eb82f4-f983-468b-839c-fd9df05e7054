<!--pages/new/shop/info.wxml-->
<view class="page">
  <watermark></watermark>
  <view class="page__bd">
    <view class="weui-search-bar">
      <view class="weui-search-bar__form">
        <view class="weui-search-bar__box">
          <icon class="weui-icon-search_in-box" type="search" size="14"></icon>
          <input type="text" class="weui-search-bar__input" placeholder="手机号" value="{{inputVal}}" focus="{{inputShowed}}" bindinput="inputTyping" bindconfirm="searchHouse" />
          <view class="weui-icon-clear" wx:if="{{inputVal.length > 0}}" bindtap="clearInput">
            <icon type="clear" size="14"></icon>
          </view>
        </view>
        <label class="weui-search-bar__label" hidden="{{inputShowed}}" bindtap="showInput">
          <icon class="weui-icon-search" type="search" size="14"></icon>
          <view class="weui-search-bar__text">通过房东手机号搜索房屋</view>
        </label>
      </view>
      <view class="weui-search-bar__cancel-btn" hidden="{{!inputShowed}}" bindtap="hideInput">取消</view>
    </view>
    <view class="searchbar-result weui-panel weui-panel_access" wx:if="{{inputVal.length > 0}}">
      <view wx:for="{{house}}" wx:for-item="item" wx:key="uuid" class="weui-panel__bd">
        <navigator url="/pages/new/house/declareZuKe/declareZuKe/declareZuKe?houseid={{item.uuid}}" class="weui-media-box weui-media-box_appmsg" hover-class="weui-cell_active">
          <view class="weui-media-box__hd weui-media-box__hd_in-appmsg">
            <image class="weui-media-box__thumb" src="{{item.img_url}}" />
          </view>
          <view class="weui-media-box__bd weui-media-box__bd_in-appmsg">
            <view class="weui-media-box__title">{{item.address}}
            </view>
            <view class="weui-media-box__desc">
              <label>{{item.owner_realname}} {{item.owner_mobilephone_number}}</label>
            </view>
          </view>
          <view class="weui-cell__ft weui-cell__ft_in-access">选择</view>
        </navigator>
      </view>
      <view style="padding:16px;" wx:if="{{code>=0}}">
        <button class="weui-btn" type="primary" bindtap="bindInvite">没有找到？邀请加入+</button>
      </view>
    </view>
  </view>
</view>