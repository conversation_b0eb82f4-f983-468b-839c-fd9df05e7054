<template name="verifyLivingBody"><view wx:if="{{livingbody.isShowGuide && !livingbody.isShowCamera}}"><view id="livingbody-guide-title">实名核身验证流程</view><view class="livingbody-guide-imgview" wx:if="{{livingbodyType === 0}}"><image src="https://s.beta.gtimg.com/GodIdent/huiyan-ui-new/images-wx/hint-heshen-number.png"></image></view><view class="livingbody-guide-imgview" wx:elif="{{livingbodyType === 1}}"><image src="https://s.beta.gtimg.com/GodIdent/huiyan-ui-new/images-wx/hint-heshen-action.png"></image></view><view class="livingbody-guide-imgview" wx:else><image src="https://s.beta.gtimg.com/GodIdent/huiyan-ui-new/images/hint-heshen-silent.png"></image></view><view class="index-btn" id="livingbody-guide-btnview"><button type="primary" bindtap="livingbodyStartToRecord" hover-class="btn-hover" disabled="{{livingbody.isNotPrepareOk}}">开始识别</button></view><view id="livingbody-guide-showdialog"><text bindtap="switchLivingbodyDialog" style="padding:5px 10px">查看规范</text></view></view><view wx:if="{{livingbody.isShowCamera}}" style="position:absolute;top:0;bottom:0;width:100%;"><camera device-position="front" flash="off" binderror="ocrCameraError" bindstop="bindstop" id="livingbody-camera" style="position:absolute;top:0;bottom:0;width:100%;" wx:if="{{livingbodyType === 0}}"><cover-view class="livingbody-title {{livingbody.isInfinityDisplayHTTitle}}"><cover-view>{{livingbody.livingbodyTitle}}</cover-view></cover-view><cover-image src="http://beta.gtimg.com/GodIdent/huiyan/img/faceyzbg.png" class="livingbody-bg {{livingbody.isInfinityDisplayHTBottom}}"></cover-image><cover-view class="livingbody-number-pre {{livingbody.isInfinityDisplayHTNumberPre}}" wx:if="{{livingbody.isPrepare}}">准备读数...</cover-view><cover-view class="livingbody-number-hint {{livingbody.isInfinityDisplayHTNumberHint}}" wx:else><cover-view class="lvingbody-number-text {{livingbody.curNumberStatus[0]}}">{{livingbody.curNumber[0]}}</cover-view><cover-view class="lvingbody-number-text {{livingbody.curNumberStatus[1]}}">{{livingbody.curNumber[1]}}</cover-view><cover-view class="lvingbody-number-text {{livingbody.curNumberStatus[2]}}">{{livingbody.curNumber[2]}}</cover-view><cover-view class="lvingbody-number-text {{livingbody.curNumberStatus[3]}}">{{livingbody.curNumber[3]}}</cover-view></cover-view></camera><camera device-position="front" flash="off" binderror="ocrCameraError" bindstop="bindstop" id="livingbody-camera" wx:elif="{{livingbodyType === 1}}"><cover-view class="livingbody-title {{livingbody.isInfinityDisplayHTTitle}}"><cover-view>{{livingbody.livingbodyTitle}}</cover-view></cover-view><cover-image src="http://beta.gtimg.com/GodIdent/huiyan/img/faceyzbg.png" class="livingbody-bg {{livingbody.isInfinityDisplayHTBottom}}"></cover-image><cover-view class="livingbody-action-pre {{livingbody.isInfinityDisplayHTActionPre}}" wx:if="{{livingbody.isPrepare}}">准备做动作...</cover-view><cover-view class="livingbody-action-hint {{livingbody.isInfinityDisplayHTActionHint}}" wx:else>{{livingbody.livingbodyActionText}}</cover-view></camera><camera device-position="front" flash="off" binderror="ocrCameraError" bindstop="bindstop" id="livingbody-camera" wx:else><cover-view class="livingbody-title {{livingbody.isInfinityDisplayHTTitle}}"><cover-view>{{livingbody.livingbodyTitle}}</cover-view></cover-view><cover-image src="http://beta.gtimg.com/GodIdent/huiyan/img/faceyzbg.png" class="livingbody-bg {{livingbody.isInfinityDisplayHTBottom}}"></cover-image><cover-view class="livingbody-silent"><cover-view class="livingbody-silent-view"><cover-view class="livingbody-silent-prepare" wx:if="{{livingbody.isPrepare}}">准备录制</cover-view><cover-view class="livingbody-silent-hintone" wx:if="{{!livingbody.isPrepare}}">录制中</cover-view><cover-view class="livingbody-silent-hinttwo" wx:if="{{!livingbody.isPrepare}}">{{livingbody.livingbodySilentText}}</cover-view></cover-view></cover-view></camera></view><view wx:if="{{livingbody.isShowProcess && !livingbody.isShowGuide && !livingbody.isShowCamera}}" id="livingbody-process"><view class="scan"><image src="http://beta.gtimg.com/GodIdent/huiyan/img/loadding-white.gif"></image></view><view id="livingbody-process-m"><progress percent="{{livingbody.uploadProcess}}" color="#2473e9" backgroundcolor="#cbcbcb" stroke-width="6"></progress></view><view id="livingbody-process-title">系统识别中...</view></view><view class="verify-footer" wx:if="{{!livingbody.isShowCamera && (livingbody.isShowGuide || livingbody.isShowProcess)}}"><view class="verify-footer-logo" style="margin:6rpx 0;" wx:if="{{!page.index.isHideTipsLogo}}"><image src="http://beta.gtimg.com/GodIdent/huiyan/img/hint-logo.png"></image></view></view><view class="js_dialog" id="iosDialog1" style="opacity: 1;" wx:if="{{livingbody.isShowDialog}}"><view class="weui-mask"></view><view class="weui-dialog style3" id="livingbody-dialog-main"><view class="weui-dialog__hd" id="livingbody-dialog-title"><strong class="weui-dialog__title">视频录制规范</strong></view><view class="weui-dialog__bd" id="livingbody-dialog-bg"><image src="https://s.beta.gtimg.com/GodIdent/huiyan-ui-new/images/v1videohint.png"></image><view class="weui-btn_area"><button type="default" size="mini" plain="true" bindtap="switchLivingbodyDialog">我知道了</button></view></view></view></view><view wx:if="{{livingbody.showTestVideo}}"><video id="myVideo" src="{{livingbody.video_src}}" poster="{{livingbody.video_preview}}" controls style="width:300px;height:300px;"></video></view></template>