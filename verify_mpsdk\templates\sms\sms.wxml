<template name="verifySms"><view class="verify-gray-container"><view id="sms-top"><view id="sms-top-phone" class="sms-top-sec"><view class="sms-ts-img"><image src="http://beta.gtimg.com/GodIdent/huiyan/img/icon-phonenum.png"></image></view><view class="sms-ts-input"><input type="number" auto-focus="true" name="phoneNum" bindinput="phoneNumChanged" maxlength="11" placeholder="请输入手机号码"/></view><view wx:if="{{sms.is60sGap}}"><view class="sms-ts-btn">重新发送 ({{sms.gapSec}}s)</view></view><view wx:else><view class="sms-ts-btn sms-ts-btn-enable" wx:if="{{sms.isEnableSendSms}}" bindtap="sendVerifyCodeReq">{{sms.sendSmsTtitle}}</view><view class="sms-ts-btn" wx:else>{{sms.sendSmsTtitle}}</view></view></view><view class="sms-top-sec"><view class="sms-ts-img"><image src="http://beta.gtimg.com/GodIdent/huiyan/img/icon-yzm.png"></image></view><view class="sms-ts-input"><input type="number" name="verifyCode" maxlength="4" placeholder="请输入验证码" bindinput="verifyCodeChanged"/></view></view></view><view class="hint-error">{{sms.hintError}}</view><view class="index-btn"><button type="primary" bindtap="smsToNext" disabled="{{sms.isForbiddenSmsBtn}}" hover-class="btn-hover">下一步</button></view><view class="verify-footer"><view class="verify-footer-logo" style="margin:6rpx 0;" wx:if="{{!page.index.isHideTipsLogo}}"><image src="http://beta.gtimg.com/GodIdent/huiyan/img/hint-logo.png"></image></view></view></view></template>