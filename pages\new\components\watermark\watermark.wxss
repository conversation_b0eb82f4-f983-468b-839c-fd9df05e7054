.watermark {
  position: fixed;
  width: 200vw;
  height: 100vh;
  /* top: -20vw;  */
  left: -50vw; 
  color: gray;
  font-size: 14px;
  opacity: 0.3;
  z-index: 1000000;
  pointer-events: none;
  transform: rotate(-20deg);
}

.watermark-col {
  display: inline-block;
  padding: 80rpx 80rpx;
}

.watermark-row {
  white-space: nowrap;
}

.watermark-row:nth-child(2n+1) {
  transform: translateX(10%);
}