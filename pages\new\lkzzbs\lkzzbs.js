// pages/new/zkdj/zkdj.js
const http = require("../utils/httpUtils.js");
const FormData = require("../utils/formData.js");
var user = require('../utils/user.js')
var app = getApp();
var url = app.globalData.requestUrl;
var columnsArr = app.globalData.columnsArr;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    disabled: false,
    identitytype: false,
    userInfo: {},
    data_type: 1,
    company_id: '',
    rent_unrent_time: '',
    face_img_file: '',
    identity: '',
    address: '',
    area_location_id: '',
    area_location_name: '',
    company_name: '',
    phone: '',
    detail_address: '',
    name: '',
    house_type: '',
    columnsArr: [],
    arrindex: [0, 0],
    defaultarray: [],
    bl1: '',
    face_file_name: ''
  },
  formSubmit: function (e) {
    console.log(this.data.identitytype);
    console.log(e.detail.value)
    let that = this;
    if (e.detail.value.name == '') {
      wx.showToast({
        title: "请填写姓名",
        icon: "none",
      });
      return false;
    }
    if (e.detail.value.identity == '') {
      wx.showToast({
        title: "请填写身份证号",
        icon: "none",
      });
      return false;
    }
    if(!this.data.identitytype) {
      wx.showToast({
        title: "请填写正确的身份证号",
        icon: "none",
      });
      return false;
    }
    if (e.detail.value.phone == "") {
      wx.showToast({
        title: "请输入联系电话",
        icon: "none",
      });
      return false;
    }
    if (e.detail.value.detail_address == "") {
      wx.showToast({
        title: "请输入详细地址",
        icon: "none",
      });
      return false;
    }
    if (e.detail.value.area_location_name == "") {
      wx.showToast({
        title: "请选择社区派出所",
        icon: "none",
      });
      return false;
    }
    if (that.data.face_img_file == '') {
      wx.showToast({
        title: "请上传头像",
        icon: "none",
      });
      return false;
    }


    // 验证电话格式
    if (!/^(\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}$/.test(e.detail.value.phone)) {
      wx.showToast({
        title: "手机号码有误",
        duration: 2000,
        icon: "none",
      });

      return false;
    }
    that.setData({
      disabled: true
    })
 
    let formData = new FormData();
    formData.append('glm', that.data.area_location_id)
    formData.append('xzdxz', e.detail.value.detail_address)
    formData.append("xm", e.detail.value.name);
    formData.append('sfzh', e.detail.value.identity)
    formData.append('lxdh', e.detail.value.phone)
    formData.appendFile('face_img_file', that.data.face_img_file[0].path, that.data.face_file_name)
    formData.append('bl1', e.detail.value.bl1)



    let datas = formData.getData();
    wx.request({
      method: 'post',
      url: 'https://zzsbapi.jxjkga.cn/storemgmt/base/ldrkPush',
      header: {
        token: wx.getStorageSync('token'),
        'content-type': datas.contentType
      },
      data: datas.buffer,
      success(res) {
        if (res.data.success == 1) {

          wx.showToast({
            title: '提交成功',
            icon: 'success',
            duration: 2000,
            success: function () {
             setTimeout(() => {
              wx.switchTab({
                url: '../homeNew/homeNew'
              })
             }, 1000);
            }
          })
        } else {
          that.setData({
            disabled: false
          })
          wx.showToast({
            title: '提交失败',
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },
  getface: function () {
    let _this = this;
    wx.chooseImage({
      count: 1,
      success(res) {
        //此处会返回图片暂存路径和文件大小
        const data = res.tempFiles;
        console.log(data,res,res.tempFilePaths[0].split('/').pop())
        _this.setData({
          face_img_file: data,
          face_file_name: res.tempFilePaths[0].split('/').pop()
        })
      }
    })
  },

  getphone: function (e) {
    this.setData({
      phone: e.detail.value
    })
  },

  getdetail_address: function (e) {
    this.setData({
      detail_address: e.detail.value
    })
  },
  getdetail_bl1: function (e) {
    this.setData({
      bl1: e.detail.value
    })
  },


  getidentity: function (e) {
    this.setData({
      identity: e.detail.value
    })
    this.verifyIdentity_PD(e.detail.value)
  },
  getname: function (e) {
    this.setData({
      name: e.detail.value
    })
  },
  // ckldClick 查看流动人口跳转页面
  ckldClick() {
    // 页面跳转 ---
    wx.switchTab({
      url: '/pages/new/homeNew/homeNew'
    })
  },

  _onDelFace(e) {
    // 获取图片索引  获取图片文件流
    let idx = e.currentTarget.dataset.idx;
    let delFile = this.data.face_img_file[idx];
    console.log(delFile);
    this.data.face_img_file.splice(idx, 1);
    this.setData({
      face_img_file: this.data.face_img_file
    })
  },

  bindMultiPickerChange: function (e) {
    console.log('11111picker发送选择改变，携带值为', e.detail.value)
    let num = e.detail.value
    this.setData({
      arrindex: e.detail.value,
    })
    console.log(this.defaultarray[0][num[0]].text, this.defaultarray[1][num[1]].text);
    console.log(num, this.defaultarray);
    this.setData({
      area_location_name: this.defaultarray[0][num[0]].text + ',' + this.defaultarray[1][num[1]].text,
      area_location_id: this.defaultarray[1][num[1]].id
    })
    console.log(this.defaultarray[1][num[1]].id, 'area_location_id');

  },

  getAreaData() {
    let that = this
    http.get('base/getAreaCodeList', {
      parent_id: 0,
    }, res => {
      if (res.data.success == 1) {
        let tempData = res.data.data;
        let arr = [];
        tempData.forEach(item => {
          arr.push({
            id: item.code,
            text: item.name,
          });
        });
        this.AreaCodeList(arr[0].id)
        let arrList = []
        arrList[0] = arr
        arrList[1] = []
        that.defaultarray = arrList
        that.setData({
          defaultarray: arrList
        })
      }
    });
  },
  AreaCodeList(val) {
    let that = this
    http.get('base/getAreaCodeList', {
      parent_id: val,
    }, res => {
      if (res.data.success == 1) {
        let tempData = res.data.data;
        let arr = [];
        tempData.forEach(item => {
          arr.push({
            id: item.code,
            text: item.name,
          });
        });
        let arrList = []
        that.defaultarray[1] = arr
        arrList = that.defaultarray
        that.setData({
          defaultarray: arrList
        })
        console.log(that.defaultarray, 'shuju');
      }
    });
  },
  bindMultiPickerColumnChange(e) {
    console.log(e, '社区滑动', this.defaultarray[0][e.detail.value]);
    if (e.detail.column == '0') {
      this.AreaCodeList(this.defaultarray[0][e.detail.value].id)
    }
  },

  // 校验身份证
  verifyIdentity_PD(val) {
    var that = this;
    if (val.length == 18) {
      let param = new FormData() // 创建form对象
      param.append('identity', val) // 通过append向form对象添加数据
      let datas = param.getData();
      wx.request({
        method: 'post',
        url: 'https://zzsbapi.jxjkga.cn/storemgmt/base/verifyIdentity',
        header: {
          token: wx.getStorageSync('token'),
          'content-type': datas.contentType
        },
        data: datas.buffer,
        success(res) {
          if (res.data.success == 1) {
            wx.showToast({
                title: '身份证格式正确',
                icon: 'success',
                duration: 2000
              }),
              that.setData({
                identitytype: true
              });
          } else {
            wx.showToast({
                title: '身份证格式错误',
                icon: 'none',
                duration: 2000
              }),
              that.setData({
                identitytype: false
              });
          }
        },
        fail(res) {
          console.log(res)
        }
      })
    } else {
      wx.showToast({
          title: '身份证格式错误',
          icon: 'none',
          duration: 2000
        }),
        that.setData({
          identitytype: false
        });
    }
  },
  // 获取派出所/社区和小区信息
// /base/getLdrkGlmByQRCode
GlmByQRCode(val,v){
  let that = this
  http.get('base/getLdrkGlmByQRCode', {
    location_id:val,
    qrcode_type: v,
  }, res => {
    if (res.data.success == 1) {
      // address: "新月公寓"
      // glm_code: "330402008035"
      // glm_name: "城西派出所,新月社区"
      console.log(res,'12313123');
      that.setData({
        area_location_name: res.data.data.glm_name
      })
      that.setData({
        area_location_id: res.data.data.glm_code
      })
      that.setData({
        detail_address: res.data.data.address
      })
      that.setData({
        bl1: res.data.data.company_name
      })
    }
  });
},
 
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (query) {
    console.log('999');
    let self = this;
    let token = wx.getStorageSync('token');
    let scene = decodeURIComponent(query.scene)
    // debugger
    if (scene != 'undefined' && scene != '')
      wx.setStorageSync('lkzzbs', scene);

    wx.getPrivacySetting({
      success: res => {
        console.log(res) // 返回结果为: res = { needAuthorization: true/false, privacyContractName: '《xxx隐私保护指引》' }
        if (res.needAuthorization) {
          wx.setStorageSync('soucePage', 'lkzzbs');
          // 需要弹出隐私协议
          this.alertSix();
        } else {
          // console.log(这是二维码)
          if (token) {
              //当前是登入状态
              this.getAreaData()
              let lkzzsb_param = wx.getStorageSync('lkzzbs');
              if (lkzzsb_param!= 'undefined' && lkzzsb_param != '') {
                let paramArray = lkzzsb_param.split("&");
                let IsValue = {}
                IsValue.l = paramArray[0].split("=")[1];
                IsValue.q = paramArray[1].split("=")[1];
                if (IsValue.l && IsValue.q) {
                  this.GlmByQRCode(IsValue.l,IsValue.q)
                }
                //读取完成,清空缓存
                wx.setStorageSync('lkzzbs', '');
              }
          } else {
            wx.showModal({
              title: '提示',
              content: '请先注册并登录',
              success(res) {
                if (res.confirm) {
                  wx.redirectTo({
                    url: '/pages/new/user/login'
                  })
                } else if (res.cancel) {
                  wx.showToast({
                    title: '登录失败',
                    icon: 'none',
                    duration: 2000
                  })
                }
              }
            })
          }
        }
      },
      fail: () => {},
      complete: () => {}
    });



    /*
    let self = this;
    let token = wx.getStorageSync('token');
    let scene = decodeURIComponent(query.scene)
    let paramArray = scene.split("&");
    console.log(paramArray)
    let IsValue = {}
    if (paramArray !='undefined') {
      console.log(paramArray,'paramArray');
      IsValue.l = paramArray[0].split("=")[1];
      IsValue.q = paramArray[1].split("=")[1];
      console.log(IsValue)
      wx.getPrivacySetting({
        success: res => {
          console.log(res) // 返回结果为: res = { needAuthorization: true/false, privacyContractName: '《xxx隐私保护指引》' }
          if (res.needAuthorization) {
            wx.setStorageSync('paramArray', IsValue);
            this.alertSix();
          }}})
      // this.GlmByQRCode(IsValue.l)
    }else{
      IsValue = wx.getStorageSync('paramArray')
      console.log(IsValue,'IsValue');
    }

    wx.setStorageSync('soucePage', 'lkzzbs');
  
    if (token) {
        if (IsValue.l || IsValue.q) {
          this.GlmByQRCode(IsValue.l)
        }
      this.getAreaData()
    } else {
      wx.showModal({
        title: '提示',
        content: '请先注册并登录',
        success(res) {
          if (res.confirm) {
            wx.redirectTo({
              url: '/pages/new/user/login'
            })
          } else if (res.cancel) {
            wx.switchTab({
              url: '../homeNew/homeNew'
            })
            wx.showToast({
              title: '登录失败',
              icon: 'none',
              duration: 2000
            })
          }
        }
      })
    }
*/
  },
  // 用户隐私信息认证代码
  alertSix() {
    console.log('没有获取到SixTip进行路由跳转');
    wx.redirectTo({
      url: '/pages/Bullet/index/index'
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },


})