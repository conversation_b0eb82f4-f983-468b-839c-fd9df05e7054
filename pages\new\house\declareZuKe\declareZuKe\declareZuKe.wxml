<!--pages/new/shop/info.wxml-->
<watermark></watermark>
<view>
  <swiper style='height:{{swiper.height}};max-height:200px;' indicator-dots="{{swiper.indicatorDots}}" autoplay="{{swiper.autoplay}}" interval="{{swiper.interval}}" duration="{{swiper.duration}}">
    <block wx:for="{{swiper.image}}" wx:key="this">
      <swiper-item>
        <image src="{{item}}" class="slide-image" mode="aspectFill" model="widthFix" bindload='bindSwiperHeight' />
      </swiper-item>
    </block>
  </swiper>
</view>
<view class="page">
  <view class="page__bd">
    <view class="weui-cells__title">小区坐落</view>
    <view class="weui-cells weui-cells_after-title">
      <view class="weui-cell">
        <view class="weui-cell__bd">
          <label class="weui-textarea">{{house.location_area_name}}{{house.address}}</label>
        </view>
      </view>
    </view>
    <view style="display:none;" class="weui-cells__title">房东信息</view>
    <view style="display:none;" class="weui-panel weui-panel_access myweui-row">
      <view class="weui-panel__bd myweui-row-body">
        <navigator url="" class="weui-media-box weui-media-box_appmsg" hover-class="weui-cell_active">
          <view class="weui-media-box__hd weui-media-box__hd_in-appmsg">
            <image class="weui-media-box__thumb" src="{{house.owner_photo}}" />
          </view>
          <view class="weui-media-box__bd weui-media-box__bd_in-appmsg">
            <view class="weui-media-box__title">{{house.owner_realname}}
            </view>
            <view class="weui-media-box__desc">
              <label class='product-sku'>{{house.owner_mobilephone_number}} </label>
            </view>
          </view>
        </navigator>
      </view>
    </view>
    <view   class="weui-cells__title">租赁时间</view>
    <view  class="weui-panel weui-panel_access myweui-row">
      <!-- <view  class="weui-panel__bd myweui-row-body">
        <navigator url="" class="weui-media-box weui-media-box_appmsg" hover-class="weui-cell_active">
          <view class="weui-media-box__hd weui-media-box__hd_in-appmsg">
            <image class="weui-media-box__thumb" src="{{renter.member_photo}}" />
          </view>
          <view class="weui-media-box__bd weui-media-box__bd_in-appmsg">
            <view class="weui-media-box__title">{{renter.member_realname}}
            </view>
            <view class="weui-media-box__desc">
              <label class='product-sku'>{{renter.member_mobilephone_number}} </label>
            </view>
          </view>
        </navigator>
      </view> -->
      <view   class="weui-cells weui-cells_after-title">
        <!-- <view class="weui-cell ">
          <view class="weui-cell__hd">
            <view class="weui-label">房间号</view>
          </view>
          <view class="weui-cell__bd">
            <input class="weui-input" bindinput="bindRoomInfo" placeholder="房间号" value="{{renter.room_number}}" />
          </view>
        </view> -->
        <view class="weui-cell ">
          <view class="weui-cell__hd">
            <view class="weui-label">租赁时间起</view>
          </view>
          <view class="weui-cell__bd">
            <picker mode="date" value="{{date}}" bindchange="bindDateBeginChange">
              <view class="weui-input">{{renter.live_date_begin}}</view>
            </picker>
          </view>
        </view>
        <view class="weui-cell ">
          <view class="weui-cell__hd">
            <view class="weui-label">租赁时间止</view>
          </view>
          <view class="weui-cell__bd">
            <picker mode="date" value="{{date}}" bindchange="bindDateEndChange">
              <view class="weui-input">{{renter.live_date_end}}</view>
            </picker>
          </view>
        </view>
      </view>
    </view>
    <view class="weui-cells__title">备注</view>
    <view class="weui-cells weui-cells_after-title">
      <view class="weui-cell">
        <view class="weui-cell__bd">
          <textarea class="weui-textarea" bindinput="bindRemark" placeholder="请输入备注" style="height: 3.3em" value="{{renter.remark}}" />
          <view  wx:if="{{false}}" class="weui-textarea-counter">0/200</view>
        </view>
      </view>
    </view>
    <view style="padding:16px;">
      <button class="weui-btn" type="primary" bindtap="bindSave">保存</button>
    </view>
  </view>
</view>