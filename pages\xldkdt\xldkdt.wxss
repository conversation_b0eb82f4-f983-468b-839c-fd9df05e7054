.map_container {
  height: 80%;
  width: 100%;
}

.map {
  height: 100%;
  width: 100%;
}

.yuantu_gy {
  position: absolute;
  top: 20px;
  align-items: center;
  width: 100px;
  height: 100px;
  border-radius: 50px;
  opacity: 1;
  background: radial-gradient(50% 50% at 50% 50%, rgba(119, 161, 235, 0) 0%, rgba(104, 166, 232, 0.6) 100%);
}
.yuantu_di{
  position: absolute;
  top: 20px;
width: 100px;
height: 100px;
border-radius: 50px;
opacity: 1;
background: #0068FF;
filter: blur(2px);
}

.icon {
  width: 54rpx;
  height: 54rpx;
}

.tabBar {
  width: 100%;
  position: fixed;
  bottom: 0;
  margin-left: -4rpx;
  background: #F7F7FA;
  font-size: 24rpx;
  color: #8A8A8A;
  box-shadow: 3rpx 3rpx 3rpx 3rpx #aaa;
  padding: 10px 0 30px;
  z-index: 9999;
}

.tabBar-item {
  float: left;
  width: 33%;
  text-align: center;
  overflow: hidden;
}

/*当前字体颜色*/
.tabBartext {
  color: red;
}

.navigator-hover {
  background-color: rgba(0, 0, 0, 0);
}