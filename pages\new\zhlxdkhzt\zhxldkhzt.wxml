<watermark></watermark>
<view class="map_container">
  <map id='map' class='map' longitude='{{longitude}}' polyline="{{polyline}}" latitude='{{latitude}}' scale='{{scale}}'
    markers='{{markers}}'  bindmarkertap='bindmarkertap' show-location></map>
</view>
<view class="module">
  <view style="display: flex;justify-content: space-between; ">
    <view class="weui-mediain-appmsg">
                        <image class="weui-media-box__thumb" src="https://zzsbapi.jxjkga.cn/wxzzsb/new/qidian.png" />
                    </view>
    <view style="flex-direction:column;width: 45%;">
      <view class='hy_Num'>起点位置</view>
      <view class='hy_text'>{{parentoptions.sign_in_time}}</view>
    </view>
    <view class="weui-mediain-appmsg">
                        <image class="weui-media-box__thumb" src="https://zzsbapi.jxjkga.cn/wxzzsb/new/zhongdian.png" />
                    </view>
    <view style="flex-direction:column;width: 45%;">
      <view class='hy_Num'>终点位置</view>
      <view class='hy_text'>{{parentoptions.sign_out_time}}</view>
    </view>
  </view>
  <view style="display: flex;justify-content: space-between;padding-top: 30px; ">
    <view style="flex-direction:column;width: 45%;">
      <view class='hy_Num'>巡逻时长: {{parentoptions.online_duration}}小时 </view>
    </view>

    <view style="flex-direction:column;width: 45%;">
      <view class='hy_Num'>巡逻里程: {{parentoptions.patrol_distance}} </view>
    </view>
  </view>
</view>