/* pages/new/xlxjrw/xlxjrw.wxss */


.container {
  height: 100vh;
  font-size: 16px;
  font-weight: 900;
  padding: 4px;
  background-color: #f6f6f6;
}

.module {
  margin: 10px;
  font-size: 16px;
  font-weight: 900;
  border-radius: 15px;
  padding: 10px;
  background-color: #fff;
}
.list_css{
  display: flex;
  justify-content: space-around;
  margin: 10px;
  font-size: 16px;
  font-weight: 900;
  border-radius: 15px;
  padding: 10px;
  background-color: #fff;
}

.weui-input {
  width: 90%;
  background-color: #e9e9e9;
  margin-left: 20px;
  margin-top: 10px;
  height: 30px;
}
.weui-time {
  width: 90%;
  background-color: #e9e9e9;
  margin-left: 20px;
}

.title_name {
  padding: 10px 0 0 0;
  text-align: center;
}

.p10 {
  padding: 10px 0;
}

.Ovalpage {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 200px;
}

.Ovalpage_img {
  position: absolute;
  width: 200px;
  height: 200px;
  background-color: #eeeeee00;
}

.hy_text {
  text-align: center;
  font-weight: 100;
  font-size: 18px;
}
.hy_Num{
  text-align: center;
  font-weight: 500;
  font-size: 24px;
}