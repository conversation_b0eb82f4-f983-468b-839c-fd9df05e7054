// pages/new/manage/view.js
var app = getApp();
var url = app.globalData.requestUrl;
var user = require('../../../pages/new/utils/user.js');
var common = require('../../../pages/new/utils/common.js');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    user: {},
    url: url,
    dataShopShow: false,
    dataHouseShow: false,
    dataShopTree: [],
    dataShopList: [],
    dataHouseTree: [],
    dataHouseList: []
  },
  getUserInfo: function(e) {
    var that = this;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/back/user/detail/byId?id=' + that.options.id,
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          var data = {
            user: that.data.user
          }
          data.user = res.data.data;
          that.setData(data);
          that.getDataHouseList();
          that.getDataShopList();
        } else {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },
  removeByValue: function(arr, val) {
    for (var i = 0; i < arr.length; i++) {
      if (arr[i].id == val.id) {
        arr.splice(i, 1);
        break;
      }
    }
  },
  bindDataHouseShow: function(e) {
    var temp = false;
    if (e.currentTarget.dataset.status == "1") {
      temp = true;
    }
    this.setData({
      dataHouseShow: temp
    });
  },
  bindDataShopShow: function(e) {
    var temp = false;
    if (e.currentTarget.dataset.status == "1") {
      temp = true;
    }
    this.setData({
      dataShopShow: temp
    });
  },
  bindDataHouseCancel: function(e) {
    this.bindSetDataHouse(e.currentTarget.dataset.item, 0);
  },
  bindDataHouseClick: function(e) {
    this.bindSetDataHouse(e.currentTarget.dataset.item, -1);
  },
  bindSetDataHouse: function(item, checked) {
    var dataHouseList = this.data.dataHouseList;
    var dataHouseTree = this.data.dataHouseTree;
    for (let i = 0; i < dataHouseList.length; i++) {
      if (dataHouseList[i].id == item.id) {
        if (checked == -1) {
          if (dataHouseList[i].checked == false) {
            dataHouseList[i].checked = true;
          } else {
            dataHouseList[i].checked = false;
          }
        } else if (checked == 1) {
          dataHouseList[i].checked = true;
        } else if (checked == 0) {
          dataHouseList[i].checked = false;
        } else {}
        break;
      }
    }
    dataHouseTree = common.toTree(dataHouseList);
    this.setData({
      dataHouseList: dataHouseList,
      dataHouseTree: dataHouseTree
    });
  },
  //
  bindDataShopCancel: function (e) {
    this.bindSetDataShop(e.currentTarget.dataset.item, 0);
  },
  bindDataShopClick: function (e) {
    this.bindSetDataShop(e.currentTarget.dataset.item, -1);
  },
  bindSetDataShop: function (item, checked) {
    var dataShopList = this.data.dataShopList;
    var dataShopTree = this.data.dataShopTree;
    for (let i = 0; i < dataShopList.length; i++) {
      if (dataShopList[i].id == item.id) {
        if (checked == -1) {
          if (dataShopList[i].checked == false) {
            dataShopList[i].checked = true;
          } else {
            dataShopList[i].checked = false;
          }
        } else if (checked == 1) {
          dataShopList[i].checked = true;
        } else if (checked == 0) {
          dataShopList[i].checked = false;
        } else { }
        break;
      }
    }
    dataShopTree = common.toTree(dataShopList);
    this.setData({
      dataShopList: dataShopList,
      dataShopTree: dataShopTree
    });
  },
  getDataHouseList: function(e) {
    var that = this;
    var currentUserDataPermission = that.data.user.data_permission == null ? [] : that.data.user.data_permission.split(',');
    var currentUserDataPermissionHouse = [];
    for (let i = 0; i < currentUserDataPermission.length; i++) {
      if (currentUserDataPermission[i].indexOf('xq') != -1) {
        currentUserDataPermissionHouse.push(currentUserDataPermission[i].replace('xq',''));
      }
    }
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/base/area/list?parent_id=330499',
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          var dataHouseList = that.data.dataHouseList;
          var dataHouseTree = that.data.dataHouseTree;
          for (let i = 0; i < res.data.data.length; i++) {
            console.log(res.data.data[i]);
            let checked = false;
            if (currentUserDataPermissionHouse.indexOf(String(res.data.data[i].id)) != -1) {
              checked = true;
            }
            dataHouseList.push({
              id: res.data.data[i].id,
              parent_id: res.data.data[i].parent_id,
              name: res.data.data[i].name,
              checked: checked
            });
          }
          dataHouseTree = common.toTree(dataHouseList);
          that.setData({
            dataHouseList: dataHouseList,
            dataHouseTree: dataHouseTree
          });
        } else {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },
  getCommunityList: function(e) {
    var that = this;
    let parent_id = e.currentTarget.dataset.item.id;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/base/community/area?parent_id=' + parent_id,
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          var dataHouseList = that.data.dataHouseList;
          var dataHouseTree = that.data.dataHouseTree;
          for (let i = 0; i < res.data.data.length; i++) {
            if (common.getObjectsFromJson(dataHouseList, 'id', res.data.data[i].id).length > 0) {
              that.removeByValue(dataHouseList, res.data.data[i]);
              console.log(dataHouseList);
              console.log(res.data.data[i].id);
              that.setData({
                dataHouseList: dataHouseList
              });
            } else {
              // let parent_id = 0;
              // if (level == 1) {
              //   parent_id = area_location_id;
              // } else {
              //   parent_id = res.data.data[i].parent_id;
              // }
              dataHouseList.push({
                id: res.data.data[i].id,
                parent_id: res.data.data[i].parent_id,
                name: res.data.data[i].name,
                checked: false
              });
            }
          }
          dataHouseTree = common.toTree(dataHouseList);
          that.setData({
            dataHouseList: dataHouseList,
            dataHouseTree: dataHouseTree
          });
        } else {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },
  getDataShopList: function (e) {
    var that = this;
    var currentUserDataPermission = that.data.user.data_permission == null ? [] : that.data.user.data_permission.split(',');
    var currentUserDataPermissionShop = [];
    for (let i = 0; i < currentUserDataPermission.length; i++) {
      if (currentUserDataPermission[i].indexOf('dp') != -1) {
        currentUserDataPermissionShop.push(currentUserDataPermission[i].replace('dp', ''));
      }
    }
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/base/area/list?parent_id=330499',
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          var dataShopList = that.data.dataShopList;
          var dataShopTree = that.data.dataShopTree;
          for (let i = 0; i < res.data.data.length; i++) {
            let checked = false;
            if (currentUserDataPermissionShop.indexOf(String(res.data.data[i].id)) != -1) {
              checked = true;
            }
            dataShopList.push({
              id: res.data.data[i].id,
              parent_id: res.data.data[i].parent_id,
              name: res.data.data[i].name,
              checked: checked
            });
          }
          dataShopTree = common.toTree(dataShopList);
          that.setData({
            dataShopList: dataShopList,
            dataShopTree: dataShopTree
          });
        } else {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },
  getAreaList: function(e) {
    var that = this;
    let area_location_id = e.currentTarget.dataset.item.id;
    let level = e.currentTarget.dataset.level;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/base/community/area?area_location_id=' + area_location_id + '&level=' + level,
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          var dataHouseList = that.data.dataHouseList;
          var dataHouseTree = that.data.dataHouseTree;
          // dataHouseList.push({
          //   id: 330499,
          //   parent_id: 3304,
          //   name: '经开',
          //   checked: false
          // });
          for (let i = 0; i < res.data.data.length; i++) {
            if (common.getObjectsFromJson(dataHouseList, 'id', res.data.data[i].id).length > 0) {
              that.removeByValue(dataHouseList, res.data.data[i]);
              console.log(dataHouseList);
              console.log(res.data.data[i].id);
              that.setData({
                dataHouseList: dataHouseList
              });
            } else {
              let parent_id = 0;
              if (level == 1) {
                parent_id = area_location_id;
              } else {
                parent_id = res.data.data[i].parent_id;
              }
              dataHouseList.push({
                id: res.data.data[i].id,
                parent_id: parent_id,
                name: res.data.data[i].name,
                checked: false
              });
            }
          }
          dataHouseTree = common.toTree(dataHouseList);
          that.setData({
            dataHouseList: dataHouseList,
            dataHouseTree: dataHouseTree
          });
        } else {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },
  bindSave: function(e) {
    var that = this;
    var dataPermission = [];
    console.log(that.data.dataHouseList.length);
    for (let i = 0; i < that.data.dataHouseList.length; i++) {
      console.log(that.data.dataHouseList[i]);
      if (that.data.dataHouseList[i].checked) {
        dataPermission.push('xq' + that.data.dataHouseList[i].id);
      }
    }
    for (let i = 0; i < that.data.dataShopList.length; i++) {
      if (that.data.dataShopList[i].checked) {
        dataPermission.push('dp' + that.data.dataShopList[i].id);
      }
    }
    console.log(dataPermission)
    dataPermission = dataPermission.join(',');
    wx.request({
      method: 'POST',
      url: url + '/storemgmt/back/data-permission/grant?user_id=' + that.options.id + '&data_permission=' + dataPermission,
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          wx.showModal({
            title: '提示',
            content: '保存成功',
            showCancel: false,
            success(res) {
              if (res.confirm) {
                wx.navigateTo({
                  url: '/pages/new/manage/list',
                })
              }
            }
          })
        } else {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.getUserInfo();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

 
})