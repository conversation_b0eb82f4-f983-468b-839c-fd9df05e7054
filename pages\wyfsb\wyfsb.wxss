/* pages/wyfsb/wyfsb.wxss */

.post_absolute {
  position: absolute;
  top: 200px;
  width: 94%;
  height: 100%;
  margin-left: 3%;
  background: #fff;
  border-radius: 10px;
}

.wztitle {
  position: absolute;
  top: 120px;
  width: 90%;
  margin-left: 5%;
  font-size: 14px;
  font-weight: 700;
  color: #fff;
  text-align: left;
}

.bg_fff {
  background: #fff;
}

.border_r10 {
  border-radius: 10px;
}

.company-list {
  padding: 20px;
}

.company-item {
  margin-bottom: 10px;
}

.company-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.company-icon {
  flex: 0 0 60px;
}

.company-info {
  flex: 1;
  padding-left: 10px;
}

.company-arrow {
  flex: 0 0 30px;
  text-align: right;
}

.p15 {
  padding: 15px;
}

.fz15 {
  font-size: 15px;
}

.fw {
  font-weight: bold;
}

.t_l {
  text-align: left;
}

.t_r {
  text-align: right;
}

.pt15 {
  padding-top: 15px;
}

.pt20 {
  padding-top: 20px;
}

.mt5 {
  margin-top: 5px;
}

.huixian {
  height: 1px;
  background: #eee;
  margin: 10px 0;
}

.room-section {
  padding: 20px;
  text-align: center;
}

.room-picker {
  margin-bottom: 20px;
}

.picker-input {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  background: #fff;
}

.picker-label {
  font-weight: bold;
  margin-right: 10px;
}

.picker-value {
  flex: 1;
  text-align: left;
  color: #333;
}

.refresh-btn {
  height: 25px;
  line-height: 25px;
  font-size: 12px;
  margin-bottom: 20px;
  background: #5062FF;
  color: #fff;
  border-radius: 5px;
}

.qr-code {
  margin: 20px 0;
}

.qr-tip {
  color: red;
  font-size: 14px;
  margin: 10px 0;
}

.qr-expire {
  font-size: 14px;
  color: #666;
}

.red {
  color: red;
}

.p10 {
  padding: 10px;
}

.empty-state {
  text-align: center;
  padding: 50px 20px;
  color: #999;
}

.iconfont {
  font-family: "iconfont";
}

.icon-arrow-right::before {
  content: ">";
}

.icon-arrow-down::before {
  content: "▼";
}

