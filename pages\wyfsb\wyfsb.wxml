<!--pages/wyfsb/wyfsb.wxml-->
<view>
  <view>
    <image mode="aspectFill" style="width: 100%;" src="https://zzsbapi.jxjkga.cn/wxzzsb/new/wyfzxdj.png" />
  </view>

  <view class="wztitle">
    <view>经开公安提醒您：</view>
    <view>&emsp;&emsp;入住房间请如实登记住宿人证件信息，住宿人与登记人要相符，登记人数与实际登记人数要相符。</view>
  </view>

  <view class="post_absolute">
    <view class="bg_fff border_r10">
      <view class="company-list">
        <view class="company-item" wx:for="{{CompanyList}}" wx:key="index" bindtap="djClick" data-index="{{index}}">
          <view class="company-content">
            <view class="company-icon">
              <view class="p15 fz15 fw t_l">
                <image style="width: 40px; height: 40px;" src="{{item.img}}" />
              </view>
            </view>
            <view class="company-info">
              <view class="pt15 fz15 fw t_l">{{item.name}}</view>
              <view class="t_l">{{item.text}}</view>
              <view class="huixian mt5"></view>
            </view>
            <view class="company-arrow t_r">
              <text class="iconfont icon-arrow-right fw fz15 pt20" style="color: #5062FF;"></text>
            </view>
          </view>
        </view>
      </view>

      <view class="room-section" wx:if="{{BookOnlineRoomList.length > 0}}">
        <view class="room-picker">
          <picker mode="selector" range="{{BookOnlineRoomList}}" range-key="text" value="{{fhIndex}}" bindchange="onRoomChange">
            <view class="picker-input">
              <text class="picker-label">房间号</text>
              <text class="picker-value">{{fhObj.text || '选择房间号'}}</text>
              <text class="iconfont icon-arrow-down"></text>
            </view>
          </picker>
        </view>

        <button class="refresh-btn" bindtap="sxewm_Click">刷新二维码</button>

        <view class="qr-code">
          <image style="width: 240px; height: 240px;" src="{{visitCommunity.pass_code}}" />
        </view>

        <view class="qr-tip fw red">
          可用于供梯控设备扫描，前往该二维码对应的房屋楼层。
        </view>

        <view class="qr-expire p10 fw">
          将于 {{visitCommunity.end_time}} 失效
        </view>
      </view>

      <view class="empty-state" wx:if="{{BookOnlineRoomList.length === 0}}">
        <text>暂无房源</text>
      </view>
    </view>
  </view>
</view>