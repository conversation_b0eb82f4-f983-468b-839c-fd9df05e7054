/* pages/policeTerminal/policeTerminsl.wxss */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.content .topNav {
  height: 120rpx;
  background: #166BE9;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}
.content .topNav .modeSwitch {
  padding-top: 10px;
  font-size: 20rpx;
  color: white;
}
.content .topNav .modeSwitch image {
  height: 60rpx;
  width: 60rpx;
}
.content .topNav .search {
  margin-right: 30rpx;
  border-radius: 30rpx;
  height: 98rpx;
  width: 80%;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  background-color: #8BB5F4;
}
.content .topButton {
  height: 200rpx;
  background: linear-gradient(#166BE9, #74A8F2);
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}
.content .topButton .item image {
  width: 100rpx;
  height: 120rpx;
  z-index: 10;
}
.content .topContent image {
  width: 100%;
}
.content .topContent .function {
  position: absolute;
  left: 5%;
  top: 580rpx;
  width: 90%;
  height: 200rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.9);
}
.content .topContent .function .title {
  padding: 20rpx;
  font-weight: bold;
}
.content .topContent .function .functionBtn {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}
.content .topContent .function .functionBtn .item image {
  height: 100rpx;
  width: 100rpx;
}
.content .mainContent {
  background-color: #F7FAFF;
  width: 100%;
  height: 1700rpx;
}
.content .mainContent .mainBox {
  margin-left: 5%;
  width: 90%;
  height: 650rpx;
  background: white;
}
.content .mainContent .mainBox .mainTitle {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height:100rpx;
  padding-top: 10rpx;
}
/* .content .mainContent .mainBox .mainTitle  */
.option {
  display: inline-block;
  padding: 10rpx 45rpx;
  font-size: 30rpx;
  background-color: #F3F9FF;
  cursor: pointer;
  color: black;
}
.active {
  background-color: white;
}

.content .mainContent .mainBox .mainTitle .shezhi image {
  width: 50rpx;
  padding-top: 25rpx;
}
.content .mainContent .mainBox .main {
  display: flex;
  justify-content: space-around;
}
.content .mainContent .mainBox .main .item {
  background-color: #F3F9FF;
  padding: 20rpx;
  margin: 20rpx;
  width: 40%;
  height: 100rpx;
  border-radius: 10rpx;
  display: flex;
  justify-content: left;
}
.content .mainContent .mainBox .main .item image {
  width: 80rpx;
  padding-top: 10rpx;
}
.content .mainContent .mainBox .main .item .text {
  padding-top: 10rpx;
  padding-left: 15rpx;
}
.content .mainContent .mainBox .main .item .text .title {
  margin: 5rpx;
  font-size: 24rpx;
}
.content .mainContent .mainBox .main .item .text .wenzi {
  margin: 5rpx;
  padding-top: 10rpx;
  font-size: 18rpx;
  font-weight: 100;
}
.content .mainContent .mainBox .line {
  height: 1rpx;
  width: 90%;
  margin-left: 5%;
  margin-top: 30rpx;
  background: #ccc;
}
.content .mainContent .mainBox .functionBox {
  height: 100rpx;
  width: 95%;
  margin-left: 2.5%;
  margin-top: 30rpx;
  background-color: #F5F9FF;
  border-radius: 10rpx;
  display: flex;
  justify-content: left;
  align-items: center;
}
.content .mainContent .mainBox .functionBox image {
  width: 60rpx;
  height: 60rpx;
  padding: 10rpx 10rpx;
  margin-left: 5rpx;
}
.content .mainContent .mainBox .functionBox view {
  font-size: 18rpx;
  padding: 10rpx;
}
.content .mainContent .mainBox .bottomContent {
  font-weight: bold;
  height: 900rpx;
  width: 100%;
  margin-top: 80rpx;
  background-color: white;
}
.content .mainContent .mainBox .bottomContent .title {
  padding: 20rpx;
  margin: 10rpx;
  font-size: 38rpx;
}
.underlines {
  height: 1rpx;
  width: 90%;
  margin-left: 5%;
  margin-top: 30rpx;
  background: #ccc;
}
.alarm {
  position: relative;
}
.divright {
  position: absolute;
  color: white;
  font-size: 20rpx;
  background-color: red;
  padding: 8rpx;
  min-width: 20rpx;
  height: 20rpx;
  line-height: 20rpx;
  left: -50%;
  top: -10%;
  text-align: center;
  border-radius: 50%;
}
.divright2 {
  left: 150%;
  top: -30%;
}
.divright3 {
  left: 105%;
  top: -30%;
}
.table {
  width: 100%;
  display: table;
  border-collapse: collapse;
}
.table .th {
  text-align: center;
  display: table-cell;
}
.table .td {
  text-align: center;
  background: #FFFFFF;
  padding: 15rpx 0;
  display: table-cell;
}
.table .tr {
  display: table-row;
}
.functionguanli{
  /* background-color: #166BE9; */
  /*  */
  text-align: center;
  margin: 20rpx 0;
  font-weight: 900;
}
.function1{
  padding: 10rpx;
  margin: 20rpx 40rpx;
  display: flex;
  justify-content: space-between;
  font-weight: 800;
}
.function1 image{
  width: 40rpx;
  height: 40rpx;
  padding: 0 10rpx;
}
.submit{
  margin-top: 50rpx;
}
.small-placeholder {
  font-size: 12px; 
  color: white;
}