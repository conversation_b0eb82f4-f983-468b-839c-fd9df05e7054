// pages/new/zhxldk/zhxldk.js

const http = require("../new/utils/httpUtils.js");
Page({
  /**
   * 页面的初始数据
   */
  data: {
    dataList:[],
    selfList:{},
    page_size:10,
    page_num:1,
    PatrolMembers:{}
  },
  getPatrolRecordList(){
    let that = this
    http.get('company/getPatrolRecordList', {
      "page_size":this.data.page_size,//
      "page_num":this.data.page_num,//
    }, res => {
      if (res.data.success == 1) {
        let old_data = this.data.dataList
        let new_data = res.data.data.data_list
        console.log( res.data.data.data_list,' res.data.dataList');
        let arr4 =  old_data.concat(new_data)
        this.setData({
          dataList: this.deWeightFour(arr4) //concat()用于连接字段或字符
        })
        console.log(this.data.dataList ,'获取的数据');
      }
    })
  },
  deWeightFour(arr4) {
    var obj ={}
    arr4 = arr4.reduce(function(a, b) {
        obj[b.id] ? '' : obj[b.id] = true && a.push(b);
        return a;
    }, [])
    return arr4;
},
  getUserOnlinePatrol(){
    let that = this
    http.get('company/getUserOnlinePatrol', {
    }, res => {
      if (res.data.success == 1) {
        that.data.selfList = res.data.data
        that.setData({
          selfList:res.data.data 
        })
      }
    })
  },


  wdwClick: function (e) {
            wx.navigateTo({
              url: '../xldkdt/xldkdt?patrol_record_id='+e.currentTarget.id+'&type='+1
            })
  },
  zhxlClick: function (e) {
    let  that = this
    if (this.data.selfList ==null) {
      wx.showModal({
        title: '提示',
        content: '加入<'+that.data.dataList[e.currentTarget.id].patrol_name+'>团队!',
        success(res) {
          if (res.confirm) {
            let patrol_record_id = that.data.dataList[e.currentTarget.id].id
            wx.navigateTo({
              url: '../xldkdt/xldkdt?patrol_record_id='+patrol_record_id+'&type='+2
            })
            console.log(e.currentTarget.id,'用户点击确定')
          } else if (res.cancel) {
            console.log('用户点击取消')
          }
        }
      })
    }else{
      wx.showToast({
        title: '请先结束我的任务',
        icon: 'none',
        duration: 5000
      })
      return
    }


  },
  xjrwClick: function (e) {
    console.log("新建任务");
    wx.navigateTo({
      url: "/pages/new/xlxjrw/xlxjrw"
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getPatrolRecordList()
    this.getUserOnlinePatrol()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    var that=this;
    var pagenum = that.data.page_num + 1; //获取当前页数并+1
    console.log(pagenum);
    that.setData({
      page_num: pagenum, //更新当前页数
    })
    that.getPatrolRecordList();//重新调用请求获取下一页数据
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})