/* pages/new/zhxldk/zhxldk.wxss */
.icon{
  width:54rpx;
  height: 54rpx;
}
.tabBar{
  width:100%;
  position: fixed;
  bottom:0;
  margin-left:-4rpx;
  background:#F7F7FA;
  font-size:24rpx;
  color:#8A8A8A;
  box-shadow: 3rpx 3rpx 3rpx 3rpx #aaa; 
  padding:10px 0 30px;
  z-index: 9999;
}

 .tabBar-item{
  float:left;
  width:33%;
  text-align: center;
  overflow: hidden;
}
/*当前字体颜色*/
.tabBartext{
  color:red;
}
.navigator-hover{
  background-color: rgba(0, 0, 0, 0);
}

.container {
  height: 100vh;
  font-size: 16px;
  font-weight: 900;
  padding: 4px;
  background-color: #f6f6f6;
}
  .module {
    display: flex;
    margin: 20px 16px 0;
    font-size: 16px;
    font-weight: 900;
    padding: 10px;
    background-color: #fff;
    justify-content: space-between;
  }
  .title_name{
    padding: 10px 0 0 0;
    text-align: center;
  }
