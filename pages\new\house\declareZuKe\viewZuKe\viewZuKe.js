var user = require('../../../utils/user.js');
var common = require('../../../utils/common.js');
var app = getApp();
const http = require("../../../utils/httpUtils.js");
const date = require("../../../utils/dateUtils.js");
var url = app.globalData.requestUrl;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showNewDataMoadl:true,
    showOldDataMoadl:true,
    change_live_date_end: date.getDate(null,0), 
    type:'0',
    member: {
      id: 0,
      uuid: '',
      house_uuid: '',
      member_id: 0,
      member_photo: '',
      member_realname: '',
      member_mobilephone_number: '',
      room_number: '',
      live_date_begin: '',
      live_date_end: '',
      remark: '',
      status: -2,
      user_id: 0,
      ip: '',
      intime: ''
    },
    apply: {},
    house: {},
    checks: [],
    op_status: -3,
    checkData: {
      status: -1,
      content: ''
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    var that = this;
    var data = {
      member: that.data.member,
      checks: that.data.checks,
      op_status: that.data.op_status,
      apply: that.data.apply
    };
    var member = that.data.member;
    var checks = that.data.checks;
    var op_status = that.data.op_status;
    var apply = that.data.apply;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/house/member/detail?uuid=' + options.uuid,
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.data.length > 0) {
          member = res.data.data[0];
          that.setData({type:member.type});
          if (member.member_photo == null || member.member_photo == '') {
            member.member_photo = '/images/avatar_null.png';
          } else {
            member.member_photo = url + member.member_photo;
          }
          member.intime = member.intime.replace('T', ' ').replace('.000+0000', '');
          user.getUserInfo().then((res) => {
            var userInfo = getApp().globalData.userInfo;
             
            if (member.status == -2 && userInfo.id == member.member_id) {
              //本人可以提交
              op_status = -2;
              data.op_status = op_status;
              that.setData(data);
            } else if (member.status == -1) {
              wx.request({
                method: 'GET',
                url: url + '/storemgmt/house/detail?uuid=' + member.house_uuid,
                header: {
                  'content-type': 'application/x-www-form-urlencoded',
                  token: wx.getStorageSync('token')
                },
                success(res) {
                  if (res.data.success == 1) {
                    if (userInfo.id == res.data.data.owner_id || user.checkPermissionByFuncName("house-check")) {
                      //房东可以审核
                      op_status = -1;
                      data.op_status = op_status;
                      console.log('op_status')
                      console.log(data)
                      that.setData(data);
                    } 
                  }
                }
              })
            }
            //状态是已经通过 操作状态码设为4 则可以对租户进行续租退租操作
            else if(member.status == 1){
              op_status = -4;
              data.op_status = op_status;
              that.setData(data);
            } else {
              //禁止操作
              op_status = -3;
              data.op_status = op_status;
              that.setData(data);
            }
            that.setData(data);
            console.log(data);
          });
        }
        data.member = member;
        that.setData(data);
        console.log(that.data);
      }
    })
    //审核记录
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/house/applyCheck/list?table=house_member&table_uuid=' + options.uuid,
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1 && res.data.data.length > 0) {
          for (let i = 0; i < res.data.data.length; i++) {
            apply.uuid = res.data.data[0].parent_uuid;
            if (res.data.data[i].status == -2) {
              checks.push({
                icon: 'wait',
                colorClass: 'qwui-status-1',
                step_name: res.data.data[i].step_name,
                time: '',
                username: '等待提交',
                content: ''
              });
            } else if (res.data.data[i].status == -1) {
              checks.push({
                icon: 'wait',
                colorClass: 'qwui-status-1',
                step_name: res.data.data[i].step_name,
                time: '',
                username: '等待审核',
                content: ''
              });
            } else if (res.data.data[i].status == 1) {
              checks.push({
                icon: 'success',
                colorClass: 'qwui-status1',
                step_name: res.data.data[i].step_name,
                time: res.data.data[i].intime.replace('T', ' ').replace('.000+0000', ''),
                username: res.data.data[i].handler_user_name + ' : ',
                content: res.data.data[i].content
              });
            } else if (res.data.data[i].status == 0) {
              checks.push({
                icon: 'fail',
                colorClass: 'qwui-status0',
                step_name: res.data.data[i].step_name,
                time: res.data.data[i].intime.replace('T', ' ').replace('.000+0000', ''),
                username: res.data.data[i].handler_user_name + ' : ',
                content: res.data.data[i].content
              });
            }
          }
        }
        data.apply = apply;
        data.checks = checks;
        that.setData(data);
        console.log(that.data);
      }
    })
  },
  //取消选择新的租赁截止日期
  cancelNewDate(){ 
    this.setData({
      change_live_date_end: date.getDate(null,0),
      showNewDataMoadl:true
    });  
  },
  cancelOldDate(){ 
    this.setData({
      change_live_date_end: date.getDate(null,0),
      showOldDataMoadl:true
    });  
  },
  //确认选择新的租赁截止日期
  confirmNewDate(){
    if(
      ((new Date(this.data.change_live_date_end.replace(/-/g, "\/"))) < (new Date(this.data.member.live_date_end.replace(/-/g, "\/"))))
    ){
      wx.showToast({
        title: '续租截止日期应大于原租赁结束时间',
        icon: 'none',
        duration: 2000
      })
      return
    }else{
      var userInfo = getApp().globalData.userInfo;     
      http.jsonpost('house/renewal',{
        old_uuid:this.data.member.uuid,
        uuid:common.wxuuid(),
        img_url:userInfo.photo.replace(url, ''),
        live_date_begin:this.data.member.live_date_end,
        live_date_end:this.data.change_live_date_end,
        status:-1
      },res=>{
        if(res.data.success == 1){
          wx.showModal({
            title: '提示',
            content: '续租成功',
            showCancel: false,
            success(res) {
              if (res.confirm) {
                wx.switchTab({
                  url: '/pages/new/declareRecord/declareRecord/declareRecord',
                })
              }
            }
          })
        }
      })
    }
  },
  //确认退租日期
  confirmOldDate(){
    if(
      ((new Date(this.data.change_live_date_end.replace(/-/g, "\/"))) < (new Date(this.data.member.live_date_begin.replace(/-/g, "\/"))))
    ){
      wx.showToast({
        title: '退租日期应大于原租赁开始时间',
        icon: 'none',
        duration: 2000
      })
      return
    }else{
      http.get('house/rentwithdrawal',{
        uuid:this.data.member.uuid,
        leave_time:this.data.change_live_date_end
      },res=>{
        if(res.data.success == 1){
          wx.showModal({
            title: '提示',
            content: '退租成功',
            showCancel: false,
            success(res) {
              if (res.confirm) {
                wx.switchTab({
                  url: '/pages/new/declareRecord/declareRecord/declareRecord',
                })
              }
            }
          })
        }
      })
    }
  },
  bindDateChange: function (e) { 
    var change_live_date_end = e.detail.value;
    this.setData({
      change_live_date_end: change_live_date_end
    }); 
  },
  xuzuCheck: function(e) {
    var that = this;
    var isxuzu = e.currentTarget.dataset.isxuzu;
    //点击了续租按钮
    if(isxuzu == 1){ 
      this.setData({ 
      showNewDataMoadl:false
    });   
    return
    }
    //点击了退租按钮
    else{ 
      this.setData({ 
        showOldDataMoadl:false
      });   
      return
    }
  },
  bindCheck: function(e) {
    var that = this;
    var status = e.currentTarget.dataset.status;
    var checkData = that.data.checkData;
    checkData.status = status;
    that.setData({
      checkData: checkData
    })
    var uuid = that.options.uuid;
    if (status == -2) {
      wx.showToast({
        title: '该功能暂未开放',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    if (status == 0) {
      if (that.data.checkData.content == '') {
        wx.showToast({
          title: '请输入拒绝理由',
          icon: 'none',
          duration: 2000
        });
        return;
      }
    }
    var that = this;
    wx.request({
      method: 'POST',
      url: url + '/storemgmt/house/apply/verify',
      data: {
        uuid: that.data.apply.uuid,
        status: status,
        content: that.data.checkData.content,
        flag:1
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          wx.switchTab({
            url: '/pages/new/declareRecord/declareRecord/declareRecord',
          })
        } else {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      }
    })
  },
  setCheckContent: function(e) {
    var that = this;
    var checkData = that.data.checkData;
    checkData.content = e.detail.value;
    that.setData({
      checkData: checkData
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

 
})