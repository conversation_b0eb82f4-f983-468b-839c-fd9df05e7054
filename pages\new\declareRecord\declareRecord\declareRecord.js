// pages/new/house/myHouse/myHouse.js
var app = getApp();
var url = app.globalData.requestUrl;
var user = require('../../utils/user.js');
const http = require("../../utils/httpUtils.js");
Page({

  /**
   * 页面的初始数据
   */
  data: {
    loadingMessage:["下拉加载","下拉加载","下拉加载","下拉加载","下拉加载"],
    inputShowed: false,
    inputVal: '',
    pageArray: [1, 1, 1, 1, 1],
    topTab: {
      tabs: [{
        status: -3,
        name: "全部"
      }, {
        status: -2,
        name: "待提交"
      }, {
        status: -1,
        name: "待审核"
      }, {
        status: 1,
        name: "已通过"
      }, {
        status: 0,
        name: "未通过"
      }],
      curIndex: 0,
      curScroolLeft: 0
    },
    apply: [[],[],[],[],[]]
  },
  scrollChange() {
    
    let loadingMessage = this.data.loadingMessage;
    loadingMessage[this.data.topTab.curIndex] = '加载中...'
    this.setData({
      loadingMessage: loadingMessage
    });
    this.getApplyList(this.data.topTab.curIndex);
  },
  switchTopTab(event) {
    var cur = event.currentTarget.dataset.current;
    //每个tab选项宽度占1/5
    var singleNavWidth = this.data.windowWidth / 5;
    var topTab = this.data.topTab;
    topTab.curScroolLeft = (cur - 2) * singleNavWidth;
    //tab选项居中                            
    this.setData({
      topTab: topTab
    })
    if (this.data.topTab.curIndex == cur) {
      return false;
    } else {
      topTab.curIndex = cur;
      this.setData({
        topTab: topTab
      })
    }
  },
 
  switchTopTabItem(event) {
    
    var cur = event.detail.current;
    var singleNavWidth = this.data.windowWidth / 5;
    var topTab = this.data.topTab;
    topTab.curIndex = cur;
    topTab.curScroolLeft = (cur - 2) * singleNavWidth;
    this.setData({
      topTab: topTab
    });
    if(this.data.apply[cur].length == 0){
      this.getApplyList(cur)
    }
  },
  inputTyping: function (e) {
    var that = this;
    var data = {
      inputVal: that.data.inputVal,
    };
    data.inputVal = e.detail.value;
    that.setData(data);
  },
  showInput: function () {
    this.setData({
      inputShowed: true
    });
  },
  hideInput: function () {
    this.setData({
      inputVal: "",
      inputShowed: false,
    });
    this.searchHouse()
  },
  clearInput: function () {
    this.setData({
      inputVal: "",
    });
    this.searchHouse();
  },
  searchHouse(){
    let mydata = {
      pageArray :   [1, 1, 1, 1, 1],
      apply:  [[],[],[],[],[]],
      loadingMessage: ["下拉加载","下拉加载","下拉加载","下拉加载","下拉加载"],
    }
    this.setData(mydata);
    this.getApplyList(this.data.topTab.curIndex);
  },
  getApplyList: function (state) {
    //定义0为全部 1为待提交 2为待审核 3为已通过 4为未通过
    var that = this;
    let mydata = {
      pageArray :  that.data.pageArray,
      apply: that.data.apply,
      loadingMessage: that.data.loadingMessage,
    }
    http.get('house/apply/list', {
      page_num: that.data.pageArray[state],
      page_size: 20,
      apply_name: that.data.inputVal,
      status: state==0?"":(state==1?-2:(state==2?-1:(state==3?1:(state==4?0:""))))
    }, res => { if(res.data.success == 1){
      let needAddapply = res.data.data.data_list;
      for (let i = 0; i < needAddapply.length; i++) {
        if (needAddapply[i].img_url == "") {
          needAddapply[i].img_url = "/images/image_null.png";
        } else {
          needAddapply[i].img_url = getApp().globalData.requestUrl +needAddapply[i].img_url;
        }
      }
      mydata.apply[state] = mydata.apply[state].concat(needAddapply);   
      if(res.data.data.total_record<=mydata.apply[state].length){
        mydata.loadingMessage[state] = "我已经到底啦：("
      }
      if(res.data.data.total_page_num >= mydata.pageArray[state]){
        mydata.pageArray[state]++;
      }
      that.setData(mydata);
    }
    })
  },
  bindUrl: function (e) {
    console.log(e);
    if (e.currentTarget.dataset.table == 'house') {
      wx.navigateTo({
        url: '/pages/new/house/viewHouseInfo/viewHouseInfo?uuid=' + e.currentTarget.dataset.table_uuid + '&applyid=' + e.currentTarget.dataset.uuid,
      })
    }
    if (e.currentTarget.dataset.table == 'house_member') {
      wx.navigateTo({
        url: '/pages/new/house/declareZuKe/viewZuKe/viewZuKe?uuid=' + e.currentTarget.dataset.table_uuid + '&applyid=' + e.currentTarget.dataset.uuid,
      })
    }
  },
  test: function (e) {
    let that = this ;
    if(user.checkPermissionByFuncName("house-check")){  
      console.log('长按事件并且有管理员权限'); 
      wx.showModal({
        title: '提示',
        content: '确认删除当前申报记录',
        cancelText: '取消',
        cancelColor: '#ff0000',
        confirmText: '确认',
        confirmColor: '#008000',
        success(res) {
          if (res.confirm) { 
            wx.showModal({
              title: '提示',
              content: '删除不可逆转,确认继续删除？',
              cancelText: '取消',
              cancelColor: '#ff0000',
              confirmText: '确认',
              confirmColor: '#008000',
              success(res) {
                if (res.confirm) { 
                http.get('/house/apply/delete', {
                  uuid: e.currentTarget.dataset.uuid
                }, res => {
                  if (res.data.success == 1) { 
                    wx.showToast({
                      title: '删除成功',
                      icon: 'none',
                      duration: 2000,
                      success(){
               
                      }
                    })
                    that.searchHouse();
                  }
                })
                } else if (res.cancel) { 
                  return;
                }
              }
            })

 
          } else if (res.cancel) { 
            return;
          }
        }
      })

    }

    
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    
    var that = this;
    user.getUserInfo(false).then((res) => {
      that.setData({
        userInfo: getApp().globalData.userInfo
      });
      if (!that.data.userInfo.isLogin) {    
        wx.showModal({
          title: '提示',
          content: '请先注册并登录',
          success(res) {
            if (res.confirm) {
              wx.redirectTo({
                url: '/pages/new/user/login'
              })
            } else if (res.cancel) {
              wx.showToast({
                title: '登录失败',
                icon: 'none',
                duration: 2000
              })
            }
          }
        })
      }else{ 
        this.searchHouse()
        
      }
    });
   

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  
})