// pages/meetingIndex/meetingIndex.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    applictionArr:[],
    status: ['拒绝','通过','待审核']
  },
  toApply: function(){
    wx.navigateTo({
      url: '../meetApply/meetApply',
      success: function(res) {},
      fail: function(res) {},
      complete: function(res) {},
    })
  },
  tolook:function(e){
    if (e.currentTarget.dataset.status == 1 || e.currentTarget.dataset.status == '1'){
      wx.navigateTo({
        url: '../meetingQrcode/meetingQrcode?vid=' + e.currentTarget.dataset.vid,
        success: function (res) { },
        fail: function (res) { },
        complete: function (res) { },
      })
    }else{
      wx.showToast({
        title: '审核未通过或暂未审核',
        icon: 'none',
        duration: 2000
      })
    }
  },
  getApply:function(){
    let self = this;
    var app = getApp();
    var url = app.globalData.requestUrl;
    wx.request({
      method: 'POST',
      url: url + 'visit/getConferenceVisitList',
      data: {
        app_id: app.globalData.app_id,
        app_key: app.globalData.app_key,
        token: wx.getStorageSync('token')
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      success(res) {
        if (res.data.error_code == 0) {
          for (let i = 0; i < res.data.data.length;i++){
            res.data.data[i].visit_start_time = res.data.data[i].visit_start_time.substr(0, 10);
            res.data.data[i].visit_end_time = res.data.data[i].visit_end_time.substr(0, 10);
          }
          self.setData({
            applictionArr: res.data.data
          })
        } else {
          wx.showToast({
            title: res.data.error_msg,
            icon: 'none',
            duration: 2000
          })
        }
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.getApply();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

 
})