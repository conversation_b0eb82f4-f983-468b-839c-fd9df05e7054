// pages/new/userCheck/userCheck.js
const http = require("../utils/httpUtils.js");
var user = require('../../../pages/new/utils/user.js')
var app = getApp();
var url = app.globalData.requestUrl;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    sbhc_status: 0,
    wgyr_status: 0,
    xfrw_status: 0,
    op_status: 0,
    userInfo: null,
    imgUrls: [
      '/images/new-banner.png'
    ],
    indicatorDots: true,
    vertical: false,
    autoplay: true,
    circular: true,
    interval: 3000,
    duration: 500,
    previousMargin: 0,
    nextMargin: 0,
    SwiperHeight: '',
    toDoNum: 0,
    winWid: '',
    noticeList: [],
    imgs: [],
    topImgs: [],
    modalHidden: true,
    search_param:'',
    buttonTop: 0,
    buttonLeft: 0,
    buttonRight:0,
    windowHeight: '',
    windowWidth: '',
    startPoint:'',
    endPoint:''
  },
  toPage: function (e) {
    let that = this
    let url = e.currentTarget.dataset.type == 'h5' ? '../../myWebview/myWebview?h5=' + e.currentTarget.dataset.url + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor;
  
    // let url ='../../myWebview/myWebview?h5=' +  e.currentTarget.dataset.url;
      if (that.data.userInfo.isLogin) {   
        if(that.data.userInfo.isAuth){
          wx.showToast({
            title: '实名认证已通过',
            icon: 'none',
            duration: 2000
          })
          return true;
        }else{
          wx.showModal({
            title: '警告！',
            content: '请务必使用本人身份证进行认证,冒用他人身份是违法行为！谢谢您的配合。',
            confirmText: '我知道了',
            confirmColor: '#008000',
            cancelText: '暂不认证',
            cancelColor: '#ff0000',
            success(res) {
              if (res.confirm) {
                wx.request({
                  method: 'GET',
                  url: 'https://zzsbapi.jxjkga.cn/storemgmt' + '/health/getMyForeignFirstInfo',
                  header: {
                    token: wx.getStorageSync('token')
                  },
                  success(res) {
                    console.log(res.data.data)
                    if(res.data.data == null){
                      wx.navigateTo({
                        url: url,
                      })
                    }else{
                      wx.navigateTo({
                        url: '../../myWebview/myWebview?h5=' + 'https://zzsbui.jxjkga.cn/#/epidemic/jwryBack/foreignFirstPromise?relation_id'+res.data.data.foreign_record_id+`&utoken=${wx.getStorageSync('token')}`
                        
                      })
                    }
                  },
                  fail(res) {
                    console.log(res)
                  }
                })
               
              }else{
                return;
              }
            }
          })
        }
       
      }else{  
         wx.showModal({
          title: '提示',
          content: '请先注册并登录',
          success(res) {
            if (res.confirm) {
              that.setData({ isClose:false}) 
              wx.redirectTo({
                url: '/pages/new/user/login'
              })
            } else if (res.cancel) {
              wx.showToast({
                title: '登录失败',
                icon: 'none',
                duration: 2000
              })
            }
          }
        })
      
      }
  },
  doAuth: function () {
    user.doAuth();
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
  
    
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    var that = this;
    user.getUserInfo(false).then((res) => {
      
      that.setData({
        userInfo: getApp().globalData.userInfo
      });
      
      // let url ='../../myWebview/myWebview?h5=' + 'https://zzsbui.jxjkga.cn/#/';
      // if (that.data.userInfo.isLogin) {   
      //   if(that.data.userInfo.isAuth){
      //     wx.navigateTo({
      //       url: url,
      //     })
      //   }else{
      //     that.doAuth();
      //   }
      // }else{  
      //    wx.showModal({
      //     title: '提示',
      //     content: '请先注册并登录',
      //     success(res) {
      //       if (res.confirm) {
      //         that.setData({ isClose:false}) 
      //         wx.redirectTo({
      //           url: '/pages/new/user/login'
      //         })
      //       } else if (res.cancel) {
      //         wx.showToast({
      //           title: '登录失败',
      //           icon: 'none',
      //           duration: 2000
      //         })
      //       }
      //     }
      //   })
      
      // }
      
    }); 
    
   
     
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})