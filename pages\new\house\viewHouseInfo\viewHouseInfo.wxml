  <watermark></watermark>
<view>
	<swiper style='height:{{swiper.height}};max-height:200px;' indicator-dots="{{swiper.indicatorDots}}" autoplay="{{swiper.autoplay}}" interval="{{swiper.interval}}" duration="{{swiper.duration}}">
		<block wx:for="{{swiper.image}}" wx:key="this">
			<swiper-item>
				<image src="{{item}}" class="slide-image" mode="aspectFill" model="widthFix" bindload='bindSwiperHeight' />
			</swiper-item>
		</block>
	</swiper>
</view>
<view class="page">
	<view class="page__bd">
		<view class="weui-cells__title">小区坐落</view>
		<view class="weui-cells weui-cells_after-title">
			<view class="weui-cell">
				<view class="weui-cell__bd">
					<label class="weui-textarea" style="font-size:15px;">{{house.area_location_name}}{{house.address}}</label>
				</view>
			</view>
		</view>
		<view class="weui-cells__title">备注</view>
		<view class="weui-cells weui-cells_after-title">
			<view class="weui-cell">
				<view class="weui-cell__bd">
					<label class="weui-textarea">{{house.remark==""|| remark==null?"无":house.remark}}</label>
				</view>
			</view>
		</view>
		<view class="weui-cells__title">房东信息</view>
		<view class="weui-panel weui-panel_access myweui-row">
			<view class="weui-panel__bd myweui-row-body">
				<navigator url="" class="weui-media-box weui-media-box_appmsg" hover-class="weui-cell_active">
					<view class="weui-media-box__hd weui-media-box__hd_in-appmsg">
						<image class="weui-media-box__thumb" src="{{house.owner_photo}}" />
					</view>
					<view class="weui-media-box__bd weui-media-box__bd_in-appmsg">
						<view class="weui-media-box__title">{{house.owner_realname}}
						</view>
						<view class="weui-media-box__desc">
							<label class='product-sku'>{{house.owner_mobilephone_number}} </label>
						</view>
					</view>
					<view class="weui-cell__ft weui-cell__ft_in-access"></view>
				</navigator>
			</view>
		</view>
		<view class="weui-cells__title">租客信息</view>
		<view class="weui-panel weui-panel_access myweui-row">
			<view class="weui-panel__bd myweui-row-body">
				<block wx:for="{{members}}"  wx:for-item="item">
					<navigator url="/pages/new/house/declareZuKe/viewZuKe/viewZuKe?uuid={{item.uuid}}" class="weui-media-box weui-media-box_appmsg" hover-class="weui-cell_active">
						<view class="weui-media-box__hd weui-media-box__hd_in-appmsg">
							<image class="weui-media-box__thumb" src="{{item.member_photo}}" />
						</view>
						<view class="weui-media-box__bd weui-media-box__bd_in-appmsg">
							<view class="weui-media-box__title">{{item.member_realname}}
							</view>
							<view class="weui-media-box__desc">
								<label class='product-sku'>{{item.member_mobilephone_number}} </label>
							</view>
							<view class="weui-media-box__info">
								<view class="weui-media-box__info__meta">
									<label class="myweui-tag myweui-tag-blue">{{item.room_number}}</label>
								</view>
								<view wx:if="{{item.status==-2}}" class="weui-media-box__info__meta">
									<label class="myweui-tag myweui-tag-yellow">待提交</label>
								</view>
								<view wx:if="{{item.status==-1}}" class="weui-media-box__info__meta">
									<label class="myweui-tag myweui-tag-gray">待审核</label>
								</view>
								<view wx:if="{{item.status==1}}" class="weui-media-box__info__meta">
									<label class="myweui-tag myweui-tag-green">已通过</label>
								</view>
								<view wx:if="{{item.status==0}}" class="weui-media-box__info__meta">
									<label class="myweui-tag myweui-tag-red">未通过</label>
								</view>
							</view>
						</view>
						<view class="weui-cell__ft weui-cell__ft_in-access"></view>
					</navigator>
				</block>
				<view style="padding:16px;" wx:if="{{members.length==0}}">
					<button class="weui-btn" type="primary" bindtap="bindInvite">暂无租客，邀请加入+</button>
				</view>
			</view>
		</view>
			<view class="weui-cells__title">家庭成员信息</view>
		<view class="weui-panel weui-panel_access myweui-row">
			<view class="weui-panel__bd myweui-row-body">
				<block wx:for="{{families}}"  wx:for-item="item">
					<navigator url="/pages/new/house/declareZuKe/viewZuKe/viewZuKe?uuid={{item.uuid}}" class="weui-media-box weui-media-box_appmsg" hover-class="weui-cell_active">
						<view class="weui-media-box__hd weui-media-box__hd_in-appmsg">
							<image class="weui-media-box__thumb" src="{{item.member_photo}}" />
						</view>
						<view class="weui-media-box__bd weui-media-box__bd_in-appmsg">
							<view class="weui-media-box__title">{{item.member_realname}}
							</view>
							<view class="weui-media-box__desc">
								<label class='product-sku'>{{item.member_mobilephone_number}} </label>
							</view>
							<view class="weui-media-box__info">
								<view class="weui-media-box__info__meta">
									<label class="myweui-tag myweui-tag-blue">{{item.room_number}}</label>
								</view>
								<view wx:if="{{item.status==-2}}" class="weui-media-box__info__meta">
									<label class="myweui-tag myweui-tag-yellow">待提交</label>
								</view>
								<view wx:if="{{item.status==-1}}" class="weui-media-box__info__meta">
									<label class="myweui-tag myweui-tag-gray">待审核</label>
								</view>
								<view wx:if="{{item.status==1}}" class="weui-media-box__info__meta">
									<label class="myweui-tag myweui-tag-green">已通过</label>
								</view>
								<view wx:if="{{item.status==0}}" class="weui-media-box__info__meta">
									<label class="myweui-tag myweui-tag-red">未通过</label>
								</view>
							</view>
						</view>
						<view class="weui-cell__ft weui-cell__ft_in-access"></view>
					</navigator>
				</block>
			 
			</view>
		</view>
		<view data-wxif="{{op_status}}" class="weui-cells__title">审批信息</view>
		<view class="weui-panel weui-panel_access myweui-row">
			<view class="weui-cells weui-cells_form">
				<view class="qwui-timeline">
					<view class="qwui-timeline-item" wx:for="{{checks}}" wx:for-item="item">
						<icon class="qwui-timeline-axis iconfont icon-{{item.icon}} {{item.colorClass}}"></icon>
						<view class="qwui-timeline-content qwui-text">
							<view class="qwui-timeline-title">
								{{item.step_name}}
								<label class="qwui-timeline-title-info">
									<icon class="iconfont icon-android"></icon>{{item.time}}
								</label>
							</view>
							<view class="qwui-timeline-details">
								<label class="qwui-timeline-details-light">{{item.username}}</label>
								<label class="qwui-timeline-details-deep">{{item.content}}</label>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</view>
<view wx-if="{{op_status==-1}}" class="check-btn-box">
	<view wx:if="{{checkData.status==0}}" class="check-btn-input">
		<view class="check-btn-txt">
			<label>理由</label>
		</view>
		<view>
			<input class="weui-input" id="check_content" bindinput="setCheckContent" placeholder="请输入拒绝理由" />
		</view>
	</view>
	<view class="check-btn-line">
		<view class="check-btn-txt">
			<label>审核</label>
		</view>
		<view bindtap="bindCheck" data-status='1' class="check-btn-status1">
			<label>通过</label>
		</view>
		<view bindtap="bindCheck" data-status='0' class="check-btn-status0">
			<label>拒绝</label>
		</view>
	</view>

</view>
<view wx-if="{{op_status==-2}}" class="check-btn-box">
	<view class="check-btn-line">
		<view class="check-btn-txt">
			<label>操作</label>
		</view>
		<view bindtap="bindCheck" data-status='-1' class="check-btn-status1">
			<label>提交审核</label>
		</view>
		<view bindtap="bindCheck" data-status='-2' class="check-btn-status-2">
			<label>修改资料</label>
		</view>
	</view>


</view>