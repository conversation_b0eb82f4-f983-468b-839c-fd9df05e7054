
<watermark></watermark>
<view class="map_container">
  <map id='map' class='map' longitude='{{longitude}}' latitude='{{latitude}}' scale='{{scale}}' markers='{{markers}}' bindmarkertap='bindmarkertap' bindlabeltap='bindmarkertap' catchtap="bindNormal" show-location></map>
</view>

<view style="width: 100%; position: relative; display: flex; align-items: center; justify-content: center;padding-bottom: 8em;flex-direction:column; "bindtap='dK_new' >
  <view class='yuantu_di'></view>
  <view class='yuantu_gy'></view>
  <div wx:if="{{show_dk}}" style="position: absolute; top:45px;" > 
    开始打卡
  </div>
  <div wx:if="{{!show_dk}}" style="position: absolute; top:45px;"> 
    结束打卡
  </div>
  <div style="position: absolute; top:65px;color: aliceblue;">
    {{date}}
  </div>
</view>