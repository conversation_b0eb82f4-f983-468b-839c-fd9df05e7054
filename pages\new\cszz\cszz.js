// pages/new/jgj/jgj.js
var user = require('../../../pages/new/utils/user.js')

const http = require("../utils/httpUtils.js");
Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    utoken:'',
    stamp:''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var timestamp = Date.parse(new Date());
    timestamp = timestamp / 1000;
    console.log("当前时间戳为：" + timestamp)
    this.setData({
      stamp:timestamp,
      utoken:encodeURIComponent(wx.getStorageSync('token')),

    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    var that = this;
    user.getUserInfo(false).then((res) => {
      
      that.setData({
        userInfo: getApp().globalData.userInfo
      });
      if (!that.data.userInfo.isLogin) {    
        wx.showModal({
          title: '提示',
          content: '请先注册并登录',
          success(res) {
            if (res.confirm) {
              that.setData({ isClose:false}) 
              wx.redirectTo({
                url: '/pages/new/user/login'
              })
            } else if (res.cancel) {
              wx.showToast({
                title: '登录失败',
                icon: 'none',
                duration: 2000
              })
            }
          }
        })
      }else{  
        // if (this.data.isClose) {
        //   this.setData({ isClose:false}) 
        //   let url ='https://alirobot.jxjkga.cn/';
        //    wx.navigateTo({
        //     url: url,
        //   })
        //  }else{
        //   this.setData({ isClose:true}) 
        //   wx.switchTab({
        //     url: '/pages/new/homeNew/homeNew',
        //    })
        //  }
      }
    }); 
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  }
})