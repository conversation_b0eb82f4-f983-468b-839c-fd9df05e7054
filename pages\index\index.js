// pages/index/index.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    bannerUrls: [
      
    ],
    indicatorDots: true,
    autoplay: true,
    interval: 3000,
    duration: 1000,
    link_list: [],
    hideNotice: true,
    notice: '',
    msg_num: 0,
    msg_link: {
      bgcolor: "#ffffff",
      fontcolor: "#000000",
      subTitle: "消息中心",
      title: "消息中心",
      type: "h5",
      url: "https://mapp.mcs.jsycloud.com/index.php/user/notice",
    },
    setting_link: {
      bgcolor: "#7192fd",
      fontcolor: "#ffffff",
      subTitle: "设置",
      title: "设置",
      type: "mp",
      url: "../setting/setting",
    },
    marqueePace: 1,//滚动速度
    marqueeDistance: 10,//初始滚动距离
    size: 12,
    interval2: 20, // 时间间隔
    countTime: '',
    user_status: 1,

    isV: 0,
    bizToken: '',
    degree: 0,
    weather: '晴',
    address: '嘉兴',
    air: '优',
    wet: '',
    tenantArr: []
  },
  /**轮播高度自适应——获取图片高度 */
  imgHeight: function (e) {
    var winWid = wx.getSystemInfoSync().windowWidth; //获取当前屏幕的宽度
    var imgh = e.detail.height;//图片高度
    var imgw = e.detail.width;//图片宽度
    var swiperH = winWid * imgh / imgw + "px"
    this.setData({
      Height: swiperH//设置高度
    })
  },

  /**跳转页面 */
  toPage: function (e) {
    let self = this;
    let url = e.currentTarget.dataset.type == 'h5' ? '../myWebview/myWebview?h5=' + e.currentTarget.dataset.url + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor;
    if (e.currentTarget.dataset.type == 'alert'){
      wx.showToast({
        title: e.currentTarget.dataset.url,
        icon: 'none',
        duration: 2000
      })
    }else{
      if (wx.getStorageSync('token') && wx.getStorageSync('token') != '') {
        if (wx.getStorageSync('auth') == '1') {
          wx.navigateTo({
            url: url,
          })
        } else {
          self.isVer();
        }
      } else {
        wx.showModal({
          title: '提示',
          content: '请先注册并登录',
          success(res) {
            if (res.confirm) {
              wx.redirectTo({
                url: '../login/login'
              })
            } else if (res.cancel) {
              wx.showToast({
                title: '登录失败',
                icon: 'none',
                duration: 2000
              })
            }
          }
        })
      }
    }
  },
  /**点击头像昵称 */
  toMine: function (){
    let self = this;
    if (self.data.user_status == 1){
      wx.navigateTo({
        url: '../myWebview/myWebview?h5=https://mapp.mcs.jsycloud.com/user/profile&bgcolor=#ffffff&fontcolor=#000000&title=个人信息',
      })
    }else if (self.data.user_status == 0) {
      wx.redirectTo({
        url: '../login/login',
      })
    } else if (self.data.user_status == -1){
      var app = getApp();
      var url = app.globalData.requestUrl;
      wx.request({
        method: 'POST',
        url: url + 'wechat/getBizToken',
        data: {
          app_id: app.globalData.app_id,
          app_key: app.globalData.app_key,
          token: wx.getStorageSync('token')
        },
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        success(res) {
          if (res.data.error_code == 0) {
            let BizToken = res.data.data.BizToken;// 去客户后端调用DetectAuth接口获取BizToken
            // 调用实名核身功能
            wx.startVerify({
              data: {
                token: BizToken // BizToken
              },
              success: (res) => { // 验证成功后触发
                wx.setStorageSync('auth', '1')
                // res 包含验证成功的token, 这里需要加500ms延时，防止iOS下不执行后面的逻辑
                setTimeout(() => {
                  wx.setStorageSync('auth', '1')
                  // 验证成功后，拿到token后的逻辑处理，具体以客户自身逻辑为准
                  wx.request({
                    method: 'POST',
                    url: url + 'wechat/getDetectInfo',
                    data: {
                      app_id: app.globalData.app_id,
                      app_key: app.globalData.app_key,
                      token: wx.getStorageSync('token'),
                      biztoken: BizToken
                    },
                    header: {
                      'content-type': 'application/x-www-form-urlencoded' // 默认值
                    },
                    success(res) {
                      if (res.data.error_code == 0) {
                        wx.showToast({
                          title: '认证成功',
                          icon: 'none',
                          duration: 2000,
                          success() {
                            wx.setStorageSync('userName', res.data.data.name)
                            wx.setStorageSync('userPhoto', res.data.data.photo)
                            wx.setStorageSync('userPhone', res.data.data.mobile)
                            wx.navigateTo({
                              url: '../setting/setting',
                            })
                          }
                        })
                      } else {
                        wx.showToast({
                          title: res.data.error_msg,
                          icon: 'none',
                          duration: 2000
                        })
                      }
                    }
                  })
                }, 500);

              },
              fail: (err) => {  // 验证失败时触发
                // err 包含错误码，错误信息，弹窗提示错误
                setTimeout(() => {
                  wx.showModal({
                    title: "提示",
                    content: err.ErrorMsg,
                    showCancel: false
                  })
                }, 500);
              }
            });
          } else {
            wx.showToast({
              title: res.data.error_msg,
              icon: 'none',
              duration: 2000
            })
          }
        }
      })
    }
  },
  /**验证是否人证核实 */
  isVer: function (){
    let self = this;
    var app = getApp();
    var url = app.globalData.requestUrl;
    if (wx.getStorageSync('auth') == '1') {
      return true;
    }else{
      wx.request({
        method: 'POST',
        url: url + 'wechat/getBizToken',
        data: {
          app_id: app.globalData.app_id,
          app_key: app.globalData.app_key,
          token: wx.getStorageSync('token')
        },
        header: {
          'content-type': 'application/x-www-form-urlencoded' // 默认值
        },
        success(res) {
          if (res.data.error_code == 0) {
            self.setData({
              bizToken: res.data.data.BizToken
            })
          } else {
            wx.showToast({
              title: res.data.error_msg,
              icon: 'none',
              duration: 2000
            })
          }
        }
      })
      wx.showModal({
        title: '提示',
        content: '系统必须在认证后使用，现在去认证',
        success(res) {
          if (res.confirm) {
            var app = getApp();
            var url = app.globalData.requestUrl;
            let BizToken = self.data.bizToken;// 去客户后端调用DetectAuth接口获取BizToken
            // 调用实名核身功能
            wx.startVerify({
              data: {
                token: BizToken // BizToken
              },
              success: (res) => { // 验证成功后触发
                wx.setStorageSync('auth', '1')
                // res 包含验证成功的token, 这里需要加500ms延时，防止iOS下不执行后面的逻辑
                setTimeout(() => {
                  wx.setStorageSync('auth', '1')
                  // 验证成功后，拿到token后的逻辑处理，具体以客户自身逻辑为准
                  wx.request({
                    method: 'POST',
                    url: url + 'wechat/getDetectInfo',
                    data: {
                      app_id: app.globalData.app_id,
                      app_key: app.globalData.app_key,
                      token: wx.getStorageSync('token'),
                      biztoken: BizToken
                    },
                    header: {
                      'content-type': 'application/x-www-form-urlencoded' // 默认值
                    },
                    success(res) {
                      if (res.data.error_code == 0) {
                        wx.showToast({
                          title: '认证成功',
                          icon: 'none',
                          duration: 2000,
                          success() {
                            wx.setStorageSync('userName', res.data.data.name)
                            wx.setStorageSync('userPhoto', res.data.data.photo)
                            wx.setStorageSync('userPhone', res.data.data.mobile)
                            wx.navigateTo({
                              url: '../setting/setting',
                            })
                          }
                        })
                      } else {
                        wx.showToast({
                          title: res.data.error_msg,
                          icon: 'none',
                          duration: 2000
                        })
                      }
                    }
                  })
                }, 500);

              },
              fail: (err) => {  // 验证失败时触发
                // err 包含错误码，错误信息，弹窗提示错误
                setTimeout(() => {
                  wx.showModal({
                    title: "提示",
                    content: err.ErrorMsg,
                    showCancel: false
                  })
                }, 500);
              }
            });
          } else if (res.cancel) {
            wx.showToast({
              title: '获取认证信息失败',
              icon: 'none',
              duration: 2000
            })
          }
        }
      })
    }
  },
  /**获取天气 */
  getWeather: function () {
    let self = this;
    var app = getApp();
    var url = app.globalData.requestUrl;
    wx.request({
      method: 'POST',
      url: url + 'home/getWeather',
      data: {
        app_id: app.globalData.app_id,
        app_key: app.globalData.app_key,
        city: '嘉兴'
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      success(res) {
        if (res.data.error_code == 0) {
          let air = '';
          let aqi = parseInt(res.data.data.aqi)
          switch (true) {
            case aqi <= 50:
              air = '优'
              break;
            case 50 < aqi <= 100:
              air = '良'
              break;
            case 100 < aqi <= 150:
              air = '轻度污染'
              break;
            case 150 < aqi <= 200:
              air = '中度污染'
              break;
            case 200 < aqi <= 300:
              air = '重度污染'
              break;
          }
          self.setData({
            degree: res.data.data.realtime.temperature,
            weather: res.data.data.realtime.info,
            address: '嘉兴市',
            air: air,
            wet: res.data.data.realtime.humidity
          })
        } else {
          wx.showToast({
            title: res.data.error_msg,
            icon: 'none',
            duration: 2000
          })
        }
      }
    })
  },

  /**获取菜单 */
  getMenu: function () {
    let self = this;
    var app = getApp();
    var url = app.globalData.requestUrl;
    wx.request({
      method: 'POST',
      url: url + 'home/getmenu',
      data: {
        app_id: app.globalData.app_id,
        app_key: app.globalData.app_key,
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      success(res) {
        if (res.data.error_code == 0) {
          let arr_list = [];
          if (wx.getStorageSync('token') && wx.getStorageSync('token') != '' && wx.getStorageSync('auth') == '1') {
            res.data.data.map(value => {
              if (value.is_admin == 0 || wx.getStorageSync('admin') == '1') {
                arr_list.push(value);
              }
            })
          }else{
            res.data.data.map(value=>{
              if (value.is_admin == 0){
                arr_list.push(value);
              }
            })
          }
          self.setData({
            link_list: arr_list
          })
        } else {
          wx.showToast({
            title: res.data.error_msg,
            icon: 'none',
            duration: 2000
          })
        }
      }
    })
  },
  /**获取消息 */
  getMsg: function () {
    let self = this;
    var app = getApp();
    var url = app.globalData.requestUrl;
    wx.request({
      method: 'POST',
      url: url + 'home/getMessage',
      data: {
        app_id: app.globalData.app_id,
        app_key: app.globalData.app_key,
        token: wx.getStorageSync('token'),
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      success(res) {
        if (res.data.error_code == 0) {
          let isShow = res.data.data.length > 0 ? false : true;
          let data = {};
          let windowWidth = wx.getSystemInfoSync().windowWidth; // 屏幕宽度
          let notice = res.data.data.map(value => {
            let str = '';
            str += value.content;
            return str;
          })
          let strNew = notice.join('              ');
          let length = strNew.length * self.data.size; //文字长度
          self.setData({
            notice: strNew,
            msg_num: res.data.data.length,
            hideNotice: isShow,
            marqueeDistance: windowWidth,
            length: length,
            windowWidth: windowWidth
          },()=>{
            self.run1();
          })
        } else {
          wx.showToast({
            title: res.data.error_msg,
            icon: 'none',
            duration: 2000
          })
        }
      }
    })
  },
  /**消息通知滚动 */
  run1: function () {
    var self = this;
    self.data.countTime = setInterval(function () {
      if (-self.data.marqueeDistance < self.data.length) {
        self.setData({
          marqueeDistance: self.data.marqueeDistance - self.data.marqueePace,
        });
      } else {
        clearInterval(self.data.countTime);
        self.setData({
          marqueeDistance: self.data.windowWidth
        });
        self.run1();
      }
    }, self.data.interval2);
  },
  /**关闭消息通知 */
  closeNotice:function(){
    let self = this;
    self.setData({
      hideNotice: true,
    });
  },
  /**toekn登录 */
  loginBytoken: function(){
    let self = this;
    var app = getApp();
    var url = app.globalData.requestUrl;
    wx.request({
      method: 'POST',
      url: url + 'wechat/loginByToken',
      data: {
        token: wx.getStorageSync('token'),
        app_id: app.globalData.app_id,
        app_key: app.globalData.app_key
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      success(res) {
        if (res.data.error_code == 0) {
          // wx.setStorageSync('token', res.data.data.user_token)
          wx.setStorageSync('auth', res.data.data.auth)
          wx.setStorageSync('userPhone', res.data.data.mobile)
          wx.setStorageSync('userPhoto', res.data.data.photo)
          wx.setStorageSync('userName', res.data.data.name)
          wx.setStorageSync('car', res.data.data.car)
          wx.setStorageSync('last_car', res.data.data.last_car)
          wx.setStorageSync('admin', res.data.data.user_is_admin)
          wx.setStorageSync('address', res.data.data.user_address)
          wx.setStorageSync('occupation', res.data.data.occupation)
          wx.switchTab({
            url: '../index/index'
          })
        } else {
          wx.showToast({
            title: res.data.error_msg,
            icon: 'none',
            duration: 2000,
            success(){
              wx.setStorageSync('token', '');
              setTimeout(function(){
                wx.redirectTo({
                  url: '../login/login'
                })
              },1000)
            }
          })
        }
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let self = this;
    let token = wx.getStorageSync('token');
    
    if (wx.getStorageSync('token') && wx.getStorageSync('token') != '') {
      self.loginBytoken();
      if (wx.getStorageSync('auth') != '1' || !wx.getStorageSync('auth')){
        self.setData({
          user_status: -1
        })
      }
    }else{
      self.setData({
        user_status: 0
      })
      wx.showModal({
        title: '提示',
        content: '请先注册并登录',
        success(res) {
          if (res.confirm) {
            wx.redirectTo({
              url: '../login/login'
            })
          } else if (res.cancel) {
            wx.showToast({
              title: '登录失败',
              icon: 'none',
              duration: 2000
            })
          }
        }
      })
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    var self = this;
    self.getMenu();
    if (wx.getStorageSync('token') && wx.getStorageSync('token') != '') {
      self.getMsg();
      self.isVer();
      if (wx.getStorageSync('auth') != '1' || !wx.getStorageSync('auth')) {
        self.setData({
          user_status: -1
        })
      } else if (wx.getStorageSync('auth') == '1'){
        self.setData({
          user_status: 1
        })
      }
    }
    self.getWeather();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

 
})