// pages/wyfsb/wyfbzdj.js
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    pageData: {
      user_name: '',
      user_phone: '',
      user_identity_type: '身份证',
      user_identity: '',
      jhr_name: '',
      jhr_phone: '',
      jhr_relation: '',
      best_frame_url: ''
    },
    fileList: [],
    showPicker: false,
    showPicker_state: false,
    identityTypeIndex: 0,
    tempIdentityTypeIndex: 0,
    columns: ['身份证', '普通护照', '其它证件'],
    ParentData: {},
    BytokenObj: {},
    HOST: 'https://zzsbapi.jxjkga.cn' // 请根据实际情况修改API地址
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      ParentData: options
    })

    if (options.apply_type == '1') {
      this.setData({
        'BytokenObj.is_wcn': 0
      })
    } else {
      this.Bytoken()
    }
  },

  /**
   * 输入框内容变化
   */
  onInputChange(e) {
    const field = e.currentTarget.dataset.field
    const value = e.detail.value
    this.setData({
      [`pageData.${field}`]: value
    })
  },

  /**
   * 手机号失焦验证
   */
  blurClick(e) {
    const val = e.detail.value
    if (!val) {
      wx.showToast({
        title: '手机号不能为空',
        icon: 'none'
      })
      return
    }

    const reg = /^1[3456789]\d{9}$/
    if (reg.test(val)) {
      wx.showToast({
        title: '手机号格式正确',
        icon: 'success'
      })
    } else {
      wx.showToast({
        title: '请输入正确手机号',
        icon: 'none'
      })
    }
  },

  /**
   * 获取用户信息
   */
  async Bytoken() {
    const that = this

    try {
      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: that.data.HOST + '/base/user/info/byToken',
          method: 'GET',
          data: { a: 1 },
          header: {
            'content-type': 'application/json'
          },
          success: resolve,
          fail: reject
        })
      })

      if (res.data.success == 1) {
        const userData = res.data.data
        that.setData({
          BytokenObj: userData
        })

        if (that.data.ParentData.apply_type == 0) {
          const imageUrl = userData.best_frame_url
          const fullImageUrl = imageUrl && !imageUrl.startsWith('http') ? that.data.HOST + imageUrl : imageUrl

          that.setData({
            'pageData.user_name': userData.name,
            'pageData.user_phone': userData.phone,
            'pageData.user_identity_type': userData.identity_type,
            'pageData.user_identity': userData.identity,
            'pageData.best_frame_url': fullImageUrl
          })
        }
      } else {
        wx.showToast({
          title: res.data.dsc || '获取用户信息失败',
          icon: 'none'
        })
      }
    } catch (err) {
      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      })
    }
  },

  /**
   * 选择图片
   */
  chooseImage() {
    const that = this
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success(res) {
        const tempFilePaths = res.tempFilePaths
        that.compressImage(tempFilePaths[0])
      }
    })
  },

  /**
   * 压缩图片
   */
  compressImage(filePath) {
    const that = this
    wx.compressImage({
      src: filePath,
      quality: 80,
      success(res) {
        that.setData({
          fileList: [{
            url: res.tempFilePath,
            file: res.tempFilePath
          }]
        })
        that.setData({
          'pageData.face_img_file': res.tempFilePath
        })
      },
      fail() {
        // 如果压缩失败，直接使用原图
        that.setData({
          fileList: [{
            url: filePath,
            file: filePath
          }]
        })
        that.setData({
          'pageData.face_img_file': filePath
        })
      }
    })
  },

  /**
   * 删除图片
   */
  deleteImage() {
    this.setData({
      fileList: [],
      'pageData.face_img_file': ''
    })
  },

  /**
   * 显示证件类型选择器
   */
  showIdentityTypePicker() {
    this.setData({
      showPicker: true,
      tempIdentityTypeIndex: this.data.identityTypeIndex
    })
  },

  /**
   * 隐藏证件类型选择器
   */
  hideIdentityTypePicker() {
    this.setData({
      showPicker: false
    })
  },

  /**
   * 选择器值变化
   */
  onPickerChange(e) {
    this.setData({
      tempIdentityTypeIndex: e.detail.value[0]
    })
  },

  /**
   * 确认选择证件类型
   */
  confirmIdentityType() {
    const index = this.data.tempIdentityTypeIndex
    this.setData({
      identityTypeIndex: index,
      'pageData.user_identity_type': this.data.columns[index],
      showPicker: false
    })
  },

  /**
   * 证件类型选择确认（兼容picker组件）
   */
  onIdentityTypeConfirm(e) {
    const index = e.detail.value
    this.setData({
      identityTypeIndex: index,
      'pageData.user_identity_type': this.data.columns[index]
    })
  },

  /**
   * 验证身份证
   */
  verifyIdentity(e) {
    const val = e.detail.value
    const valType = this.data.pageData.user_identity_type

    if (valType != '身份证') {
      return
    }

    const that = this
    wx.request({
      url: that.data.HOST + "/base/verifyIdentity",
      method: 'POST',
      data: {
        identity: val
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success(res) {
        if (res.data.success === 1) {
          that.setData({
            'BytokenObj.is_wcn': res.data.data.is_wcn,
            showPicker_state: true
          })
          wx.showToast({
            title: '身份证格式正确',
            icon: 'success'
          })
        } else {
          that.setData({
            showPicker_state: false
          })
          wx.showToast({
            title: '身份证格式错误',
            icon: 'none'
          })
        }
      },
      fail() {
        wx.showToast({
          title: '验证失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 图片访问控制（辅助函数）
   */
  imgAccessControl(url) {
    if (!url) return ''
    if (url.startsWith('http')) return url
    return this.data.HOST + url
  },

  /**
   * 提交或取消
   */
  onSubmit(e) {
    const type = e.currentTarget.dataset.type

    if (type == 0) {
      wx.navigateBack()
      return
    }

    const that = this
    const { pageData, ParentData, BytokenObj, fileList } = that.data

    // 表单验证
    if (ParentData.apply_type == 1 && fileList.length == 0) {
      wx.showToast({
        title: '请上传照片',
        icon: 'none'
      })
      return
    }

    if (!pageData.user_name) {
      wx.showToast({
        title: '请填写用户名',
        icon: 'none'
      })
      return
    }

    if (!pageData.user_phone) {
      wx.showToast({
        title: '请填写手机号',
        icon: 'none'
      })
      return
    }

    if (!pageData.user_identity_type) {
      wx.showToast({
        title: '请填写证件类型',
        icon: 'none'
      })
      return
    }

    if (!pageData.user_identity) {
      wx.showToast({
        title: '请填写证件号码',
        icon: 'none'
      })
      return
    }

    if (BytokenObj.is_wcn === 1) {
      if (!pageData.jhr_name) {
        wx.showToast({
          title: '请填写监护人姓名',
          icon: 'none'
        })
        return
      }

      if (!pageData.jhr_relation) {
        wx.showToast({
          title: '请填写监护人关系',
          icon: 'none'
        })
        return
      }

      if (!pageData.jhr_phone) {
        wx.showToast({
          title: '请填写监护人手机号',
          icon: 'none'
        })
        return
      }
    }

    if (!that.data.showPicker_state && ParentData.apply_type == 1) {
      wx.showToast({
        title: '请等待核实身份证号',
        icon: 'none'
      })
      return
    }

    // 提交数据
    that.submitData()
  },

  /**
   * 提交数据
   */
  submitData() {
    const that = this
    const { pageData, ParentData, BytokenObj } = that.data

    // 构建请求数据
    const formData = {
      company_id: ParentData.company_id,
      user_name: pageData.user_name,
      user_phone: pageData.user_phone,
      user_identity_type: pageData.user_identity_type,
      user_identity: pageData.user_identity,
      apply_type: ParentData.apply_type
    }

    if (ParentData.apply_type == 1) {
      // 帮助登记需要上传图片
      formData.face_img_file = pageData.face_img_file
    } else {
      // 本人登记使用已有头像
      formData.face_img_url = pageData.best_frame_url
    }

    if (BytokenObj.is_wcn === 1) {
      formData.jhr_name = pageData.jhr_name
      formData.jhr_phone = pageData.jhr_phone
      formData.jhr_relation = pageData.jhr_relation
    }

    // 如果是帮助登记且有图片文件，需要使用uploadFile
    if (ParentData.apply_type == 1 && pageData.face_img_file) {
      wx.uploadFile({
        url: that.data.HOST + '/company/registerOnlineRoom',
        filePath: pageData.face_img_file,
        name: 'face_img_file',
        formData: formData,
        success(res) {
          const data = JSON.parse(res.data)
          if (data.success == 1) {
            wx.showToast({
              title: '登记成功',
              icon: 'success'
            })
            setTimeout(() => {
              wx.navigateBack()
            }, 1500)
          } else {
            wx.showToast({
              title: data.dsc || '登记失败',
              icon: 'none'
            })
          }
        },
        fail() {
          wx.showToast({
            title: '网络请求失败',
            icon: 'none'
          })
        }
      })
    } else {
      // 本人登记使用普通请求
      wx.request({
        url: that.data.HOST + '/company/registerOnlineRoom',
        method: 'POST',
        data: formData,
        header: {
          'content-type': 'application/x-www-form-urlencoded'
        },
        success(res) {
          if (res.data.success == 1) {
            wx.showToast({
              title: '登记成功',
              icon: 'success'
            })
            setTimeout(() => {
              wx.navigateBack()
            }, 1500)
          } else {
            wx.showToast({
              title: res.data.dsc || '登记失败',
              icon: 'none'
            })
          }
        },
        fail() {
          wx.showToast({
            title: '网络请求失败',
            icon: 'none'
          })
        }
      })
    }
  }
})