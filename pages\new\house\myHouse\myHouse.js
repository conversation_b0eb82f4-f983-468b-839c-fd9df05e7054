// pages/new/house/myHouse/myHouse.js
var app = getApp();
var url = app.globalData.requestUrl;
var user = require('../../utils/user.js')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    inputShowed: false,
    inputVal: '',
    house: []
  },
  inputTyping: function(e) {
    var that = this;
    var data = {
      inputVal: that.data.inputVal,
    };
    data.inputVal = e.detail.value;
    that.setData(data);
  },
  showInput: function() {
    this.setData({
      inputShowed: true
    });
  },
  hideInput: function() {
    this.setData({
      inputVal: "",
      inputShowed: false,
    });
  },
  clearInput: function() {
    this.setData({
      inputVal: "",
    });
  },
  getHouseList: function(e) {
    
    var that = this;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/house/list?page_num=1&page_size=0&status=1&search_param=' + that.data.inputVal,
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          var data = {
            house: that.data.house
          }
          var house = [];
          for (let i = 0; i < res.data.data.data_list.length; i++) {
            if (res.data.data.data_list[i].img_url == null || res.data.data.data_list[i].img_url == '') {
              res.data.data.data_list[i].img_url = '/images/image_null.png';
            } else {
              res.data.data.data_list[i].img_url = url + res.data.data.data_list[i].img_url;
            }
            house.push(res.data.data.data_list[i]);
          }
          data.house = house;
          that.setData(data);
        } else {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    var that = this;
    user.getUserInfo(false).then((res) => {
      that.setData({
        userInfo: getApp().globalData.userInfo
      });
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    this.getHouseList();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

 
})