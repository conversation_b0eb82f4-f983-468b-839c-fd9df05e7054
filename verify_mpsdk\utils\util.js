const regeneratorRuntime=require("./regenerator-runtime/runtime");function compareVersion(e,r){e=e.split("."),r=r.split(".");for(var o=Math.max(e.length,r.length);e.length<o;)e.push("0");for(;r.length<o;)r.push("0");for(var t=0;t<o;t++){var s=parseInt(e[t]),a=parseInt(r[t]);if(s>a)return 1;if(s<a)return-1}return 0}let requestPromise=function(e){let{url:r,method:o="POST",data:t,header:s={"Content-Type":"application/json"}}=e;return console.log("requestPromise start:",r,t),new Promise((e,a)=>{wx.request({url:r,method:o,data:t,header:s,success(r){console.log("requestPromise success:",r),e(r)},fail(e){console.log("requestPromise error:",e),a(e)}})})},request=function(e,r){let{url:o,method:t="POST",data:s,header:a={"Content-Type":"application/json"}}=e;console.log("requestPromise start:",o,s);try{wx.request({url:o,method:t,data:s,header:a,success(e){console.log("request success:",e),200===e.statusCode&&e.data?0===e.data.ErrorCode?r({ErrorCode:0,Data:e.data.Data}):r({ErrorCode:e.data.ErrorCode,ErrorMsg:e.data.ErrorMsg,Data:e.data.Data}):r({ErrorCode:-107,ErrorMsg:"request请求异常，请稍后重试"})},fail(e){console.log("request error:",e),e.errMsg.indexOf("request:fail Unable to resolve host")>=0||e.errMsg.indexOf("request:fail 似乎已断开与互联网的连接")>=0?r({ErrorCode:101,ErrorMsg:"网络异常，请稍后重试"}):r({ErrorCode:-107,ErrorMsg:"request请求异常，请稍后重试"})}})}catch(e){console.log("request error:",e),r({ErrorCode:-107,ErrorMsg:"request请求异常"})}},uploadFile=function(e,r){console.log(e),wx.uploadFile({url:e.url,filePath:e.filePath,name:"file",formData:e.data,success:e=>{if(console.log("uploadFile| ",e),200===e.statusCode){console.log(e);let o=JSON.parse(e.data);console.log("resTemp"),console.log(o),0===o.ErrorCode?(console.log(this.data),r({ErrorCode:0,Data:o})):r({ErrorCode:o.ErrorCode,ErrorMsg:"上传视频失败，"+o.ErrorMsg})}else r({ErrorCode:101,ErrorMsg:"上传视频失败 "+e.statusCode})},fail:e=>{console.log("upload img fail",e),r({ErrorCode:101,ErrorMsg:"上传视频失败, "+e.errMsg})}}).onProgressUpdate(e=>{this.setData({"livingbody.uploadProcess":e.progress-10<0?0:e.progress-10}),console.log("progress",e.progress),console.log("already upload data",e.totalBytesSent),console.log("all upload data",e.totalBytesExpectedToSend)})},validate=function(e,r){switch(r){case"signature":return/^\S{74}={2}$/.test(e);case"appid":return"string"==typeof e&&/^\d{4}$/.test(e);case"uid":return e;case"sms_phone":return/^(13[0-9]|14[5-9]|15[012356789]|16[6]|17[0135678]|18[0-9]|19[8-9])\d{8}$/.test(e);case"sms_verifyCode":return/^\d{4}$/.test(e);case"idcard":return IDNumberValid(e);case"idname":return/^[\u4e00-\u9fa5]{1,15}[·•]?[\u4e00-\u9fa5]{1,15}$/.test(e);case"idaddress":return!!e;case"end_path":return/^\//.test(e);case"token":return/^[a-zA-Z0-9-]{36}$/.test(e)}},showModal=function(e,r){wx.showModal({title:e,content:r.replace(/(^\s*)|(\s*$)/g,""),showCancel:!1,confirmText:"我知道了",confirmColor:"#2d72f1",success:function(e){e.confirm||e.cancel}})},IDNumberValid=function(e){if(!e||!/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i.test(e))return!1;if(!{11:"北京",12:"天津",13:"河北",14:"山西",15:"内蒙古",21:"辽宁",22:"吉林",23:"黑龙江 ",31:"上海",32:"江苏",33:"浙江",34:"安徽",35:"福建",36:"江西",37:"山东",41:"河南",42:"湖北 ",43:"湖南",44:"广东",45:"广西",46:"海南",50:"重庆",51:"四川",52:"贵州",53:"云南",54:"西藏 ",61:"陕西",62:"甘肃",63:"青海",64:"宁夏",65:"新疆",71:"台湾",81:"香港",82:"澳门",91:"国外"}[e.substr(0,2)])return!1;if(18===e.length){e=e.split("");const r=[7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2],o=[1,0,"X",9,8,7,6,5,4,3,2];let t=0,s=0,a=0;for(let o=0;o<17;o++)t+=(s=e[o])*(a=r[o]);let l=o[t%11];if("x"===e[17]||"X"===e[17])return l===e[17].toUpperCase();if(l!==parseInt(e[17]))return!1}return!0},startNativeVerify=(e,r,o,t,s,a)=>{getUserIdKey(o,t,s,s=>{console.log("获取userIdKey成功:",s);let l=wx.startFacialRecognitionVerify;e&&(l=wx.startFacialRecognitionVerifyAndUploadVideo),l({userIdKey:s,checkAliveType:r,success(e){console.log("wx res:",e);let r=e.verifyResult;getWxResult(o,t,r,e=>{console.log("拿到结果了，准备传给前端"),console.log(e),a(e)})},fail(r){if(console.log(r),90100===r.errCode);else if(r.verifyResult){let e=r.verifyResult;getWxResult(o,t,e,e=>{console.log("拿到结果了，准备传给前端"),console.log(e),a(e)})}else{console.log("验证失败",r.errMsg),wx.showModal({title:"提示",content:"验证失败, "+JSON.stringify(r),showCancel:!1}),reportError(t,e?"startFacialRecognitionVerifyAndUploadVideo":"startFacialRecognitionVerify",r)}}})})},getUserIdKey=async(e,r,o,t)=>{try{let s={url:`${e}/api/liveness/getWxUserIdKey?BizToken=${r}`};wx.showLoading({title:"加载中...",mask:!0});let a=await getUserIdKeyRequest(s);wx.hideLoading(),t(a)}catch(s){console.log(s),wx.hideLoading(),15===s.ErrorCode||14===s.ErrorCode?(15===s.ErrorCode?s.ErrorMsg="当前BizToken已过期，请重试":14===s.ErrorCode&&(s.ErrorMsg="当前BizToken已验证完成"),o({BizToken:r,ErrorCode:s.ErrorCode,ErrorMsg:s.ErrorMsg})):-1===s.ErrorCode?wx.showModal({title:"提示",content:s.ErrorMsg,showCancel:!1}):wx.showModal({title:"提示",content:s.ErrorMsg,confirmText:"重试",confirmColor:"#2d72f1",success:s=>{s.confirm&&getUserIdKey(e,r,o,t)}})}},getUserIdKeyRequest=e=>(console.log(`请求 ${e.url}`),new Promise((r,o)=>{try{wx.request({url:e.url,method:"POST",data:{},success(e){console.log("request success:",e.data),0===e.data.ErrorCode?r(e.data.Data.UserIdKey):o(e.data)},fail(e){console.log("requestPromise error:",e),e.errMsg.indexOf("request:fail Unable to resolve host")>=0||e.errMsg.indexOf("request:fail 似乎已断开与互联网的连接")>=0?o({ErrorCode:101,ErrorMsg:"网络异常，请稍后重试"}):"request:fail url not in domain list"===e.errMsg?o({ErrorCode:-1,ErrorMsg:"接口还未添加到服务器域名，请点击右上角三个点，打开调试模式再试"}):o({ErrorCode:101,ErrorMsg:e.errMsg})}})}catch(e){console.log(Raven.captureException(e)+"捕获error")}})),getWxResult=async(e,r,o,t)=>{try{wx.showLoading({title:"加载中...",mask:!0});let s=await getWxResultRequest(e,r,o);wx.hideLoading(),t(s)}catch(s){console.log(s),wx.hideLoading(),-1===s.ErrorCode?wx.showModal({title:"提示",content:s.ErrorMsg,showCancel:!1}):wx.showModal({title:"提示",content:s,confirmText:"重试",confirmColor:"#2d72f1",showCancel:!1,success:s=>{s.confirm&&getWxResult(e,r,o,t)}})}},getWxResultRequest=(e,r,o)=>(console.log(`请求 ${e}/api/liveness/getWxResult?BizToken=${r}`),new Promise((t,s)=>{wx.request({url:`${e}/api/liveness/getWxResult?BizToken=${r}`,method:"POST",data:{VerifyResult:o},success(e){console.log("request success:",e.data),e.data.ErrorCode,t(e.data)},fail(e){console.log("requestPromise error:",e),e.errMsg.indexOf("request:fail Unable to resolve host")>=0||e.errMsg.indexOf("request:fail 似乎已断开与互联网的连接")>=0?s({ErrorCode:101,ErrorMsg:"网络异常，请稍后重试"}):"request:fail url not in domain list"===e.errMsg?s({ErrorCode:-1,ErrorMsg:"接口还未添加到服务器域名，请点击右上角三个点，打开调试模式再试"}):s({ErrorCode:101,ErrorMsg:e.errMsg})}})}));const reportError=(e,r,o,t)=>{const s={tag:r,error:o,source:"miniprogram"};try{s.system=wx.getSystemInfoSync()}catch(e){}return t&&(s.extra=t),console.log("上报错误：",s),new Promise((r,o)=>{wx.request({url:`${wx.verifyBaseUrl}/api/report/reportError`,method:"POST",data:{token:e,errorData:JSON.stringify(s)},success(e){r(e)},fail(e){o(e)}})})};module.exports={requestPromise:requestPromise,validate:validate,compareVersion:compareVersion,showModal:showModal,request:request,uploadFile:uploadFile,startNativeVerify:startNativeVerify,reportError:reportError};