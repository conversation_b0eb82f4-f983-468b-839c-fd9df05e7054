//app.js
var common = require('/pages/new/utils/common.js')
App({

  onLaunch: function() {
    // 获取用户信息
    console.log({
      __wxConfig,
      __wxAppCode__,
      $gwx,
      __vd_version_info__,
      __wxAppCurrentFile__,
      __wxRouteBegin,
      __wxRoute,
      __wxAppData,
      global,
      $gaic,
      $gwxc,
      definePlugin
    })
    if (typeof __wxConfig == "object") {
      let version = __wxConfig.envVersion;
      console.log("当前环境:" + version)
      if (version == "develop") {
        //工具或者真机 开发环境
        console.log("当前环境:开发环境")
      } else if (version == "trial") {
        //测试环境(体验版)
        console.log("当前环境:体验版")
      } else if (version == "release") {
        //正式环境
        console.log("当前环境:正式环境")
      }
    }
    var that = this;
    //初始化区域

    // imgAccessControl(url) {
    //   let token =encodeURIComponent (this.$store.state.user.token)
    //   return 'https://zzsbapi.jxjkga.cn/storemgmt/base/imgAccessControl2?img_url='+url+'&token='+token
    //   }
    
    wx.request({
      method: 'GET',
      url: that.globalData.requestUrl + '/storemgmt/base/area/list',
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success(res) {
        if (res.data.success == 1) {
          that.globalData.areaData = res.data.data;
          that.globalData.areaData = common.toTree(that.globalData.areaData);
          console.log(that.globalData.areaData,'这是全局变量');
        } else {

        }
      }
    })
    wx.request({
      method: 'GET',
      url: that.globalData.requestUrl + '/storemgmt/base/getJKArea',
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success(res) {
        if (res.data.success == 1) {
          that.globalData.columnsArr = res.data.data;
          // that.globalData.columnsArr = common.toTree(that.globalData.columnsArr);
          console.log(that.globalData.columnsArr);
        } else {

        }
      }
    })
    wx.getSystemInfo({
      success: e => {
        this.globalData.StatusBar = e.statusBarHeight;
        let capsule = wx.getMenuButtonBoundingClientRect();
        if (capsule) {
          this.globalData.Custom = capsule;
          this.globalData.CustomBar = capsule.bottom + capsule.top - e.statusBarHeight;
        } else {
          this.globalData.CustomBar = e.statusBarHeight + 50;
        }
      }
    })
    wx.getSetting({
      withSubscriptions: true,
      success: res => {
        let isGet = '';
        if (res.subscriptionsSetting) {
          isGet = (res.subscriptionsSetting.itemSettings) ? JSON.stringify(res.subscriptionsSetting.itemSettings) : '';
        } else {
          isGet = '';
        }
        if (isGet.includes('"03E4vnN66C-ZMmqMdVxGwD5IeI5cCcVzPwFzki-TiOY":"accept"')) {
          wx.setStorageSync('accept', '1');
        } else {
          wx.setStorageSync('accept', '0');
        }
        /*
                if (res.authSetting['scope.userInfo']) {
                  // 已经授权，可以直接调用 getUserInfo 获取头像昵称，不会弹框
                  wx.getUserInfo({
                    success: res => {
                      // 可以将 res 发送给后台解码出 unionId
                      console.log(res)
                      this.globalData.userInfo = res.userInfo
                      // 由于 getUserInfo 是网络请求，可能会在 Page.onLoad 之后才返回
                      // 所以此处加入 callback 以防止这种情况
                      if (this.userInfoReadyCallback) {
                        this.userInfoReadyCallback(res)
                      }
                    }
                  })
                }
         */
      }
    })
    
    const Verify = require('/verify_mpsdk/main');
    Verify.init();
  },
  globalData: {
    userInfo: {
      isLogin: false,
      isAuth: false,
      isAdmin: false,
      // isOpenNotice: false,
      isOpenLocation: false,
      id: 0,
      realname: '',
      id_number: '',
      data_permission: '',
      photo: '',
      mobilephone_number: '',
      car_number: '',
      address_reg: '',
      company: '',
      professional: '',
      openid:''
    },
    areaData: [], 
    columnsArr:[],
    //requestUrl: 'https://api.jialiban.cn',
    //newRequestUrl: 'https://api.mcs.jsycloud.com/index.php/',
    // requestUrl: ''http://************',
    requestUrl: 'https://zzsbapi.jxjkga.cn', 
    // requestUrl: 'http://************:9109', 
    app_id: '6cf97fa6097d6b68',
    //app_key: '131fc3c59a1303cae7ec9491daef5103' 
   app_key: '' 
  } 
  
})
function imgUrl_Get(url, callback, errFun) {
  wx.request({
    method: 'GET',
    url: 'https://zzsbapi.jxjkga.cn/storemgmt/base/imgAccessControl2' + url,
    header: {
      token: wx.getStorageSync('token')
    }, 
    success: function (res) {
      callback(res);
    },
    fail: function (err) {
      errFun(res);
    }
  })
};
module.exports = {
  imgUrl_Get: imgUrl_Get
}