/* pages/meetingIndex/meetingIndex.wxss */
.bg{
  height: 100%;
  width: 100%;
  position: absolute;
  z-index: -1;
}
.bg image{
  width: 100%;
  height: 100%;
  display: block;
}
.title{
  margin-top: 20%;
  display: flex;
  flex-direction: column;
  text-align: center;
  color: #fff;
}
.title .top{
  font-size: 56rpx;
}
.title .bottom{
  font-size: 36rpx;
  margin-top: 20rpx;
}
.btn{
  width: 80%;
  margin: 0 auto;
  margin-top: 100rpx;
}
.applicationList{
  width: 80%;
  margin: 0 auto;
  margin-top: 60rpx;
}
.applicationList .list{
  background: rgba(255, 255, 255, .8);
  border-radius: 5px;
  margin-bottom: 40rpx;
  padding: 40rpx;
  color: #333;
}
.applicationList .list text{
  font-size: 28rpx;
}
.applicationList .list .tt{
  min-width: 80px;
}
.applicationList .list .cc{
  width: 60%;
}
.applicationList .list view{
  margin-bottom: 10rpx;
}
.applicationList .list .visitInfo{
  display: flex;
}
.applicationList .list .visitInfo .cc{
  width: calc(100% - 80px);
}
.applicationList .list .visitInfo text{
  margin-right: 30rpx;
}
.applicationList .list .visitTime{
  display: flex;
}
.applicationList .list .visitTime .cc{
  width: calc(100% - 80px);
  display: flex;
  flex-direction: column;
  margin-top: 2px;
}
.applicationList .list .visitStatus{
  position: relative;
  display: flex;
}
.applicationList .list .visitStatus .cc{
  display: flex;
  flex-direction: column;
}
.applicationList .list .visitStatus button{
  margin-left: 0px;
  margin-top: 10rpx;
}