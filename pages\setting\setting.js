// pages/login/login.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
  },
  getNotice: function () {
    if (wx.getStorageSync('accept') == '0') {
      wx.requestSubscribeMessage({
        tmplIds: ['03E4vnN66C-ZMmqMdVxGwD5IeI5cCcVzPwFzki-TiOY'],
        success(res) {
          wx.showToast({
            title: '授权成功',
            icon: 'none',
            duration: 2000,
            success() {
              wx.setStorageSync('accept', '1')
            }
          })
        },
        fail() {
          wx.showToast({
            title: '授权失败',
            icon: 'none',
            duration: 2000
          })
        }
      })
    } else {
      wx.showToast({
        title: '您已授权，无需重复操作',
        icon: 'none',
        duration: 2000
      })
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

 
})