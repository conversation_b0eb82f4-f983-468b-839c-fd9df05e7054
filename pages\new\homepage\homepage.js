// pages/mine/mine.js

const http = require("../utils/httpUtils.js");
var user = require('../../../pages/new/utils/user.js')
var app = getApp();
var url = app.globalData.requestUrl;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    info_Authority:[],
    mycompanyid:'',
    mycompanyName:'',
    companyList:[],
    myHousrid:'',
    myHousrName:'',
    myHousrList:[],
    police_status:0,
    sbhc_status: 0,
    wgyr_status: 0,
    xfrw_status: 0,
    wycj_status: 0,
    is_fkgzz_state:0,
    op_status: 0,
    userInfo: null,
    imgUrls: [
      '/images/new-banner.png'
    ],
    indicatorDots: true,
    vertical: false,
    autoplay: true,
    circular: true,
    interval: 3000,
    duration: 500,
    previousMargin: 0,
    nextMargin: 0,
    SwiperHeight: '',
    toDoNum: 0,
    toDoNumwdqy: 0,
    winWid: '',
    noticeList: [],
    imgs: [],
    topImgs: [],
    modalHidden: true,
    search_param:'',
    buttonTop: 0,
    buttonLeft: 0,
    buttonRight:0,
    windowHeight: '',
    windowWidth: '',
    startPoint:'',
    endPoint:'',
    ldWorkToken:'',
    ldrkToken:'',
    isForeign:'',
  },
  getStaffAuthority(id){
    let self = this;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/company/getStaffAuthority',
      header: {
        token: wx.getStorageSync('token')
      },
      data:{
        company_id:id,
      },
      success(res) {
        if (res.data.success == 1) {
          console.log(res.data.data)
          let staffAuthority =res.data.data
          let list=[]
          if(staffAuthority != undefined){
            list = staffAuthority.authority_list.map(item => {
              return item.name
            })
            self.setData({
              info_Authority:list
            })
          }
        } else {
         
        }
      },
      fail(res) {
        console.log(res)
      }
    })
 
  },
  getCommunityDetail(id){
    let self = this;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/house/getCommunityDetail',
      header: {
        token: wx.getStorageSync('token')
      },
      data:{
        id:id,
        level:4
      },
      success(res) {
        if (res.data.success == 1) {
           self.setData({
            myHousrName:res.data.data.community_name,
          })
        } else {
         
        }
      },
      fail(res) {
        console.log(res)
      }
    })
 
  },
  getMyHouse(){
    let self = this;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/house/getMainHouses',
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          self.setData({
            myHousrList:res.data.data,
          })
          
          if(res.data.data[0]){
            let housedata = self.data.myHousrList.filter(item=>item.is_main==1)
            if(housedata.length == 0){
              self.setData({
                myHousrName:self.data.myHousrList[0].area_location_name,
                myHousrid:self.data.myHousrList[0].area_location_id
              })
            }else{
              self.setData({
                myHousrName:housedata[0].area_location_name,
                myHousrid:housedata[0].area_location_id,
              })
            }
            // self.getCommunityDetail(self.data.myHousrid)
          }else{
            self.setData({
              myHousrName:'暂未申报',
            })
          }
        } else {
         
        }
      },
      fail(res) {
        console.log(res)
      }
    })
 
  },
  getMyCompanyList(){
    let self = this;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/company/my/list',
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          self.setData({
            companyList:res.data.data,
          })
          
          if(res.data.data[0]){
            let companydata = self.data.companyList.filter(item=>item.is_main==1)
            if(companydata.length == 0){
              self.setData({
                mycompanyName:self.data.companyList[0].company_name,
                mycompanyid:self.data.companyList[0].company_id
              })
            }else{
              self.setData({
                mycompanyName:companydata[0].company_name,
                mycompanyid:companydata[0].company_id,
              })
            }
            self.getStaffAuthority(self.data.mycompanyid)
          }else{
            self.setData({
              mycompanyName:'暂未申报',
            })
          }
        } else {
         
        }
      },
      fail(res) {
        console.log(res)
      }
    })
 
  },
  getLdrkToken(){
    let self = this;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/base/getLdrkToken',
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          let ldrkToken = res.data.data
          // console.log(ldWorkToken)
          self.setData({
            ldrkToken:ldrkToken
          })
        } else {
         
        }
      },
      fail(res) {
        console.log(res)
      }
    })
 
  },
  getLdWorkToken(){
    let self = this;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/base/getLdWorkToken',
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          let ldWorkToken = res.data.data
          // console.log(ldWorkToken)
          self.setData({
            ldWorkToken:ldWorkToken
          })
        } else {
         
        }
      },
      fail(res) {
        console.log(res)
      }
    })
 
  },
  gotoycqz(){
    let self = this;
    const ldWorkToken =self.data.userInfo.ldWorkToken
    if (self.data.userInfo.isLogin) {
      if (self.data.userInfo.isAuth) {
        wx.navigateToMiniProgram({
          appId: 'wxfb6bb5cdaaed786d',
          path: '/pages/index/index?ldWorkToken='+ldWorkToken,
          extraData: {
            foo: 'bar'
          },
          envVersion: 'release',
          success(res) {
            // 打开成功
      }
        })
      } else {
        // self.doAuth();
        wx.navigateTo({
          url: '/pages/new/userCheck/userCheck'
        })
      }
    } else {
      wx.showModal({
        title: '提示',
        content: '请先注册并登录',
        success(res) {
          if (res.confirm) {
            wx.redirectTo({
              url: '/pages/new/user/login'
            })
          } else if (res.cancel) {
            wx.showToast({
              title: '登录失败',
              icon: 'none',
              duration: 2000
            })
          }
        }
      })
    }
    
  },
  gotofsacw:function(e){
    wx.navigateTo({
      url: '../fsacw/fsacw'
    })
  },
  gotowhp:function(e){
    wx.navigateTo({
      url: '../whp/whp'
    })
  },
  gotojgj:function(e){
    wx.navigateTo({
      url: '../jgj/jgj'
    })
  },
  gotocszz:function(e){
    wx.navigateTo({
      url: '../cszz/cszz'
    })
  },
  gotoXinxi:function(e){
    wx.navigateTo({
      url: '../xxfz/xxfz'
    })
  },
  //跳转到无疫创建
  gotowycj1: function (e) { 
    let self = this;
   
    if (!e.currentTarget.dataset.url) {
      return
    }
    let fkgzz_status = self.data.fkgzz_status;
    let url = e.currentTarget.dataset.type == 'h5' ? '../../myWebview/myWebview?h5=' + e.currentTarget.dataset.url + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor;
   console.log(url)
    if (e.currentTarget.dataset.type == 'alert') {
      wx.showToast({
        title: e.currentTarget.dataset.url,
        icon: 'none',
        duration: 2000
      })
    } else {
      if (self.data.userInfo.isLogin) {
        if (self.data.userInfo.isAuth) {
          
          if (fkgzz_status == 1) {
            wx.navigateTo({
              url: url,
            })
          }else{
            wx.showToast({
                  title: '您没有操作权限！',
                  icon: 'none',
                  duration: 2000
                })
          }
          
        } else {
          wx.navigateTo({
            url: '/pages/new/userCheck/userCheck'
          })
        }
      } else {
        wx.showModal({
          title: '提示',
          content: '请先注册并登录',
          success(res) {
            if (res.confirm) {
              wx.redirectTo({
                url: '/pages/new/user/login'
              })
            } else if (res.cancel) {
              wx.showToast({
                title: '登录失败',
                icon: 'none',
                duration: 2000
              })
            }
          }
        })
      }
    }
    // if (this.data.wycj_status == 1) {
    //   this.toPage(e);
    // } else {
    //   wx.showToast({
    //     title: '您没有操作权限！',
    //     icon: 'none',
    //     duration: 2000
    //   })
    // }
  },
  toPage3: function (e) {
    let url = e.currentTarget.dataset.type == 'h5' ? '../../myWebview/myWebview?h5=' + e.currentTarget.dataset.url + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor;
    wx.navigateTo({
      url: url,
    })
  },
  toPage4: function (e) {
    var search_param = this.data.search_param;
    if (search_param.trim().length < 2) {
      wx.showToast({
        title: '请至少输入两个字！',
        icon: 'none',
        duration: 2000
      }) 
      return
    } 
    let url = e.currentTarget.dataset.type == 'h5' ? '../../myWebview/myWebview?h5=' +encodeURIComponent( e.currentTarget.dataset.url+'?search_param='+search_param.trim() )+ '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor;
    wx.navigateTo({
      url: url,
    })
  },
  toPage6: function (e) { 
    let url = e.currentTarget.dataset.type == 'h5' ? '../../myWebview/myWebview?h5=' +encodeURIComponent( e.currentTarget.dataset.url+'?openpz=1'  )+ '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor;
    wx.navigateTo({
      url: url,
    })
  },
  getInputValue(e){ 
    this.setData({
      search_param: e.detail.value
    })
  },
  /**
   * 点击取消
   */
  modalCandel: function () {
    // do something
    this.setData({
      modalHidden: true
    })
  },

  /**
   *  点击确认
   */
  modalConfirm: function () {
    // do something
    this.setData({
      modalHidden: true
    })
  },
  onPageScroll: function (e) {
    // console.log(e.scrollTop) //这个就是滚动到的位置,可以用这个位置来写判断
  },
  onClick2wm(e) {
    let self = this;
    if (self.data.userInfo.isLogin) {
      if (self.data.userInfo.isAuth) {
        self.get2wm();
      } else {
        self.doAuth();
      }
    } else {
      wx.showModal({
        title: '提示',
        content: '请先注册并登录',
        success(res) {
          if (res.confirm) {
            self.get2wm();
          } else if (res.cancel) {
            wx.showToast({
              title: '登录失败',
              icon: 'none',
              duration: 2000
            })
          }
        }
      })
    }
  },
  get2wm() {
    let self = this;
    http.get('base/user/qrcode', {}, res => {
      if (res.data.success == 1) {
        self.setData({
          modalHidden: false,
          ewmUrl: 'https://zzsbapi.jxjkga.cn' + res.data.data
        })
      }
    })
  },
  //跳转到抽奖界面
  goPrize: function () {
    let self = this;

    if (self.data.userInfo.isLogin) {
      if (self.data.userInfo.isAuth) {
        wx.navigateTo({
          url: '../prize/prize',
        })
      } else {
        self.doAuth();
      }
    } else {
      wx.showModal({
        title: '提示',
        content: '请先注册并登录',
        success(res) {
          if (res.confirm) {
            wx.redirectTo({
              url: '/pages/new/user/login'
            })
          } else if (res.cancel) {
            wx.showToast({
              title: '登录失败',
              icon: 'none',
              duration: 2000
            })
          }
        }
      })
    }
    // if(wx.getStorageSync('lottery') == '0'){

    // }else{
    //   wx.navigateTo({
    //     url: self.data.ll_type == '1' ? '../huaShu/huaShu?code=' + self.data.ll_code : '../getMoney/getMoney?code=' + self.data.ll_code,
    //   })
    // }  
  },
  changeProperty: function (e) {
    var propertyName = e.currentTarget.dataset.propertyName
    var newData = {}
    newData[propertyName] = e.detail.value
    this.setData(newData)
  },
  changeIndicatorDots: function (e) {
    this.setData({
      indicatorDots: !this.data.indicatorDots
    })
  },
  changeAutoplay: function (e) {
    this.setData({
      autoplay: !this.data.autoplay
    })
  },
  intervalChange: function (e) {
    this.setData({
      interval: e.detail.value
    })
  },
  durationChange: function (e) {
    this.setData({
      duration: e.detail.value
    })
  },
  todoList: function (e) {
    wx.navigateTo({
      url: '/pages/new/todoList/todoList',
    })
  },
  bindSwiperHeight: function (e) {
    var that = this;
    var winWid = wx.getSystemInfoSync().windowWidth; //获取当前屏幕的宽度
    var imgh = e.detail.height; //图片高度
    var imgw = e.detail.width;
    var swiperH = winWid * imgh / imgw + "px"
    //等比设置swiper的高度。  即 屏幕宽度 / swiper高度 = 图片宽度 / 图片高度    ==》swiper高度 = 屏幕宽度 * 图片高度 / 图片宽度
    that.setData({
      winWid: winWid,
      SwiperHeight: swiperH
    })
  },
  toMiniProgram(){
    wx.navigateToMiniProgram({
      appId: 'wx182be45f90ffbf3d',
      path: '/pages/webwiew/index',
      extraData: {
        foo: 'bar'
      },
      envVersion: 'release',
      success(res) {
        // 打开成功
  }
    })
  },
  gotowycj: function (e) { 
    if (this.data.fkgzz_status == 1) {
      this.toPage7(e);
    } else {
      wx.showToast({
        title: '您没有操作权限！',
        icon: 'none',
        duration: 2000
      })
    }
  },
  toPage2: function (e) { 
    if (this.data.wgyr_status == 1) {
      this.toPage(e);
    } else {
      wx.showToast({
        title: '您没有操作权限，请联系负责的外国人联络员！',
        icon: 'none',
        duration: 2000
      })
    }
  },

  toPageQy: function (e) {
    let self = this;
    console.log(e)
    if (!e.currentTarget.dataset.url) {
      return
    }
    let mycompanyid = self.data.mycompanyid
    let mycompanyName = self.data.mycompanyName
    let info_manage =self.data.info_Authority.indexOf("company-info-manage") > -1 ? 1 : 0
    let staff_check =self.data.info_Authority.indexOf("company-staff_check") > -1 ? 1 : 0
    // let parram='&myHousrid='+myHousrid+'&myHousrName='+myHousrName

    let url = e.currentTarget.dataset.type == 'h5' ? '../../myWebview/myWebview?h5=' + e.currentTarget.dataset.url + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title +'&mycompanyid='+mycompanyid+'&mycompanyName='+mycompanyName+'&info_manage='+info_manage+'&staff_check='+staff_check: 
    e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor +'&mycompanyid='+mycompanyid+'&mycompanyName='+mycompanyName+'&info_manage='+info_manage+'&staff_check='+staff_check;
   
   
   console.log(url)
    if (e.currentTarget.dataset.type == 'alert') {
      wx.showToast({
        title: e.currentTarget.dataset.url,
        icon: 'none',
        duration: 2000
      })
    } else {
      if (self.data.userInfo.isLogin) {
        if (self.data.userInfo.isAuth) {
          wx.navigateTo({
            url: url,
          })
        } else {
          wx.navigateTo({
            url: '/pages/new/userCheck/userCheck'
          })
        }
      } else {
        wx.showModal({
          title: '提示',
          content: '请先注册并登录',
          success(res) {
            if (res.confirm) {
              wx.redirectTo({
                url: '/pages/new/user/login'
              })
            } else if (res.cancel) {
              wx.showToast({
                title: '登录失败',
                icon: 'none',
                duration: 2000
              })
            }
          }
        })
      }
    }
  },
  toPageXq: function (e) {
    let self = this;
    console.log(e)
    if (!e.currentTarget.dataset.url) {
      return
    }
    let myHousrid = self.data.myHousrid
    let myHousrName = self.data.myHousrName
    // let parram='&myHousrid='+myHousrid+'&myHousrName='+myHousrName

    let url = e.currentTarget.dataset.type == 'h5' ? '../../myWebview/myWebview?h5=' + e.currentTarget.dataset.url + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title +'&myHousrid='+myHousrid+'&myHousrName='+myHousrName: 
    e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor +'&myHousrid='+myHousrid+'&myHousrName='+myHousrName;
   
   
   console.log(url)
    if (e.currentTarget.dataset.type == 'alert') {
      wx.showToast({
        title: e.currentTarget.dataset.url,
        icon: 'none',
        duration: 2000
      })
    } else {
      if (self.data.userInfo.isLogin) {
        if (self.data.userInfo.isAuth) {
          wx.navigateTo({
            url: url,
          })
        } else {
          wx.navigateTo({
            url: '/pages/new/userCheck/userCheck'
          })
        }
      } else {
        wx.showModal({
          title: '提示',
          content: '请先注册并登录',
          success(res) {
            if (res.confirm) {
              wx.redirectTo({
                url: '/pages/new/user/login'
              })
            } else if (res.cancel) {
              wx.showToast({
                title: '登录失败',
                icon: 'none',
                duration: 2000
              })
            }
          }
        })
      }
    }
  },
  /**跳转页面 */
  toPage: function (e) {
    let self = this;
    
    if (!e.currentTarget.dataset.url) {
      return
    }
    // parram='id='+2+'arealocationid='+55555555
    let url = e.currentTarget.dataset.type == 'h5' ? '../../myWebview/myWebview?h5=' + e.currentTarget.dataset.url + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor;
   console.log(url)
    if (e.currentTarget.dataset.type == 'alert') {
      wx.showToast({
        title: e.currentTarget.dataset.url,
        icon: 'none',
        duration: 2000
      })
    } else {
      if (self.data.userInfo.isLogin) {
        if (self.data.userInfo.isAuth) {
          wx.navigateTo({
            url: url,
          })
        } else {
          wx.navigateTo({
            url: '/pages/new/userCheck/userCheck'
          })
        }
      } else {
        wx.showModal({
          title: '提示',
          content: '请先注册并登录',
          success(res) {
            if (res.confirm) {
              wx.redirectTo({
                url: '/pages/new/user/login'
              })
            } else if (res.cancel) {
              wx.showToast({
                title: '登录失败',
                icon: 'none',
                duration: 2000
              })
            }
          }
        })
      }
    }
  },
  toPage5: function (e) {
    let self = this;
    if (!e.currentTarget.dataset.url) {
      return
    }
    let url = e.currentTarget.dataset.type == 'h5' ? '../../myWebview/myWebview?h5=' + e.currentTarget.dataset.url + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor;
   console.log(url)
    if (e.currentTarget.dataset.type == 'alert') {
      wx.showToast({
        title: e.currentTarget.dataset.url,
        icon: 'none',
        duration: 2000
      })
    } else {

      if (self.data.userInfo.isLogin) {
        if (self.data.userInfo.isAuth) {
          wx.navigateTo({
            url: url,
          })
        } else {
          wx.navigateTo({
            url: '/pages/new/jwCheck/jwCheck'
          })
        }
      } else {
        wx.showModal({
          title: '提示',
          content: '请先注册并登录',
          success(res) {
            if (res.confirm) {
              wx.redirectTo({
                url: '/pages/new/user/login'
              })
            } else if (res.cancel) {
              wx.showToast({
                title: '登录失败',
                icon: 'none',
                duration: 2000
              })
            }
          }
        })
      }
    }
  },
  toPage7: function (e) {
    let self = this;
    
    if (!e.currentTarget.dataset.url) {
      return
    }
    let url = e.currentTarget.dataset.type == 'h5' ? '../../myWebview/myWebview?h5=' + e.currentTarget.dataset.url + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor+ '&company_id='+1;
   console.log(url)
    if (e.currentTarget.dataset.type == 'alert') {
      wx.showToast({
        title: e.currentTarget.dataset.url,
        icon: 'none',
        duration: 2000
      })
    } else {
      if (self.data.userInfo.isLogin) {
        if (self.data.userInfo.isAuth) {
          wx.navigateTo({
            url: url,
          })
        } else {
          wx.navigateTo({
            url: '/pages/new/userCheck/userCheck'
          })
        }
      } else {
        wx.showModal({
          title: '提示',
          content: '请先注册并登录',
          success(res) {
            if (res.confirm) {
              wx.redirectTo({
                url: '/pages/new/user/login'
              })
            } else if (res.cancel) {
              wx.showToast({
                title: '登录失败',
                icon: 'none',
                duration: 2000
              })
            }
          }
        })
      }
    }
  },
  /**验证是否人证核实 */
  doAuth: function () {
    user.doAuth();
  },
  doLogin: function () {
    var that = this;
    user.getUserInfo().then((res) => {
      that.setData({
        userInfo: getApp().globalData.userInfo
      });
      if (user.checkPermissionByFuncName("house-check")) {
        that.setData({
          op_status: 1
        });
      } else {
        that.setData({
          op_status: 0
        });
      }
      if (user.checkPermissionByFuncName("foreigner-manage")) {
        that.setData({
          wgyr_status: 1
        });
      } else {
        that.setData({
          wgyr_status: 0
        });
      } 
    if (user.checkPermissionByFuncName("person-check")) {
      that.setData({
        sbhc_status: 1
      });
    } else {
      that.setData({
        sbhc_status: 0
      });
    }
    if (user.checkPermissionByFuncName("police-check")) {
      that.setData({
        police_status: 1
      });
    } else {
      that.setData({
        police_status: 0
      });
    }

    if (user.checkPermissionByFuncName("epidemic-free")) {
      that.setData({
        wycj_status: 1
      });
    } else {
      that.setData({
        wycj_status: 0
      });
    }
    if (user.get_is_fkgzz_state()) {
      that.setData({
        fkgzz_status: 1
      });
    }else {
      that.setData({
        fkgzz_status: 0
      });
    }
    if (user.checkPermissionByFuncName("publish-building-task")) {
      that.setData({
        xfrw_status: 1
      });
    } else {
      that.setData({
        xfrw_status: 0
      });
    }

  });
    console.log(this.data);
  },
  bindHouseSelectRole: function () {
    var that = this;
    wx.showToast({
      title: '请选择角色',
      icon: 'none',
      duration: 2000
    })
    wx.showActionSheet({
      itemList: ['我是房东', '我是租客', '自住'],
      success(res) {
        if (that.data.userInfo.isLogin) {
          if (that.data.userInfo.isAuth) {
            if (res.tapIndex == 0) {
              wx.navigateTo({
                url: '/pages/new/house/declareFangDong/declareFangDong/declareFangDong?type=1',
              })
            } else if (res.tapIndex == 2) {
              wx.navigateTo({
                url: '/pages/new/house/declareFangDong/declareFangDong/declareFangDong?type=2',
              })
            } else {
              wx.navigateTo({
                url: '/pages/new/house/declareZuKe/searchHouse/searchHouse',
              })
            }
          } else {
            that.doAuth();
          }
        } else {
          that.doLogin();
        }
      },
      fail(res) {
        console.log(res.errMsg)
      }
    })
  },


  getnotice: function () {
    if (wx.getStorageSync('token')) {
      http.get('house/notice/list', {
        page_num: 1,
        page_size: 2,
        token: wx.getStorageSync('token')
      }, res => {
        if (res.data.success == 1) {
          let mynoticeList = res.data.data.data_list;
          this.setData({
            noticeList: mynoticeList
          });
        }
      })
    }
  },
  getimgs: function () {
    http.get('base/getAdvertisePicInfo', {
      type: "小程序首页mid图"
    }, res => {
      if (res.data.success == 1) {
        let myImgs = res.data.data;
        this.setData({
          imgs: myImgs, 
        });
      }
    });

    http.get('base/getAdvertisePicInfo', {
      type: "小程序首页top图"
    }, res => {
      if (res.data.success == 1) {
        let myImgs2 = res.data.data;
        // myImgs2[0].url = myImgs2[0].url+ '?m='+Math.random(); 
        this.setData({
          topImgs: myImgs2
        });
        wx.setNavigationBarColor({
          frontColor: '#ffffff', // 必写项
          backgroundColor:  myImgs2[0].colour, // 传递的颜色值 
        }) 
      }
    })
  },
  getDbCount: function () {
    var that = this;
    if (wx.getStorageSync('token')) {
      http.get('company/getHouseCompanyTodoNum', {}, res => {
        if (res.data.success == 1) {
          if (res.data.data > 0) {
          
            that.setData({
              toDoNum: res.data.data
            });
          } else {
           
            that.setData({
              toDoNum: 0
            });
          }
        } else {
          wx.removeTabBarBadge({
            index: 1, //tabBar下标（底部tabBar的位置，从0开始）
          });
          that.setData({
            toDoNum: 0
          });
        }
      })
    }
  },
  
  getDbCountWdyq: function () {
    var that = this;
    if (wx.getStorageSync('token')) {
      http.get('company/getMyCompanyExamineTodoNum', {
        token: wx.getStorageSync('token')
      }, res => {
        if (res.data.success == 1) {
          if (res.data.data.todo_num > 0) {
            that.setData({
              toDoNumwdqy: res.data.data.todo_num
            });
            console.log(res.data,'**************');
          } else {
            that.setData({
              toDoNumwdqy: 0
            });
          }
        } else {
          wx.removeTabBarBadge({
            index: 1, //tabBar下标（底部tabBar的位置，从0开始）
          });
          that.setData({
            toDoNumwdqy: 0
          });
        }
      })
    }
  },
  buttonStart: function (e) {
    var startPoint = e.touches[0]//获取拖动开始点
    this.setData({
      startPoint:startPoint
    })
  },
  buttonMove: function (e) {
    var startPoint= this.data.startPoint;
    var endPoint = e.touches[e.touches.length - 1]//获取拖动结束点
    //计算在X轴上拖动的距离和在Y轴上拖动的距离
    var translateX = endPoint.clientX - startPoint.clientX
    var translateY = endPoint.clientY - startPoint.clientY
    
    startPoint = endPoint//重置开始位置
    var buttonTop = this.data.buttonTop + translateY
    var buttonLeft = this.data.buttonLeft + translateX
    //判断是移动否超出屏幕
    if (buttonLeft+50 >= this.data.windowWidth){
      buttonLeft = this.data.windowWidth-50;
    }
    if (buttonLeft<=0){
      buttonLeft=0;
    }
    if (buttonTop<=0){
      buttonTop=0
    }
    if (buttonTop + 50 >= this.data.windowHeight){
      buttonTop = this.data.windowHeight-50;
    }
    this.setData({
      buttonTop: buttonTop,
      buttonLeft: buttonLeft
    })
  },
  buttonEnd: function (e) {
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    that.getLdWorkToken()
    that.getLdrkToken()
    that.getMyHouse()
    that.getMyCompanyList()
    user.getUserInfo(false).then((res) => {
      this.setData({
        userInfo: getApp().globalData.userInfo
      });
      wx.request({
        method:"POST",
        url:url+"/storemgmt/base/getMyHomePage",
        header: {
          token: wx.getStorageSync('token')
        },
        success:(res)=>{
          if(res.data.success==1 && res.data.data && res.data.data.url){
            console.log(res.data.data.url)
            wx.navigateTo({
              url: "../../myWebview/myWebview?h5="+res.data.data.url+`&utoken=${wx.getStorageSync('token')}`
            })
          }
          
        }
      })
      
      if (user.checkPermissionByFuncName("house-check")) {
        that.setData({
          op_status: 1
        });
      } else {
        that.setData({
          op_status: 0
        });
      }
      if (user.checkPermissionByFuncName("foreigner-manage")) {
        that.setData({
          wgyr_status: 1
        });
      } else {
        that.setData({
          wgyr_status: 0
        });
      }
      if (user.checkPermissionByFuncName("publish-building-task")) {
        that.setData({
          xfrw_status: 1
        });
      } else {
        that.setData({
          xfrw_status: 0
        });
      }
  
      if (user.checkPermissionByFuncName("person-check")) {
        that.setData({
          sbhc_status: 1
        });
      } else {
        that.setData({
          sbhc_status: 0
        });
      }

      if (user.checkPermissionByFuncName("epidemic-free")) {
        that.setData({
          wycj_status: 1
        });
      } else {
        that.setData({
          wycj_status: 0
        });
      }
      if (user.get_is_fkgzz_state()) {
        that.setData({
          fkgzz_status: 1
        });
      }else {
        that.setData({
          fkgzz_status: 0
        });
      }
      if (user.checkPermissionByFuncName("police-check")) {
        that.setData({
          police_status: 1
        });
      } else {
        that.setData({
          police_status: 0
        });
      }
    });

   
 
    // 获取购物车控件适配参数
    wx.getSystemInfo({
      success: function (res) {
        console.log(res);
        // 屏幕宽度、高度
        console.log('height=' + res.windowHeight);
        console.log('width=' + res.windowWidth);
        // 高度,宽度 单位为px
        that.setData({
          windowHeight:  res.windowHeight,
          windowWidth:  res.windowWidth,
          buttonTop:res.windowHeight*0.8,//这里定义按钮的初始位置
          buttonLeft:res.windowWidth*0.73,//这里定义按钮的初始位置
        })
      }
    })

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.getimgs();
    this.getDbCount();
    var that = this;
    if(wx.getStorageSync('token') ) {
  
      this.getDbCountWdyq();
      this.getnotice();
    }

    if (user.checkPermissionByFuncName("foreigner-manage")) {
      that.setData({
        wgyr_status: 1
      });
    } else {
      that.setData({
        wgyr_status: 0
      });
    }
    if (user.checkPermissionByFuncName("publish-building-task")) {
      that.setData({
        xfrw_status: 1
      });
    } else {
      that.setData({
        xfrw_status: 0
      });
    }
    if (user.checkPermissionByFuncName("police-check")) {
      that.setData({
        police_status: 1
      });
    } else {
      that.setData({
        police_status: 0
      });
    }
    if (user.checkPermissionByFuncName("epidemic-free")) {
      that.setData({
        wycj_status: 1
      });
    } else {
      that.setData({
        wycj_status: 0
      });
    }
    
    if (user.get_is_fkgzz_state()) {
      that.setData({
        fkgzz_status: 1
      });
    }else {
      that.setData({
        fkgzz_status: 0
      });
    }
    if (user.checkPermissionByFuncName("person-check")) {
      that.setData({
        sbhc_status: 1
      });
    } else {
      that.setData({
        sbhc_status: 0
      });
    }
    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})