// pages/system/system.js

const http = require("../../pages/new/utils/httpUtils.js");
var user = require('../../pages/new/utils/user.js')
Page({
  /**
   * 页面的初始数据
   */
  data: {
    web_url: '',
    userInfo: null,
  },
  getPara: function (name, url) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
    var r = url.substr(1).match(reg); //匹配目标参数
    if (r != null) return r[2];
    return ""; //返回参数值
  },
  /**
   * 生命周期函数--监听页面加载
   */
  /* 获取用户地点 */
  onLoad: function (options) {
    console.log(options)
    var that = this;
    wx.getLocation({
      type: 'wgs84',
      success: function (result) { 

        let token = wx.getStorageSync('token');
        
        //获取解码后的url
        let h5 = '';
        
        if (options.h5) { 
          h5 = decodeURIComponent(options.h5);
          console.log(h5);
          //通过正则获取url中的参数 
          let baseUrl = h5.split("?")[0];
          let target_url = '';
          
          if (token) {
            target_url = baseUrl + '?utoken=' + encodeURIComponent(token) + '&latitude=' + result.latitude + '&longitude=' + result.longitude;
            if(options.myHousrid){
              target_url = baseUrl + '?utoken=' + encodeURIComponent(token) + '&latitude=' + result.latitude + '&longitude=' + result.longitude+'&id='+options.myHousrid+'&name='+options.myHousrName+'&l=4';
            }
            if(options.mycompanyid){
              target_url = baseUrl + '?utoken=' + encodeURIComponent(token) + '&latitude=' + result.latitude + '&longitude=' + result.longitude+'&company_id='+options.mycompanyid+'&company_name='+options.mycompanyName+'&info_manage='+options.info_manage+'&staff_check='+options.staff_check;
            }
          } else {
            target_url = baseUrl + '?a=1' + '&latitude=' + result.latitude + '&longitude=' + result.longitude;
            console.log(target_url)
          }
          if (h5.split("?").length > 1) {
            
            let paramUrl = h5.split("?")[1];
            let paramArray = paramUrl.split("&");
            for (var i = 0; i < paramArray.length; i++) {
              let key = paramArray[i].split("=")[0];
              let value = paramArray[i].split("=")[1];
              if (key != "utoken") {
                target_url = target_url + "&" + key + "=" + value
              }
            }
          }
          user.getUserInfo(false).then((res) => {
            that.setData({
              userInfo: getApp().globalData.userInfo
            });

            //判断url中是否包含fzxc1（反诈宣传答题）  有的话加上手机号 加上是否答题完成的标识
            // that.data.userInfo.phone
            if (target_url.indexOf("fzxc1") > -1) {
              target_url = target_url + "&phone=" + that.data.userInfo.mobilephone_number;
              http.get('OpenApi/isComplete', {
                phone: that.data.userInfo.mobilephone_number,
              }, res => {
                if (res.data.success == 1) {
                  target_url = target_url + "&state=" + res.data.data;
                  that.setData({
                    web_url: target_url
                  })
                  wx.setNavigationBarTitle({
                    title: options.title
                  })
                  wx.setNavigationBarColor({
                    frontColor: options.fontcolor,
                    backgroundColor: options.bgcolor,
                    animation: {
                      duration: 400,
                      timingFunc: 'easeIn'
                    }
                  })
                }
              })
            } else {
              
              that.setData({
                web_url: target_url
              })
              wx.setNavigationBarTitle({
                title: options.title
              })
              wx.setNavigationBarColor({
                frontColor: options.fontcolor,
                backgroundColor: options.bgcolor,
                animation: {
                  duration: 400,
                  timingFunc: 'easeIn'
                }
              })
            }
          })
        } else if (options.q) { 
          user.getUserInfo(false).then((res) => {
            that.setData({
              userInfo: getApp().globalData.userInfo
            });
            
            if (!that.data.userInfo.isLogin) {

              let myurl = (decodeURIComponent(options.q).split("h5=")[1]);
              wx.redirectTo({
                url: '/pages/ewmWebview/ewmWebview?h5=' + encodeURIComponent(myurl)
              })
            } else {
              h5 = decodeURIComponent(decodeURIComponent(options.q).split("h5=")[1]);
              console.log(h5);
              
              //通过正则获取url中的参数 
              let baseUrl = h5.split("?")[0];
              let url =h5;
              if(url.indexOf('?') != -1){
                let obj = {};
                let uuid =''
                let arr = url.slice(url.indexOf('?')+1).split('&');
                arr.forEach(item => {
                  let param = item.split('=');
                  console.log(param);
                  uuid= param[1]
                  obj[param[0]] = param[1];
                })
                console.log(obj,uuid);
              }
              
              let target_url = '';
              if (token) {
                target_url = baseUrl + '?utoken=' + encodeURIComponent(token) + '&latitude=' + result.latitude + '&longitude=' + result.longitude;
                console.log(target_url)
              } else {
                target_url = baseUrl + '?a=1' + '&latitude=' + result.latitude + '&longitude=' + result.longitude;
              }
              if (h5.split("?").length > 1) {
                let paramUrl = h5.split("?")[1];
                let paramArray = paramUrl.split("&");
                for (var i = 0; i < paramArray.length; i++) {
                  let key = paramArray[i].split("=")[0];
                  let value = paramArray[i].split("=")[1];
                  target_url = (target_url + ("&" + key + "=" + value));
                  console.log(target_url)
                }
              }
              if (target_url.indexOf("fzxc1") > -1) {
                
                target_url = target_url + "&phone=" + that.data.userInfo.mobilephone_number;
                http.get('OpenApi/isComplete', {
                  phone: that.data.userInfo.mobilephone_number,
                }, res => {
                  if (res.data.success == 1) {
                    target_url = target_url + "&state=" + res.data.data;
                    
                    that.setData({
                      web_url: target_url
                    })
                    
                    wx.setNavigationBarTitle({
                      title: options.title
                    })
                    wx.setNavigationBarColor({
                      frontColor: options.fontcolor,
                      backgroundColor: options.bgcolor,
                      animation: {
                        duration: 400,
                        timingFunc: 'easeIn'
                      }
                    })
                  }
                })

              } else {
                that.setData({
                  web_url: target_url
                })
                wx.setNavigationBarTitle({
                  title: options.title
                })
                wx.setNavigationBarColor({
                  frontColor: options.fontcolor,
                  backgroundColor: options.bgcolor,
                  animation: {
                    duration: 400,
                    timingFunc: 'easeIn'
                  }
                })

              }
            }
          });
        }
      },
      fail: function () {
         
        let token = wx.getStorageSync('token');
        //获取解码后的url
        let h5 = '';
        if (options.h5) {
          
          h5 = decodeURIComponent(options.h5);
          //通过正则获取url中的参数 
          let baseUrl = h5.split("?")[0];
          let target_url = '';
          if (token) {
            target_url = baseUrl + '?utoken=' + encodeURIComponent(token);
          } else {
            target_url = baseUrl + '?a=1';
          }
          if (h5.split("?").length > 1) {
            let paramUrl = h5.split("?")[1];
            let paramArray = paramUrl.split("&");
            for (var i = 0; i < paramArray.length; i++) {
              let key = paramArray[i].split("=")[0];
              let value = paramArray[i].split("=")[1];
              if (key != "utoken") {
                target_url = target_url + "&" + key + "=" + value
              }
            }
          }
          user.getUserInfo(false).then((res) => {
            that.setData({
              userInfo: getApp().globalData.userInfo
            });

            //判断url中是否包含fzxc1（反诈宣传答题）  有的话加上手机号 加上是否答题完成的标识
            // that.data.userInfo.phone
            if (target_url.indexOf("fzxc1") > -1) {
              target_url = target_url + "&phone=" + that.data.userInfo.mobilephone_number;
              http.get('OpenApi/isComplete', {
                phone: that.data.userInfo.mobilephone_number,
              }, res => {
                if (res.data.success == 1) {
                  target_url = target_url + "&state=" + res.data.data;
                  that.setData({
                    web_url: target_url
                  })
                  wx.setNavigationBarTitle({
                    title: options.title
                  })
                  wx.setNavigationBarColor({
                    frontColor: options.fontcolor,
                    backgroundColor: options.bgcolor,
                    animation: {
                      duration: 400,
                      timingFunc: 'easeIn'
                    }
                  })
                }
              })
            } else {
              
              that.setData({
                web_url: target_url
              })
              wx.setNavigationBarTitle({
                title: options.title
              })
              wx.setNavigationBarColor({
                frontColor: options.fontcolor,
                backgroundColor: options.bgcolor,
                animation: {
                  duration: 400,
                  timingFunc: 'easeIn'
                }
              })
            }
          })
        } else if (options.q) {

          user.getUserInfo(false).then((res) => {
            that.setData({
              userInfo: getApp().globalData.userInfo
            });

            if (!that.data.userInfo.isLogin) { 
              let myurl = (decodeURIComponent(options.q).split("h5=")[1]);
              wx.redirectTo({
                url: '/pages/ewmWebview/ewmWebview?h5=' + encodeURIComponent(myurl)
              })
            } else { 
              h5 = decodeURIComponent(decodeURIComponent(options.q).split("h5=")[1]);
              //通过正则获取url中的参数 
              let baseUrl = h5.split("?")[0];
              let target_url = '';
              if (token) {
                target_url = baseUrl + '?utoken=' + encodeURIComponent(token);
              } else {
                target_url = baseUrl + '?a=1';
              }
              if (h5.split("?").length > 1) {
                let paramUrl = h5.split("?")[1];
                let paramArray = paramUrl.split("&");
                for (var i = 0; i < paramArray.length; i++) {
                  let key = paramArray[i].split("=")[0];
                  let value = paramArray[i].split("=")[1];
                  target_url = (target_url + ("&" + key + "=" + value));
                }
              }
              if (target_url.indexOf("fzxc1") > -1) {
                target_url = target_url + "&phone=" + that.data.userInfo.mobilephone_number;
                http.get('OpenApi/isComplete', {
                  phone: that.data.userInfo.mobilephone_number,
                }, res => {
                  if (res.data.success == 1) {
                    target_url = target_url + "&state=" + res.data.data;
                    that.setData({
                      web_url: target_url
                    })
                    wx.setNavigationBarTitle({
                      title: options.title
                    })
                    wx.setNavigationBarColor({
                      frontColor: options.fontcolor,
                      backgroundColor: options.bgcolor,
                      animation: {
                        duration: 400,
                        timingFunc: 'easeIn'
                      }
                    })
                  }
                })

              } else {
                that.setData({
                  web_url: target_url
                })
                wx.setNavigationBarTitle({
                  title: options.title
                })
                wx.setNavigationBarColor({
                  frontColor: options.fontcolor,
                  backgroundColor: options.bgcolor,
                  animation: {
                    duration: 400,
                    timingFunc: 'easeIn'
                  }
                })

              }
            }
          });
        }


      },
    })
    
    wx.getSetting({
      withSubscriptions: true,   //  这里设置为true,下面才会返回mainSwitch
      success: function(res){   
        // 调起授权界面弹窗
        if (res.subscriptionsSetting.mainSwitch) {  // 用户打开了订阅消息总开关
          if (res.subscriptionsSetting.itemSettings != null) {   // 用户同意总是保持是否推送消息的选择, 这里表示以后不会再拉起推送消息的授权
            let moIdState = res.subscriptionsSetting.itemSettings['03E4vnN66C-ZMmqMdVxGwD5IeI5cCcVzPwFzki-TiOY'];  // 用户同意的消息模板id
            if(moIdState === 'accept'){   
              console.log('接受了消息推送');

            }else if(moIdState === 'reject'){
              console.log("拒绝消息推送");

            }else if(moIdState === 'ban'){
              console.log("已被后台封禁");

            }
          }else {
            // 当用户没有点击 ’总是保持以上选择，不再询问‘  按钮。那每次执到这都会拉起授权弹窗
            wx.showModal({
              title: '提示',
              content:'请授权开通服务通知',
              showCancel: true,
              success: function (ress) {
                if (ress.confirm) {  
                  wx.requestSubscribeMessage({   // 调起消息订阅界面
                    tmplIds: ['03E4vnN66C-ZMmqMdVxGwD5IeI5cCcVzPwFzki-TiOY'],
                    success (res) { 
                      console.log('订阅消息 成功 ');
                      console.log(res);
                    },
                    fail (er){
                      console.log("订阅消息 失败 ");
                      console.log(er);
                    }
                  })       
                }
              }
            })
          }
        }else {
          console.log('订阅消息未开启')
        }      
      },
      fail: function(error){
        console.log(error);
      },
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },
  doLogin: function () {
    var that = this;

    user.getUserInfo().then((res) => {
      that.setData({
        userInfo: getApp().globalData.userInfo
      });
      let a = self.data.userInfo;

    });



    console.log(this.data);
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
  
   
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
   
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },


})