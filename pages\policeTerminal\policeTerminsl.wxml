<!--pages/policeTerminal/policeTerminsl.wxml-->
<!-- <text>pages/policeTerminal/policeTerminsl.wxml</text> -->
<!--  -->
<view class="content">
  <watermark></watermark>
  <view class="topNav">
    <view class="modeSwitch" bindtap="gotoIndex">
      <image src="https://zzsbapi.jxjkga.cn/wxzzsb/new/QieHuan.png" mode="aspectFit"></image>
      <view>模式切换</view>
    </view>
    <view class="search">
      <input bindinput='getInputValue' name="search_param" placeholder="姓名/手机/车牌/小区/楼栋/门牌" placeholder-class="small-placeholder"/>
      <text style="color:white;">|</text>
      <text style="font-weight:bold;color:white;" bindtap="toPage4" data-bgcolor="#ffffff" data-fontcolor="#000000" data-title="嘉兴公安自主申报" data-url="https://zzsbui.jxjkga.cn/#/searchManage" data-type="h5">搜索</text>
    </view>
  </view>
  <view class="topButton">
    <view class="item">
      <image bindtap="toPage" data-bgcolor="#ffffff" data-fontcolor="#000000" data-title="嘉兴公安自主申报" data-url="https://zzsbui.jxjkga.cn/#/xqzzsb" data-type="h5" src="https://zzsbapi.jxjkga.cn/wxzzsb/new/fangwuShenbao.png" mode="aspectFit"></image>
    </view>
    <view class="item">
      <image bindtap="toPage" data-bgcolor="#ffffff" data-fontcolor="#000000" data-title="嘉兴公安自主申报" data-url="https://zzsbui.jxjkga.cn/#/xqtkewm/xqtkm" data-type="h5" src="https://zzsbapi.jxjkga.cn/wxzzsb/new/qiyeshenbao.png" mode="aspectFit"></image>
    </view>
    <view class="item">
      <image bindtap="toPage" data-bgcolor="#ffffff" data-fontcolor="#000000" data-title="嘉兴公安自主申报" data-url="https://zzsbui.jxjkga.cn/#/qyzzsb" data-type="h5" src="https://zzsbapi.jxjkga.cn/wxzzsb/new/tikongmabutton.png" mode="aspectFit"></image>
    </view>
    <view class="item">
      <image bindtap="toPage2" data-bgcolor="#ffffff" data-fontcolor="#000000" data-title="嘉兴公安自主申报" data-url="https://zzsbui.jxjkga.cn/#/jwzzsb" data-type="h5" src="https://zzsbapi.jxjkga.cn/wxzzsb/new/jinwaiShenbao.png" mode="aspectFit"></image>
    </view>
  </view>
  <view class="topContent">
    <image src="https://zzsbapi.jxjkga.cn/wxzzsb/new/BannerCxy.png" mode="widthFix"></image>
    <view class="function">
      <view class="title">常用功能</view>
      <view class="functionBtn">
        <view class="item">
          <image bindtap="toPage" data-bgcolor="#ffffff" data-fontcolor="#000000" data-title="人像核查" data-url="https://zzsbui.jxjkga.cn/#/serachUserInfo" data-type="h5" src="https://zzsbapi.jxjkga.cn/wxzzsb/new/rxhcCy.png" mode="aspectFit"></image>
        </view>
        <view class="item">
          <image bindtap="toPage" data-bgcolor="#ffffff" data-fontcolor="#000000" data-title="电子房卡" data-url="https://zzsbui.jxjkga.cn/#/wyfsb/wdfydj" data-type="h5" src="https://zzsbapi.jxjkga.cn/wxzzsb/new/wyfcy.png" mode="aspectFit"></image>
        </view>
        <view class="item">
          <image bindtap="toPage" data-bgcolor="#ffffff" data-fontcolor="#000000" data-title="日常巡楼" data-url="https://zzsbui.jxjkga.cn/#/xqzzsb/htgl/sbgl" data-type="h5" src="https://zzsbapi.jxjkga.cn/wxzzsb/new/sbglcy.png" mode="aspectFit"></image>
        </view>
        <view class="item">
          <image bindtap="toPage" data-bgcolor="#ffffff" data-fontcolor="#000000" data-title="小区管理" data-url="https://zzsbui.jxjkga.cn/#/xqzzsb/htgl/fwgl" data-type="h5" src="https://zzsbapi.jxjkga.cn/wxzzsb/new/xqglcy.png" mode="aspectFit"></image>
        </view>
        <view class="item alarm">
          <image bindtap="toPage" data-bgcolor="#ffffff" data-fontcolor="#000000" data-title="嘉兴公安自主申报" data-url="https://zzsbui.jxjkga.cn/#/alldbsx" data-type="h5" src="https://zzsbapi.jxjkga.cn/wxzzsb/new/daibancy.png" mode="aspectFit"></image>
        </view>
        <view class="item alarm">
          <image src="https://zzsbapi.jxjkga.cn/wxzzsb/new/gdgnCy.png" mode="aspectFit"></image>
          <view class="divright _div">3</view>
        </view>
      </view>
    </view>
  </view>
  <view class="mainContent" style="height:{{contentheight}}">
    <view class="mainBox">
      <view class="mainTitle">
        <block wx:for="{{options}}" wx:for-item="item" wx:for-index="index" wx:key="index">
          <view class="option" style="background-color: {{item.bg}};" bindtap="selectOption" data-index="{{index}}">{{item.text}}</view>
        </block>
        <view class="shezhi" bindtap="selectSet">
          <image src="https://zzsbapi.jxjkga.cn/wxzzsb/new/shezhi.png" mode="widthFix"></image>
        </view>
      </view>
      <!-- <view>{{show0}}</view>
      <view>{{show1}}</view>
      <view>{{show2}}</view> -->
      <view hidden="{{!show0}}">
        <view class="main">
          <view class="item" bindtap="toPageworksp11" data-bgcolor="#ffffff" data-fontcolor="#000000" data-title="嘉兴公安自主申报" data-name="经开公安分局" data-company_id="1" data-police_type_id="1" data-police_type_name="反恐" data-url="https://zzsbui.jxjkga.cn/#/dcgz" data-type="h5">
            <image src="https://zzsbapi.jxjkga.cn/wxzzsb/new/FKgzz/zdhydj.png" mode="widthFix"></image>
            <view class="text">
              <view class="title">重点行业</view>
              <view class="wenzi">查看辖区重点企业</view>
            </view>
          </view>
          <view class="item" bindtap="toPageworksp" data-bgcolor="#ffffff" data-fontcolor="#000000" data-title="嘉兴公安自主申报" data-url="https://zzsbui.jxjkga.cn/#/fkgz/mgwztj" data-type="h5">
            <image src="https://zzsbapi.jxjkga.cn/wxzzsb/new/FKgzz/mgwzdj.png" mode="widthFix"></image>
            <view class="text">
              <view class="title">敏感物资</view>
              <view class="wenzi">辖区物资去向</view>
            </view>
          </view>
        </view>
        <view class="main">
          <view class="item" bindtap="toPageworksp" data-bgcolor="#ffffff" data-fontcolor="#000000" data-title="嘉兴公安自主申报" data-url="https://zzsbui.jxjkga.cn/#/ssmzfw" data-type="h5">
            <image src="https://zzsbapi.jxjkga.cn/wxzzsb/new/FKgzz/ssmzdj.png" mode="widthFix"></image>
            <view class="text">
              <view class="title">少数民族服务</view>
              <view class="wenzi">辖区人员数据</view>
            </view>
          </view>
          <view class="item" bindtap="toPageworksp" data-bgcolor="#ffffff" data-fontcolor="#000000" data-title="嘉兴公安自主申报" data-name="经开公安分局" data-company_id="1" data-police_type_id="1" police_type_name="反恐" data-url="https://zzsbui.jxjkga.cn/#/dcgz" data-type="h5">
            <image src="https://zzsbapi.jxjkga.cn/wxzzsb/new/FKgzz/lsrwxf.png" mode="widthFix"></image>
            <view class="text alarm">
              <view class="title">我的待办</view>
              <view class="divright divright2">10</view>
              <view class="wenzi">待办事项</view>
            </view>
          </view>
        </view>
        <view class="line"></view>
        <view class="functionBox">
          <block wx:for="{{workstation}}" wx:for-item="item" wx:for-index="index" wx:key="index">
            <block>
              <image src="{{item.url}}" mode="aspectFit"></image>
              <view>{{''+item.text+">"}}</view>
            </block>
          </block>
        </view>
        <view class="bottomContent">
          <!-- <view class="title">{{''+options[0].text+''}}</view> -->
          <view class="title">工作站</view>
          <view class="table">
            <view class="tr"><text class="th" style="padding:0 40rpx;"></text><text class="th">{{total_company_count}}家</text><text class="th">{{total_company_num}}家</text><text class="th">{{total_ssmz_num}}人</text></view>
            <view class="tr"><text class="th"></text><text class="th">物资单位</text><text class="th">重点行业</text><text class="th">少数民族</text></view>
            <view class="tr"><text class="td"></text>
              <block wx:for="{{3}}" wx:for-item="item" wx:for-index="__i0__" wx:key="__i0__"><text class="td" style="border-bottom:1rpx solid #eee;"></text></block>
            </view>
            <view class="tr">
              <text class="td"></text><text class="td">月销量/月核查/月新增/单位数</text><text class="td">月自查/月检查/单位数</text><text class="td">月新增/月走访/人员总数</text>
            </view>
            <view class="tr">
              <text class="td">{{ssmz_list[0].station_name}}</text>
              <text class="td">{{company_list[0].cn_sale_count}}/{{company_list[0].cn_check_count}}/{{company_list[0].cn_new_company_count}}/{{company_list[0].cn_company_count}}</text>
              <text class="td">{{examine_list[0].cn_examine_count}}/{{examine_list[0].cn_overseer_count}}/{{examine_list[0].cn_company_count}}</text>
              <text class="td">{{ssmz_list[0].ry_incr_count}}/{{ssmz_list[0].zf_count}}/{{ssmz_list[0].ssmz_count}}</text>
            </view>
            <view class="tr"><text class="td"></text>
              <block wx:for="{{3}}" wx:for-item="item" wx:for-index="__i1__" wx:key="__i1__"><text class="td" style="border-bottom:1rpx solid #eee;"></text></block>
            </view>
            <view class="tr">
              <text class="td">{{ssmz_list[1].station_name}}</text>
              <text class="td">{{company_list[1].cs_sale_count}}/{{company_list[1].cs_check_count}}/{{company_list[1].cs_new_company_count}}/{{company_list[1].cs_company_count}}</text>
              <text class="td">{{examine_list[1].cs_examine_count}}/{{examine_list[1].cs_overseer_count}}/{{examine_list[1].cs_company_count}}</text>
              <text class="td">{{ssmz_list[1].ry_incr_count}}/{{ssmz_list[1].zf_count}}/{{ssmz_list[1].ssmz_count}}</text>
            </view>
            <view class="tr"><text class="td"></text>
              <block wx:for="{{3}}" wx:for-item="item" wx:for-index="__i2__" wx:key="__i2__"><text class="td" style="border-bottom:1rpx solid #eee;"></text></block>
            </view>
            <view class="tr">
              <text class="td">{{ssmz_list[2].station_name}}</text>
              <text class="td">{{company_list[2].jb_sale_count}}/{{company_list[2].jb_check_count}}/{{company_list[2].jb_new_company_count}}/{{company_list[2].jb_company_count}}</text>
              <text class="td">{{examine_list[2].jb_examine_count}}/{{examine_list[2].jb_overseer_count}}/{{examine_list[2].jb_company_count}}</text>
              <text class="td">{{ssmz_list[2].ry_incr_count}}/{{ssmz_list[2].zf_count}}/{{ssmz_list[2].ssmz_count}}</text>
            </view>
            <view class="tr"><text class="td"></text>
              <block wx:for="{{3}}" wx:for-item="item" wx:for-index="__i3__" wx:key="__i3__"><text class="td" style="border-bottom:1rpx solid #eee;"></text></block>
            </view>
            <view class="tr">
              <text class="td">{{ssmz_list[3].station_name}}</text>
              <text class="td">{{company_list[3].th_sale_count}}/{{company_list[3].th_check_count}}/{{company_list[3].th_new_company_count}}/{{company_list[3].th_company_count}}</text>
              <text class="td">{{examine_list[3].th_examine_count}}/{{examine_list[3].th_overseer_count}}/{{examine_list[3].th_company_count}}</text>
              <text class="td">{{ssmz_list[3].ry_incr_count}}/{{ssmz_list[3].zf_count}}/{{ssmz_list[3].ssmz_count}}</text>
            </view>
            <view class="tr"><text class="td"></text>
              <block wx:for="{{3}}" wx:for-item="item" wx:for-index="__i4__" wx:key="__i4__"><text class="td" style="border-bottom:1rpx solid #eee;"></text></block>
            </view>
            <view class="tr">
              <text class="td">{{ssmz_list[4].station_name}}</text>
              <text class="td">{{company_list[4].cx_sale_count}}/{{company_list[4].cx_check_count}}/{{company_list[4].cx_new_company_count}}/{{company_list[4].cx_company_count}}</text>
              <text class="td">{{examine_list[4].cx_examine_count}}/{{examine_list[4].cx_overseer_count}}/{{examine_list[4].cx_company_count}}</text>
              <text class="td">{{ssmz_list[4].ry_incr_count}}/{{ssmz_list[4].zf_count}}/{{ssmz_list[4].ssmz_count}}</text>
            </view>
            <view class="tr"><text class="td"></text>
              <block wx:for="{{3}}" wx:for-item="item" wx:for-index="__i5__" wx:key="__i5__"><text class="td" style="border-bottom:1rpx solid #eee;"></text></block>
            </view>
          </view>
        </view>
      </view>
      <view hidden="{{!show1}}">
        <view class="main">
          <view class="item" bindtap="toPageworksp" data-bgcolor="#ffffff" data-fontcolor="#000000" data-title="嘉兴公安自主申报" data-url="https://zzsbui.jxjkga.cn/#/wdqytdgl/zjbstdgl?team_name=网约房团队&team_id=28&company_id=1&name=经开公安分局" data-type="h5">
            <image src="https://zzsbapi.jxjkga.cn/wxzzsb/new/wyftdgl.png" mode="widthFix"></image>
            <view class="text">
              <view class="title">网约房团队</view>
              <view class="wenzi">点击管理团队</view>
            </view>
          </view>
          <view class="item" bindtap="toPageworksp" data-bgcolor="#ffffff" data-fontcolor="#000000" data-title="嘉兴公安自主申报" data-url="https://zzsbui.jxjkga.cn/#/wdqytdgl/zjbstdgl?team_name=中介报送团队&team_id=28&company_id=1&name=经开公安分局" data-type="h5">
            <image src="https://zzsbapi.jxjkga.cn/wxzzsb/new/tdgl.png" mode="widthFix"></image>
            <view class="text">
              <view class="title">中介报送团队</view>
              <view class="wenzi">点击管理团队</view>
            </view>
          </view>
        </view>
        <view class="main">
          <view class="item" bindtap="toPageworksp" data-bgcolor="#ffffff" data-fontcolor="#000000" data-title="嘉兴公安自主申报" data-url="https://zzsbui.jxjkga.cn/#/wdqytdgl/zjbstdgl?team_name=led屏团队&team_id=28&company_id=1&name=经开公安分局" data-type="h5">
            <image src="https://zzsbapi.jxjkga.cn/wxzzsb/new/ledtdgl.png" mode="widthFix"></image>
            <view class="text">
              <view class="title">LED屏管理</view>
              <view class="wenzi">点击管理团队</view>
            </view>
          </view>
          <view class="item">
            <image src="https://zzsbapi.jxjkga.cn/wxzzsb/new/wodetuandui1.png" mode="widthFix"></image>
            <view class="text">
              <view class="title">我的团队</view>
              <view class="wenzi">管理我的团队</view>
            </view>
          </view>
        </view>
        <view class="line"></view>
        <view class="functionBox">
          <block wx:for="{{teamgl}}" wx:for-item="item" wx:for-index="index" wx:key="index">
            <block>
              <image src="{{item.url}}" mode="aspectFit"></image>
              <view>{{''+item.text+">"}}</view>
            </block>
          </block>
        </view>
        <view class="bottomContent">
          <!-- <view class="title">{{''+options[1].text+''}}</view> -->
          <view class="title">团队管理</view>
          <view class="table">
            <view class="tr"><text class="th" style="padding:0 40rpx;"></text><text class="th">2000家</text><text class="th">50000人</text><text class="th">50件</text></view>
            <view class="tr"><text class="th"></text><text class="th">企业单位</text><text class="th">申报总人数</text><text class="th">待清查工作</text></view>
            <view class="tr"><text class="td"></text>
              <block wx:for="{{3}}" wx:for-item="item" wx:for-index="__i6__" wx:key="__i6__"><text class="td" style="border-bottom:1rpx solid #eee;"></text></block>
            </view>
            <view class="tr"><text class="td"></text><text class="td">企业人数/单位总数</text><text class="td">人口总数/申报房屋数</text><text class="td">待清查工作</text></view>
            <view class="tr"><text class="td">嘉北</text><text class="td">100/200</text><text class="td">3000/1030</text><text class="td">10</text></view>
            <view class="tr"><text class="td"></text>
              <block wx:for="{{3}}" wx:for-item="item" wx:for-index="__i7__" wx:key="__i7__"><text class="td" style="border-bottom:1rpx solid #eee;"></text></block>
            </view>
            <view class="tr"><text class="td">塘汇</text><text class="td">100/200</text><text class="td">3000/1030</text><text class="td">10</text></view>
            <view class="tr"><text class="td"></text>
              <block wx:for="{{3}}" wx:for-item="item" wx:for-index="__i8__" wx:key="__i8__"><text class="td" style="border-bottom:1rpx solid #eee;"></text></block>
            </view>
            <view class="tr"><text class="td">城南</text><text class="td">100/200</text><text class="td">3000/1030</text><text class="td">10</text></view>
            <view class="tr"><text class="td"></text>
              <block wx:for="{{3}}" wx:for-item="item" wx:for-index="__i9__" wx:key="__i9__"><text class="td" style="border-bottom:1rpx solid #eee;"></text></block>
            </view>
            <view class="tr"><text class="td">长水</text><text class="td">100/200</text><text class="td">3000/1030</text><text class="td">10</text></view>
            <view class="tr"><text class="td"></text>
              <block wx:for="{{3}}" wx:for-item="item" wx:for-index="__i10__" wx:key="__i10__"><text class="td" style="border-bottom:1rpx solid #eee;"></text></block>
            </view>
            <view class="tr"><text class="td">城西</text><text class="td">100/200</text><text class="td">3000/1030</text><text class="td">10</text></view>
            <view class="tr"><text class="td"></text>
              <block wx:for="{{3}}" wx:for-item="item" wx:for-index="__i11__" wx:key="__i11__"><text class="td" style="border-bottom:1rpx solid #eee;"></text></block>
            </view>
          </view>
        </view>
      </view>
      <view hidden="{{!show2}}">
        <view class="main">
          <view class="item">
            <image src="https://zzsbapi.jxjkga.cn/wxzzsb/new/WyfQiye.png" mode="widthFix"></image>
            <view class="text">
              <view class="title">网约房企业</view>
              <view class="wenzi">查看辖区网约房</view>
            </view>
          </view>
          <view class="item">
            <image src="https://zzsbapi.jxjkga.cn/wxzzsb/new/xzry.png" mode="widthFix"></image>
            <view class="text">
              <view class="title">登记人员</view>
              <view class="wenzi">查看网约房住户</view>
            </view>
          </view>
        </view>
        <view class="main">
          <view class="item">
            <image src="https://zzsbapi.jxjkga.cn/wxzzsb/new/FwLeixing.png" mode="widthFix"></image>
            <view class="text">
              <view class="title">房屋类型</view>
              <view class="wenzi">查看短租/长租房屋</view>
            </view>
          </view>
          <view class="item">
            <image src="https://zzsbapi.jxjkga.cn/wxzzsb/new/dqc.png" mode="widthFix"></image>
            <view class="text alarm">
              <view class="title">待清查房屋</view>
              <view class="divright divright3">10</view>
              <view class="wenzi">长期未报送网约房</view>
            </view>
          </view>
        </view>
        <view class="line"></view>
        <view class="functionBox">
          <block wx:for="{{wyfgl}}" wx:for-item="item" wx:for-index="index" wx:key="index">
            <block>
              <image src="{{item.url}}" mode="aspectFit"></image>
              <view>{{''+item.text+">"}}</view>
            </block>
          </block>
        </view>
        <view class="bottomContent">
          <view class="title">网约房管理</view>
          <view class="table">
            <view class="tr"><text class="th" style="padding:0 40rpx;"></text><text class="th">200家</text><text class="th">5000人</text><text class="th">500家</text></view>
            <view class="tr"><text class="th"></text><text class="th">网约房企业</text><text class="th">当前在住人数</text><text class="th">长期未报送企业</text></view>
            <view class="tr"><text class="td"></text>
              <block wx:for="{{3}}" wx:for-item="item" wx:for-index="__i12__" wx:key="__i12__"><text class="td" style="border-bottom:1rpx solid #eee;"></text></block>
            </view>
            <view class="tr"><text class="td"></text><text class="td">工作人员/单位总数</text><text class="td">当前新增/月新增/人员总数</text><text class="td">7天/30天/无报送企业</text></view>
            <view class="tr"><text class="td">嘉北</text><text class="td">10/200</text><text class="td">300/1200/1500</text><text class="td">10/100</text></view>
            <view class="tr"><text class="td"></text>
              <block wx:for="{{3}}" wx:for-item="item" wx:for-index="__i13__" wx:key="__i13__"><text class="td" style="border-bottom:1rpx solid #eee;"></text></block>
            </view>
            <view class="tr"><text class="td">塘汇</text><text class="td">10/200</text><text class="td">300/1200/1500</text><text class="td">10/100</text></view>
            <view class="tr"><text class="td"></text>
              <block wx:for="{{3}}" wx:for-item="item" wx:for-index="__i14__" wx:key="__i14__"><text class="td" style="border-bottom:1rpx solid #eee;"></text></block>
            </view>
            <view class="tr"><text class="td">城南</text><text class="td">10/200</text><text class="td">300/1200/1500</text><text class="td">10/100</text></view>
            <view class="tr"><text class="td"></text>
              <block wx:for="{{3}}" wx:for-item="item" wx:for-index="__i15__" wx:key="__i15__"><text class="td" style="border-bottom:1rpx solid #eee;"></text></block>
            </view>
            <view class="tr"><text class="td">长水</text><text class="td">10/200</text><text class="td">300/1200/1500</text><text class="td">10/100</text></view>
            <view class="tr"><text class="td"></text>
              <block wx:for="{{3}}" wx:for-item="item" wx:for-index="__i16__" wx:key="__i16__"><text class="td" style="border-bottom:1rpx solid #eee;"></text></block>
            </view>
            <view class="tr"><text class="td">城西</text><text class="td">10/200</text><text class="td">300/1200/1500</text><text class="td">10/100</text></view>
            <view class="tr"><text class="td"></text>
              <block wx:for="{{3}}" wx:for-item="item" wx:for-index="__i17__" wx:key="__i17__"><text class="td" style="border-bottom:1rpx solid #eee;"></text></block>
            </view>
          </view>
        </view>
      </view>
      <view hidden="{{!show3}}">
        <!-- {{show3}} -->
        <view class="functionguanli">功能管理</view>
        <block wx:for="{{newOptions}}" wx:for-item="item" wx:for-index="index" wx:key="index">
          <view class="function1">
            <view>{{item.text}}</view>
            <view>
              <image src="https://zzsbapi.jxjkga.cn/wxzzsb/new/ZhiDing.png" mode="aspectFit" bindtap="toUp" data-index="{{index}}" />
              <image src="https://zzsbapi.jxjkga.cn/wxzzsb/new/XuanZhong.png" mode="aspectFit" />
            </view>
          </view>
          <view class="underlines"></view>
        </block>

        <view class="submit" bindtap="submitFun">
          <button>提交</button>
        </view>
      </view>
    </view>
  </view>
</view>