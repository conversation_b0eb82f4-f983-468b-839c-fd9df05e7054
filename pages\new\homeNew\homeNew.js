// pages/mine/mine.js
const http = require("../utils/httpUtils.js");
var user = require('../../../pages/new/utils/user.js')
var app = getApp();
var url = app.globalData.requestUrl;
var columnsArr = app.globalData.columnsArr;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    police_status: 0,
    wenbo_rent_check: 0,
    chengnan_user_register: 0,
    sbhc_status: 0,
    wgyr_status: 0,
    xfrw_status: 0,
    wycj_status: 0,
    is_fkgzz_state: 0,
    op_status: 0,
    mdtj_status: 0,
    jxyjb_status: 0,
    userInfo: null,
    imgUrls: [
      '/images/new-banner.png'
    ],
    indicatorDots: true,
    vertical: false,
    showPrivacy: false,
    autoplay: true,
    circular: true,
    interval: 5000,
    duration: 1000,
    previousMargin: 0,
    nextMargin: 0,
    SwiperHeight: '',
    toDoNum: 0,
    toDoNumwdqy: 0,
    winWid: '',
    noticeList: [],
    imgs: [],
    listimg: [{
        id: 1,
        url: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/ldrkdjnew.png'
      },
      {
        id: 2,
        // url: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/zxfznew.png'
        url: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/zaixianfanzhaButton.png'
      },
      {
        id: 3,
        url: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/jingxingyijianbanButton.png'
      },
    ],
    topImgs: [],
    modalHidden: true,
    search_param: '',
    buttonTop: 0,
    buttonLeft: 0,
    buttonRight: 0,
    windowHeight: '',
    windowWidth: '',
    startPoint: '',
    endPoint: '',
    ldWorkToken: '',
    ldrkToken: '',
    isForeign: '',
    countJxyjb: 0
  },
  gotoXjm: function (e) {
    wx.navigateTo({
      url: '../xjm/xjm'
    })
  },
  getLdrkToken() {
    let self = this;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/base/getLdrkToken',
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          let ldrkToken = res.data.data
          // console.log(ldWorkToken)
          self.setData({
            ldrkToken: ldrkToken
          })
        } else {

        }
      },
      fail(res) {
        console.log(res)
      }
    })

  },
  getLdWorkToken() {
    let self = this;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/base/getLdWorkToken',
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          let ldWorkToken = res.data.data
          // console.log(ldWorkToken)
          self.setData({
            ldWorkToken: ldWorkToken
          })
        } else {

        }
      },
      fail(res) {
        console.log(res)
      }
    })

  },
  gotoycqz() {
    let self = this;
    const ldWorkToken = self.data.userInfo.ldWorkToken
    if (self.data.userInfo.isLogin) {
      if (self.data.userInfo.isAuth) {
        wx.navigateToMiniProgram({
          appId: 'wxfb6bb5cdaaed786d',
          path: '/pages/index/index?ldWorkToken=' + ldWorkToken,
          extraData: {
            foo: 'bar'
          },
          envVersion: 'release',
          success(res) {
            // 打开成功
          }
        })
      } else {
        // self.doAuth();
        wx.navigateTo({
          url: '/pages/new/userCheck/userCheck'
        })
      }
    } else {
      wx.showModal({
        title: '提示',
        content: '请先注册并登录',
        success(res) {
          if (res.confirm) {
            wx.redirectTo({
              url: '/pages/new/user/login'
            })
          } else if (res.cancel) {
            wx.showToast({
              title: '登录失败',
              icon: 'none',
              duration: 2000
            })
          }
        }
      })
    }

  },
  gotofsacw: function (e) {
    wx.navigateTo({
      url: '../fsacw/fsacw'
    })
  },
  tomsyjb: function (e) {
    wx.navigateTo({
      url: '../msyjb/msyjb'
    })
  },
  gotowhp: function (e) {
    wx.navigateTo({
      url: '../whp/whp'
    })
  },
  gotojgj: function (e) {
    wx.navigateTo({
      url: '../jgj/jgj'
    })
  },
  gotocszz: function (e) {
    wx.navigateTo({
      url: '../cszz/cszz'
    })
  },
  toPagexl: function (e) {
    wx.navigateTo({
      url: '../xlsb/xlsb'
    })
  },
  gotoXinxi: function (e) {
    wx.navigateTo({
      url: '../xxfz/xxfz'
    })
  },

  //跳转到police页面
  gotoPolice: function (e) {
    wx.navigateTo({
      url: '../../policeTerminal/policeTerminsl',
    })
  },


  //跳转到无疫创建
  gotowycj1: function (e) {
    let self = this;

    if (!e.currentTarget.dataset.url) {
      return
    }
    let fkgzz_status = self.data.fkgzz_status;
    let url = e.currentTarget.dataset.type == 'h5' ? '../../myWebview/myWebview?h5=' + e.currentTarget.dataset.url + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor;
    console.log(url)
    if (e.currentTarget.dataset.type == 'alert') {
      wx.showToast({
        title: e.currentTarget.dataset.url,
        icon: 'none',
        duration: 2000
      })
    } else {
      if (self.data.userInfo.isLogin) {
        if (self.data.userInfo.isAuth) {

          if (fkgzz_status == 1) {
            wx.navigateTo({
              url: url,
            })
          } else {
            wx.showToast({
              title: '您没有操作权限！',
              icon: 'none',
              duration: 2000
            })
          }

        } else {
          wx.navigateTo({
            url: '/pages/new/userCheck/userCheck'
          })
        }
      } else {
        wx.showModal({
          title: '提示',
          content: '请先注册并登录',
          success(res) {
            if (res.confirm) {
              wx.redirectTo({
                url: '/pages/new/user/login'
              })
            } else if (res.cancel) {
              wx.showToast({
                title: '登录失败',
                icon: 'none',
                duration: 2000
              })
            }
          }
        })
      }
    }
    // if (this.data.wycj_status == 1) {
    //   this.toPage(e);
    // } else {
    //   wx.showToast({
    //     title: '您没有操作权限！',
    //     icon: 'none',
    //     duration: 2000
    //   })
    // }
  },
  toPage3: function (e) {
    let url = e.currentTarget.dataset.type == 'h5' ? '../../myWebview/myWebview?h5=' + e.currentTarget.dataset.url + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor;
    wx.navigateTo({
      url: url,
    })
  },
  toPage4: function (e) {
    var search_param = this.data.search_param;
    if (search_param.trim().length < 2) {
      wx.showToast({
        title: '请至少输入两个字！',
        icon: 'none',
        duration: 2000
      })
      return
    }
    let url = e.currentTarget.dataset.type == 'h5' ? '../../myWebview/myWebview?h5=' + encodeURIComponent(e.currentTarget.dataset.url + '?search_param=' + search_param.trim()) + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor;
    wx.navigateTo({
      url: url,
    })
  },
  toPage6: function (e) {
    let url = e.currentTarget.dataset.type == 'h5' ? '../../myWebview/myWebview?h5=' + encodeURIComponent(e.currentTarget.dataset.url + '?openpz=1') + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor;
    wx.navigateTo({
      url: url,
    })
  },
  getInputValue(e) {
    this.setData({
      search_param: e.detail.value
    })
  },
  /**
   * 点击取消
   */
  modalCandel: function () {
    // do something
    this.setData({
      modalHidden: true
    })
  },

  /**
   *  点击确认
   */
  modalConfirm: function () {
    // do something
    this.setData({
      modalHidden: true
    })
  },
  onPageScroll: function (e) {
    // console.log(e.scrollTop) //这个就是滚动到的位置,可以用这个位置来写判断
  },
  onClick2wm(e) {
    let self = this;
    if (self.data.userInfo.isLogin) {
      if (self.data.userInfo.isAuth) {
        self.get2wm();
      } else {
        self.doAuth();
      }
    } else {
      wx.showModal({
        title: '提示',
        content: '请先注册并登录',
        success(res) {
          if (res.confirm) {
            self.get2wm();
          } else if (res.cancel) {
            wx.showToast({
              title: '登录失败',
              icon: 'none',
              duration: 2000
            })
          }
        }
      })
    }
  },
  get2wm() {
    let self = this;
    http.get('base/user/qrcode', {}, res => {
      if (res.data.success == 1) {
        self.setData({
          modalHidden: false,
          ewmUrl: 'https://zzsbapi.jxjkga.cn' + res.data.data
        })
      }
    })
  },

  //跳转到抽奖界面

  goPrize: function () {
    let self = this;

    if (self.data.userInfo.isLogin) {
      if (self.data.userInfo.isAuth) {
        wx.navigateTo({
          url: '../prize/prize',
        })
      } else {
        self.doAuth();
      }
    } else {
      wx.showModal({
        title: '提示',
        content: '请先注册并登录',
        success(res) {
          if (res.confirm) {
            wx.redirectTo({
              url: '/pages/new/user/login'
            })
          } else if (res.cancel) {
            wx.showToast({
              title: '登录失败',
              icon: 'none',
              duration: 2000
            })
          }
        }
      })
    }
    // if(wx.getStorageSync('lottery') == '0'){

    // }else{
    //   wx.navigateTo({
    //     url: self.data.ll_type == '1' ? '../huaShu/huaShu?code=' + self.data.ll_code : '../getMoney/getMoney?code=' + self.data.ll_code,
    //   })
    // }  
  },
  changeProperty: function (e) {
    var propertyName = e.currentTarget.dataset.propertyName
    var newData = {}
    newData[propertyName] = e.detail.value
    this.setData(newData)
  },
  changeIndicatorDots: function (e) {
    this.setData({
      indicatorDots: !this.data.indicatorDots
    })
  },
  changeAutoplay: function (e) {
    this.setData({
      autoplay: !this.data.autoplay
    })
  },
  intervalChange: function (e) {
    this.setData({
      interval: e.detail.value
    })
  },
  durationChange: function (e) {
    this.setData({
      duration: e.detail.value
    })
  },
  todoList: function (e) {
    wx.navigateTo({
      url: '/pages/new/todoList/todoList',
    })
  },
  bindSwiperHeight: function (e) {
    var that = this;
    var winWid = wx.getSystemInfoSync().windowWidth; //获取当前屏幕的宽度
    var imgh = e.detail.height; //图片高度
    var imgw = e.detail.width;
    var swiperH = winWid * imgh / imgw + "px"
    //等比设置swiper的高度。  即 屏幕宽度 / swiper高度 = 图片宽度 / 图片高度    ==》swiper高度 = 屏幕宽度 * 图片高度 / 图片宽度
    that.setData({
      winWid: winWid,
      SwiperHeight: swiperH
    })
  },
  toMiniProgram() {
    wx.navigateToMiniProgram({
      appId: 'wx182be45f90ffbf3d',
      path: '/pages/webwiew/index',
      extraData: {
        foo: 'bar'
      },
      envVersion: 'release',
      success(res) {
        // 打开成功
      }
    })
  },
  gotowycj: function (e) {
    if (this.data.fkgzz_status == 1) {
      this.toPage7(e);
    } else {
      wx.showToast({
        title: '您没有操作权限！',
        icon: 'none',
        duration: 2000
      })
    }
  },
  toPageJiaShequ: function (e) {
    if (!this.data.userInfo.is_jsq_admin) {
      wx.showToast({
        title: '您没有操作权限！',
        icon: 'none',
        duration: 2000
      })

    } else {
      this.toPage(e);
    }
  },
  toPage2: function (e) {
    if (this.data.wgyr_status == 1) {
      this.toPage(e);
    } else {
      wx.showToast({
        title: '您没有操作权限，请联系负责的外国人联络员！',
        icon: 'none',
        duration: 2000
      })
    }
  },
  imgtoPage: function (e) {
    console.log(e);
    if (e.currentTarget.dataset.url == '2') {
      wx.navigateTo({
        url: `../../myWebview/myWebview?h5=https://zzsbui.jxjkga.cn/#/jxyjb/index&bgcolor=#ffffff&fontcolor=#000000&title=警馨一键办`
      })
      return
    }
    let self = this;
    if (self.data.userInfo.isLogin) {
      if (e.currentTarget.dataset.url == '0') {
        console.log(e);
        wx.setStorageSync('paramArray', '');
        wx.navigateTo({
          url: '/pages/new/lkzzbs/lkzzbs?' + `&utoken=${wx.getStorageSync('token')}`,
        })
      }
      if (e.currentTarget.dataset.url == '1') {
        console.log(e);
        this.gotoXinxi()
      }
      if (e.currentTarget.dataset.url == '2') {
        wx.navigateTo({
          url: 'https://zzsbui.jxjkga.cn/#/jxyjb/index?' + `&utoken=${wx.getStorageSync('token')}`,
        })
      }
    } else {
      wx.showModal({
        title: '提示',
        content: '请先注册并登录',
        success(res) {
          if (res.confirm) {
            wx.redirectTo({
              url: '/pages/new/user/login'
            })
          } else if (res.cancel) {
            wx.showToast({
              title: '登录失败',
              icon: 'none',
              duration: 2000
            })
          }
        }
      })
    }

  },
  /**跳转页面 */
  toPage: function (e) {
    let self = this;
    if (!e.currentTarget.dataset.url) {
      return
    }
    let url = e.currentTarget.dataset.type == 'h5' ? '../../myWebview/myWebview?h5=' + e.currentTarget.dataset.url + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor;
    console.log(url)
    if (e.currentTarget.dataset.type == 'alert') {
      wx.showToast({
        title: e.currentTarget.dataset.url,
        icon: 'none',
        duration: 2000
      })
    } else {
      if (self.data.userInfo.isLogin) {
        if (self.data.userInfo.isAuth) {
          console.log('------', url);
          wx.navigateTo({
            url: url,
          })
        } else {
          wx.navigateTo({
            url: '/pages/new/userCheck/userCheck'
          })
        }
      } else {
        wx.showModal({
          title: '提示',
          content: '请先注册并登录',
          success(res) {
            if (res.confirm) {
              wx.redirectTo({
                url: '/pages/new/user/login'
              })
            } else if (res.cancel) {
              wx.showToast({
                title: '登录失败',
                icon: 'none',
                duration: 2000
              })
            }
          }
        })
      }
    }
  },
  toPage5: function (e) {
    let self = this;
    if (!e.currentTarget.dataset.url) {
      return
    }
    let url = e.currentTarget.dataset.type == 'h5' ? '../../myWebview/myWebview?h5=' + e.currentTarget.dataset.url + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor;
    console.log(url)
    if (e.currentTarget.dataset.type == 'alert') {
      wx.showToast({
        title: e.currentTarget.dataset.url,
        icon: 'none',
        duration: 2000
      })
    } else {

      if (self.data.userInfo.isLogin) {
        if (self.data.userInfo.isAuth) {
          wx.navigateTo({
            url: url,
          })
        } else {
          wx.navigateTo({
            url: '/pages/new/jwCheck/jwCheck'
          })
        }
      } else {
        wx.showModal({
          title: '提示',
          content: '请先注册并登录',
          success(res) {
            if (res.confirm) {
              wx.redirectTo({
                url: '/pages/new/user/login'
              })
            } else if (res.cancel) {
              wx.showToast({
                title: '登录失败',
                icon: 'none',
                duration: 2000
              })
            }
          }
        })
      }
    }
  },
  toPage7: function (e) {
    let self = this;

    if (!e.currentTarget.dataset.url) {
      return
    }
    let url = e.currentTarget.dataset.type == 'h5' ? '../../myWebview/myWebview?h5=' + e.currentTarget.dataset.url + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&company_id=' + 1;
    console.log(url)
    if (e.currentTarget.dataset.type == 'alert') {
      wx.showToast({
        title: e.currentTarget.dataset.url,
        icon: 'none',
        duration: 2000
      })
    } else {
      if (self.data.userInfo.isLogin) {
        if (self.data.userInfo.isAuth) {
          wx.navigateTo({
            url: url,
          })
        } else {
          wx.navigateTo({
            url: '/pages/new/userCheck/userCheck'
          })
        }
      } else {
        wx.showModal({
          title: '提示',
          content: '请先注册并登录',
          success(res) {
            if (res.confirm) {
              wx.redirectTo({
                url: '/pages/new/user/login'
              })
            } else if (res.cancel) {
              wx.showToast({
                title: '登录失败',
                icon: 'none',
                duration: 2000
              })
            }
          }
        })
      }
    }
  },
  /**验证是否人证核实 */
  doAuth: function () {
    user.doAuth();
  },
  doLogin: function () {
    var that = this;
    user.getUserInfo().then((res) => {
      that.setData({
        userInfo: getApp().globalData.userInfo
      });
      if (user.checkPermissionByFuncName("house-check")) {
        that.setData({
          op_status: 1
        });
      } else {
        that.setData({
          op_status: 0
        });
      }
      if (user.checkPermissionByFuncName("foreigner-manage")) {
        that.setData({
          wgyr_status: 1
        });
      } else {
        that.setData({
          wgyr_status: 0
        });
      }
      if (user.checkPermissionByFuncName("person-check")) {
        that.setData({
          sbhc_status: 1
        });
      } else {
        that.setData({
          sbhc_status: 0
        });
      }

      if (user.checkPermissionByFuncName("police-check")) {
        that.setData({
          police_status: 1
        });
      } else {
        that.setData({
          police_status: 0
        });
      }

      if (user.checkPermissionByFuncName("epidemic-free")) {
        that.setData({
          wycj_status: 1
        });
      } else {
        that.setData({
          wycj_status: 0
        });
      }
      if (user.get_is_fkgzz_state()) {
        that.setData({
          fkgzz_status: 1
        });
      } else {
        that.setData({
          fkgzz_status: 0
        });
      }
      if (user.checkPermissionByFuncName("publish-building-task")) {
        that.setData({
          xfrw_status: 1
        });
      } else {
        that.setData({
          xfrw_status: 0
        });
      }

    });
    console.log(this.data);
  },
  bindHouseSelectRole: function () {
    var that = this;
    wx.showToast({
      title: '请选择角色',
      icon: 'none',
      duration: 2000
    })
    wx.showActionSheet({
      itemList: ['我是房东', '我是租客', '自住'],
      success(res) {
        if (that.data.userInfo.isLogin) {
          if (that.data.userInfo.isAuth) {
            if (res.tapIndex == 0) {
              wx.navigateTo({
                url: '/pages/new/house/declareFangDong/declareFangDong/declareFangDong?type=1',
              })
            } else if (res.tapIndex == 2) {
              wx.navigateTo({
                url: '/pages/new/house/declareFangDong/declareFangDong/declareFangDong?type=2',
              })
            } else {
              wx.navigateTo({
                url: '/pages/new/house/declareZuKe/searchHouse/searchHouse',
              })
            }
          } else {
            that.doAuth();
          }
        } else {
          that.doLogin();
        }
      },
      fail(res) {
        console.log(res.errMsg)
      }
    })
  },


  getnotice: function () {
    if (wx.getStorageSync('token')) {
      http.get('house/notice/list', {
        page_num: 1,
        page_size: 2,
        token: wx.getStorageSync('token')
      }, res => {
        if (res.data.success == 1) {
          let mynoticeList = res.data.data.data_list;
          this.setData({
            noticeList: mynoticeList
          });
        }
      })
    }
  },
  getimgs: function () {
    http.get('base/getAdvertisePicInfo', {
      type: "小程序首页mid图"
    }, res => {
      if (res.data.success == 1) {
        let myImgs = res.data.data;
        this.setData({
          imgs: myImgs,
        });
      }
    });

    http.get('base/getAdvertisePicInfo', {
      type: "小程序首页top图"
    }, res => {
      if (res.data.success == 1) {
        let myImgs2 = res.data.data;
        // myImgs2[0].url = myImgs2[0].url+ '?m='+Math.random(); 
        this.setData({
          topImgs: myImgs2
        });
        wx.setNavigationBarColor({
          frontColor: '#ffffff', // 必写项
          backgroundColor: myImgs2[0].colour, // 传递的颜色值 
        })
      }
    })
  },
  getDbCount: function () {
    var that = this;
    if (wx.getStorageSync('token')) {
      http.get('company/getHouseCompanyTodoNum', {}, res => {
        if (res.data.success == 1) {
          if (res.data.data > 0) {

            that.setData({
              toDoNum: res.data.data
            });
          } else {

            that.setData({
              toDoNum: 0
            });
          }
        } else {
          wx.removeTabBarBadge({
            index: 1, //tabBar下标（底部tabBar的位置，从0开始）
          });
          that.setData({
            toDoNum: 0
          });
        }
      })
    }
  },

  getDbCountWdyq: function () {
    var that = this;
    if (wx.getStorageSync('token')) {
      http.get('company/getMyCompanyExamineTodoNum', {
        token: wx.getStorageSync('token')
      }, res => {
        if (res.data.success == 1) {
          if (res.data.data.todo_num > 0) {
            that.setData({
              toDoNumwdqy: res.data.data.todo_num
            });
            console.log(res.data, '**************');
          } else {
            that.setData({
              toDoNumwdqy: 0
            });
          }
        } else {
          wx.removeTabBarBadge({
            index: 1, //tabBar下标（底部tabBar的位置，从0开始）
          });
          that.setData({
            toDoNumwdqy: 0
          });
        }
      })
    }
  },
  buttonStart: function (e) {
    var startPoint = e.touches[0] //获取拖动开始点
    this.setData({
      startPoint: startPoint
    })
  },
  buttonMove: function (e) {
    var startPoint = this.data.startPoint;
    var endPoint = e.touches[e.touches.length - 1] //获取拖动结束点
    //计算在X轴上拖动的距离和在Y轴上拖动的距离
    var translateX = endPoint.clientX - startPoint.clientX
    var translateY = endPoint.clientY - startPoint.clientY

    startPoint = endPoint //重置开始位置
    var buttonTop = this.data.buttonTop + translateY
    var buttonLeft = this.data.buttonLeft + translateX
    //判断是移动否超出屏幕
    if (buttonLeft + 50 >= this.data.windowWidth) {
      buttonLeft = this.data.windowWidth - 50;
    }
    if (buttonLeft <= 0) {
      buttonLeft = 0;
    }
    if (buttonTop <= 0) {
      buttonTop = 0
    }
    if (buttonTop + 50 >= this.data.windowHeight) {
      buttonTop = this.data.windowHeight - 50;
    }
    this.setData({
      buttonTop: buttonTop,
      buttonLeft: buttonLeft
    })
  },
  buttonEnd: function (e) {},
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    that.getLdWorkToken()
    that.getLdrkToken()
    user.getUserInfo(false).then((res) => {
      this.setData({
        userInfo: getApp().globalData.userInfo
      });
      wx.request({
        method: "POST",
        url: url + "/storemgmt/base/getMyHomePage",
        header: {
          token: wx.getStorageSync('token')
        },
        success: (res) => {
          if (res.data.success == 1 && res.data.data && res.data.data.url) {
            console.log(res.data.data.url)
            wx.navigateTo({
              url: "../../myWebview/myWebview?h5=" + res.data.data.url + `&utoken=${wx.getStorageSync('token')}`
            })
          }

        }
      })

      if (user.checkPermissionByFuncName("house-check")) {
        that.setData({
          op_status: 1
        });
      } else {
        that.setData({
          op_status: 0
        });
      }
      if (user.checkPermissionByFuncName("wenbo-rent-check")) {
        that.setData({
          wenbo_rent_check: 1
        });
      } else {
        that.setData({
          wenbo_rent_check: 0
        });
      }
      if (user.checkPermissionByFuncName("chengnan-user-register")) {
        that.setData({
          chengnan_user_register: 1
        });
      } else {
        that.setData({
          chengnan_user_register: 0
        });
      }
      if (user.checkPermissionByFuncName("foreigner-manage")) {
        that.setData({
          wgyr_status: 1
        });
      } else {
        that.setData({
          wgyr_status: 0
        });
      }
      if (user.checkPermissionByFuncName("publish-building-task")) {
        that.setData({
          xfrw_status: 1
        });
      } else {
        that.setData({
          xfrw_status: 0
        });
      }

      if (user.checkPermissionByFuncName("person-check")) {
        that.setData({
          sbhc_status: 1
        });
      } else {
        that.setData({
          sbhc_status: 0
        });
      }

      if (user.checkPermissionByFuncName("epidemic-free")) {
        that.setData({
          wycj_status: 1
        });
      } else {
        that.setData({
          wycj_status: 0
        });
      }
      if (user.get_is_fkgzz_state()) {
        that.setData({
          fkgzz_status: 1
        });
      } else {
        that.setData({
          fkgzz_status: 0
        });
      }
      if (getApp().globalData.userInfo.is_jxyjb_admin) {
        that.setData({
          jxyjb_status: 1,
          listimg: [{
              id: 1,
              url: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/ldrkdjnew.png'
            },
            {
              id: 2,
              // url: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/zxfznew.png'
              url: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/zaixianfanzhaButton.png'
            },
            {
              id: 3,
              url: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/jingxingyijianbanButton.png'
            }
          ]
        });
        that.getCount()
      } else {
        that.setData({
          jxyjb_status: 0,
          listimg: [{
              id: 1,
              url: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/ldrkdjnew.png'
            },
            {
              id: 2,
              url: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/zxfznew.png'
            }
          ]
        });
      }

      if (user.checkPermissionByFuncName("police-check")) {
        that.setData({
          police_status: 1
        });
        // 跳转到新开发的页面
        // wx.navigateTo({
        //   url: '../../policeTerminal/policeTerminsl',
        // })
      } else {
        that.setData({
          police_status: 0
        });
      }

      if (user.checkPermissionByFuncName("conflict-mediation")) {
        that.setData({
          mdtj_status: 1
        });
        // 跳转到新开发的页面
        // wx.navigateTo({
        //   url: '../../policeTerminal/policeTerminsl',
        // })
      } else {
        that.setData({
          mdtj_status: 0
        });
      }
    });
    // 用户隐私信息认证代码
    // if (wx.getStorageSync("SixTip") == "") {
    //   this.alertSix();
    //   console.log("SixTip用戶沒有登錄");
    // }else{
    //   wx.redirectTo({
    //     url: '/pages/new/homeNew/homeNew'
    //   })
    //   console.log("SixTip用户已登录");
    // }
    wx.getPrivacySetting({
        success: res => {
          console.log(res) // 返回结果为: res = { needAuthorization: true/false, privacyContractName: '《xxx隐私保护指引》' }
          if (res.needAuthorization) {
            // 需要弹出隐私协议
            this.alertSix();
          }
        },
        fail: () => {},
        complete: () => {}
      }),

      // 获取购物车控件适配参数
      wx.getSystemInfo({
        success: function (res) {
          console.log(res);
          // 屏幕宽度、高度
          console.log('height=' + res.windowHeight);
          console.log('width=' + res.windowWidth);
          // 高度,宽度 单位为px
          that.setData({
            windowHeight: res.windowHeight,
            windowWidth: res.windowWidth,
            buttonTop: res.windowHeight * 0.8, //这里定义按钮的初始位置
            buttonLeft: res.windowWidth * 0.73, //这里定义按钮的初始位置
          })
        }
      })
  },
  // 警馨一键办数量
  getCount() {
    //我的待办数量
    let count1 = 0
    let count2 = 0
    http.get('jxyjb/getMyToDoListCount', {}, res => {
      let {
        data: {
          success,
          data
        }
      } = res
      if (success) {
        count1 = data
      }
    })

    http.get('jxyjb/getMyTaskCount', {}, res => {
      let {
        data: {
          success,
          data
        }
      } = res
      if (success) {
        count2 = data
      }
    })

    this.setData({
      countJxyjb: count1 + count2
    })
  },

  // 用户隐私信息认证代码
  alertSix() {
    console.log('没有获取到SixTip进行路由跳转');
    wx.redirectTo({
      url: '/pages/Bullet/index/index'
    })
  },
  handleAgreePrivacyAuthorization() {
    this.setData({
      showPrivacy: false
    })
    // 用户同意隐私协议事件回调
    // 用户点击了同意，之后所有已声明过的隐私接口和组件都可以调用了
    // wx.getUserProfile()
    // wx.chooseMedia()
    // wx.getClipboardData()
    // wx.startRecord()
  },
  handleOpenPrivacyContract() {
    // 打开隐私协议页面
    wx.openPrivacyContract({
      success: () => {}, // 打开成功
      fail: () => {}, // 打开失败
      complete: () => {}
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

    var that = this;
    this.getimgs();
    this.getDbCount();
    if (wx.getStorageSync('token')) {
      this.getDbCountWdyq();
      this.getnotice();
    }

    if (user.checkPermissionByFuncName("foreigner-manage")) {
      that.setData({
        wgyr_status: 1
      });
    } else {
      that.setData({
        wgyr_status: 0
      });
    }
    if (user.checkPermissionByFuncName("publish-building-task")) {
      that.setData({
        xfrw_status: 1
      });
    } else {
      that.setData({
        xfrw_status: 0
      });
    }
    if (user.checkPermissionByFuncName("police-check")) {
      that.setData({
        police_status: 1
      });
    } else {
      that.setData({
        police_status: 0
      });
    }
    if (user.checkPermissionByFuncName("epidemic-free")) {
      that.setData({
        wycj_status: 1
      });
    } else {
      that.setData({
        wycj_status: 0
      });
    }

    if (user.get_is_fkgzz_state()) {
      that.setData({
        fkgzz_status: 1
      });
    } else {
      that.setData({
        fkgzz_status: 0
      });
    }
    if (user.checkPermissionByFuncName("person-check")) {
      that.setData({
        sbhc_status: 1
      });
    } else {
      that.setData({
        sbhc_status: 0
      });
    }

    if (getApp().globalData.userInfo.is_jxyjb_admin) {
      that.setData({
        jxyjb_status: 1,
        listimg: [{
            id: 1,
            url: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/ldrkdjnew.png'
          },
          {
            id: 2,
            // url: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/zxfznew.png'
            url: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/zaixianfanzhaButton.png'
          },
          {
            id: 3,
            url: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/jingxingyijianbanButton.png'
          }
        ]
      })
      that.getCount()
    } else {
      that.setData({
        jxyjb_status: 0,
        listimg: [{
            id: 1,
            url: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/ldrkdjnew.png'
          },
          {
            id: 2,
            url: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/zxfznew.png'
          }
        ]
      })
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})