// pages/meetApply/meetApply.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    name: '',
    phone: '',
    date1: '',
    date2: '',
    reason: '',
  },
  bindKeyname:function(e){
    this.setData({
      name: e.detail.value
    })
  },
  bindKeyphone: function (e) {
    this.setData({
      phone: e.detail.value
    })
  },
  bindDateChange1: function (e) {
    this.setData({
      date1: e.detail.value
    })
  },
  bindDateChange2: function (e) {
    this.setData({
      date2: e.detail.value
    })
  },
  bindKeyreason: function (e) {
    this.setData({
      reason: e.detail.value
    })
  },
  apply:function(){
    let self = this;
    var app = getApp();
    var url = app.globalData.requestUrl;
    console.log(self.data.date1 + ' 00:00:00', self.data.date2 + ' 23:59:59')
    wx.request({
      method: 'POST',
      url: url + 'visit/applyConferenceVisit',
      data: {
        app_id: app.globalData.app_id,
        app_key: app.globalData.app_key,
        token: wx.getStorageSync('token'),
        name: self.data.name,
        mobile: self.data.phone,
        reason: self.data.reason,
        start_time: self.data.date1 + ' 00:00:00',
        end_time: self.data.date2 + ' 23:59:59',
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded' // 默认值
      },
      success(res) {
        if (res.data.error_code == 0) {
          wx.showToast({
            title: '提交成功',
            icon: 'success',
            duration: 2000,
            success:function(){
              wx.navigateBack({
                delta: 1
              })
            }
          })
        } else {
          wx.showToast({
            title: res.data.error_msg,
            icon: 'none',
            duration: 2000
          })
        }
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

 
})