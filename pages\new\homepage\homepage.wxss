/* pages/new/homepage/homepage.wxss */
.html {
  /* background-color: #ffffff; */
  background: linear-gradient(180deg, #176BEA 2%, rgba(255,255,255,0.00) 53%), #F3F5F9;
  opacity: 1;
}

.top-paper {
  width: 100%;
  position: relative;
}
.userinfo_box{
  display: flex;
  justify-content: space-around;
  margin-left: 30rpx;
  width: 110px;
}
.xq-paper{
  /* width: 100%;
  position: relative; */
  /* margin: 0px auto; */
}
.threeButtonBar{
  position: absolute;
  top: 18px;
  width: 100%;
  
}
.threeButtonBar1{
  position: absolute;
  top: 55px;
  width: 100%;
}
.top-image {
  width: 100%;
  z-index: -1;
}

.fourButtonBar {
  position: absolute;
  top: -50px;
  width: 100%;
}

.fourButton_1 {
  padding-left: 6.25%;
}

.fourButton_2 {
  padding-left: 12.5%;
}

.fiveButton_1 {
  padding-top: 15px;
  padding-bottom: 15px;
}

.fiveButton_2 {
  /* padding-top: 15px; */
  /* padding-left: 5.5%; */
  padding: 15px 5px;
  /* margin-left: 10px; */
  padding-bottom: 15px;
}

.dianjiButtonBar {
  /* width: 100%; */
  /* margin: 0px auto; */
  /* text-align: center;
  margin-top: -100px;
  border-radius: 10px;
  opacity: 0.7;
  height: 36px; */
  /* background: #FFFFFF; */
  margin-top: -100px;
}

.mid-paper {
  /* margin-top: 15px; */
}
.xq_box{
  display: flex;
  justify-content: space-between;
  margin:20px 18px 0 18px;
  font-size: 14px;
}
.mid-paper-bottonBar {
  position: relative;
  width: 100%;
  margin: 0px auto;
  text-align: center;
  margin-top: 10px;
}
.paper_box{
  display: flex;
  justify-content: space-between;
}
.mid-paper-botton {
  margin-left: 3%;
  margin-right: 3%;
}

.swiperPaper {
  width: 100%;
  text-align: center;
  /* margin-top: 10px; */
}

.myjb {
  position: absolute;
  top: -10px;
  right: 10px;
  margin: 5px;
  min-width: 26px;
  padding: 0 3px;
  color: #fafafa;
  font-weight: 500;
  font-size: 12px;
  font-family: PingFang SC, Helvetica Neue, Arial, sans-serif;
  line-height: 14px;
  text-align: center;
  background-color: #e63e3e;
  border: 1px solid #e63e3e;
  border-radius: 16px;
}

.mdqy {
  position: absolute;
  top: -10px;
  left: 135px;
  margin: 5px;
  min-width: 26px;
  padding: 0 3px;
  color: #fafafa;
  font-weight: 500;
  font-size: 12px;
  font-family: PingFang SC, Helvetica Neue, Arial, sans-serif;
  line-height: 14px;
  text-align: center;
  background-color: #e63e3e;
  border: 1px solid #e63e3e;
  border-radius: 16px;
}
.myjb1 {
  position: absolute;
  top: -10px;
  right: 20px;
  margin: 5px;
  min-width: 16px;
  padding: 0 3px;
  color: #fafafa;
  font-weight: 500;
  font-size: 12px;
  font-family: PingFang SC, Helvetica Neue, Arial, sans-serif;
  line-height: 14px;
  text-align: center;
  background-color: #e63e3e;
  border: 1px solid #e63e3e;
  border-radius: 16px;
}
.CommonFuncPaper {
  width: 93%;
  background-color: #c7c7c711;
  border-radius: 4px;
  margin: 0px auto;
  text-align: center;
}
.newsPaper{
  width: 93%;
  background-color: #c7c7c711;
  border-radius: 4px;
  margin: 0px auto;
  text-align: center;
  margin: 0 24px;
}
.search {
  width: calc(100% - 180px);
  font-size: 14px;
}

.search_arr {
  border: 1px solid #d0d0d0;
  border-radius: 20rpx;
  /* margin-left: 30rpx; */
  position: relative;
  
}
.search_box{
  position: absolute;
}

.search_arr input {
  margin-left: 70rpx;
  height: 60rpx;
  border-radius: 5px;
}

.bc_text {
  line-height: 68rpx;
  height: 68rpx;
  margin-top: 34rpx;
}

.sousuo {
  position: absolute;
  /* margin-left: 15rpx; */
  /* width: 13%; */
  right: 0;
  /* line-height: 190%; */
  text-align: center;
  border: 1px solid #e3e4e4;
  
  border-radius: 20rpx;
  color: #ffffff;
  font-size: 16px;
  height: 56rpx;
  font-weight: 700;
}

.page_row {
  display: flex;
  flex-direction: row;
  
  padding-top: 15px;
  padding-bottom: 10px;
}

.searchcion {
  margin: 10rpx 10rpx 10rpx 10rpx;
  position: absolute;
  left: 40rpx;
  z-index: 2;
  width: 20px;
  height: 20px;
  text-align: center;
}

.search_arr {
  background-color: #ffffff;
}

.bottomPaper {
  position: relative;
}

.bottomPaper {
  position: relative;
}
/* .btn_Suspension{
  position: fixed;
  height: 100rpx;
  width: 100rpx;
  border-radius: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999;
}
.Suspension_logo{
  position:absolute;
  height:50%;
  width:50%;
  left:23%;
  top:27%
} */