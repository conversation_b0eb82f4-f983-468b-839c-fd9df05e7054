// pages/new/mine/mine.js
var user = require('../../../pages/new/utils/user.js')
var app = getApp();
var url = app.globalData.requestUrl;
const http = require("../utils/httpUtils.js");
Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    //0为没有管理员权限
    op_status: 0,
    //分配管理员
    fpgly_status: 0,
    //发布公告
    fbgg_status: 0,
    //审核公告
    shgg_status: 0,
    //帮助申报权限
    house_check: 0,
    //配置设备宣传页
    xcy_status: 0,
    //竞赛排行
    jwhc_status: 0,
    //设备管理
    sbgl_status:0,
    //下发大清查任务
    xfrw_status: 0,
    //人员核查
    sbhc_status: 0,
    modalHidden: true,
    //问题反馈
    wtfk_status:0,
    //异常日志
    ycrz_status:0,
    //举报反馈
    jbfk_status:0,
    //所长直通码
    szztm_status:0,
    ewmUrl: '',
    //增企服务码
    zqfwm_status: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },
  resethomepage(){
    let self = this
    wx.showModal({
      title: '提示',
      content: '是否重置当前主页',
      success (res) {
        if (res.confirm) {
          
          wx.request({
            method: 'GET',
            url: 'https://zzsbapi.jxjkga.cn/storemgmt/base/delMyHomePage',
            header: {
              token: wx.getStorageSync('token')
            },
            success(res) {
              if(res.data.success == 1)
              wx.showToast({
                title: '重置主页成功',
                icon: 'success',
                duration: 2000,
                success:function(){
                  // wx.navigateTo({
                  //   url: '/pages/new/user/login'
                  // })
                }
              })
            }
          })
        } else if (res.cancel) {
        }
      }
    })
  },
  reset(){
    let self = this
    wx.showModal({
      title: '提示',
      content: '页面不是本人信息，确定重置',
      success (res) {
        if (res.confirm) {
          
          wx.request({
            method: 'GET',
            url: 'https://zzsbapi.jxjkga.cn/storemgmt/base/resetPhone',
            header: {
              token: wx.getStorageSync('token')
            },
            success(res) {
              if(res.data.success == 1)
              wx.showToast({
                title: '重置成功',
                icon: 'success',
                duration: 2000,
                success:function(){
                  wx.navigateTo({
                    url: '/pages/new/user/login'
                  })
                }
              })
            }
          })
        } else if (res.cancel) {
        }
      }
    })


    
  },
  Accountcancellation(){
    let self = this
    wx.showModal({
      title: '提示',
      content: '确定注销本账号',
      success (res) {
        if (res.confirm) {
          
          wx.request({
            method: 'GET',
            url: 'https://zzsbapi.jxjkga.cn/storemgmt/base/deleteNoAuthWxUser',
            header: {
              token: wx.getStorageSync('token')
            },
            success(res) {
              if(res.data.success == 1)
              wx.exitMiniProgram()
              wx.showToast({
                title: '注销成功',
                icon: 'success',
                duration: 2000,
              })
            }
          })
        } else if (res.cancel) {
        }
      }
    })
  },
  /**
   * 生命周期函数--监听页面显示
   */
  /**跳转页面 */
  toPage: function (e) {
    
    let self = this;
    let url = e.currentTarget.dataset.type == 'h5' ? '../../myWebview/myWebview?h5=' + e.currentTarget.dataset.url + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor;
    if (e.currentTarget.dataset.type == 'alert') {
      wx.showToast({
        title: e.currentTarget.dataset.url,
        icon: 'none',
        duration: 2000
      })
    } else {
      if (self.data.userInfo.isLogin) {
        if (self.data.userInfo.isAuth) {
          wx.navigateTo({
            url: url,
          })
        } else {
          self.doAuth();
        }
      } else {
        wx.showModal({
          title: '提示',
          content: '请先注册并登录',
          success(res) {
            if (res.confirm) {
              wx.redirectTo({
                url: '/pages/new/user/login'
              })
            } else if (res.cancel) {
              wx.showToast({
                title: '登录失败',
                icon: 'none',
                duration: 2000
              })
            }
          }
        })
      }
    }
  },
  toPage2: function (e) {
    let url = e.currentTarget.dataset.type == 'h5' ? '../../myWebview/myWebview?h5=' + e.currentTarget.dataset.url + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor;
    wx.navigateTo({
      url: url,
    })
  },
  get2wm() {
    let self = this;
    http.get('base/user/qrcode', {}, res => {
      if (res.data.success == 1) {
        self.setData({
          modalHidden: false,
          ewmUrl: 'https://zzsbapi.jxjkga.cn' + res.data.data
        })
      }
    })
  },
  onClick2wm(e) { 
    let self = this; 
    if (self.data.userInfo.isLogin) {
      if (self.data.userInfo.isAuth) {
        self.get2wm();
      } else {
        self.doAuth();
      }
    } else {
      wx.showModal({
        title: '提示',
        content: '请先注册并登录',
        success(res) {
          if (res.confirm) {
            self.get2wm();
          } else if (res.cancel) {
            wx.showToast({
              title: '登录失败',
              icon: 'none',
              duration: 2000
            })
          }
        }
      })
    } 
  },
  /**
   * 点击取消
   */
  modalCandel: function () {
    // do something
    this.setData({
      modalHidden: true
    })
  },

  /**
   *  点击确认
   */
  modalConfirm: function () {
    // do something
    this.setData({
      modalHidden: true
    })
  }, 
  onLoad:function(){
   
  },
  onShow: function () {
    var that = this;
    console.log(user.checkPermissionByFuncName("complain-check"))
    user.getUserInfo().then((res) => {
      console.log(res,'sjadkasljdaldjasldjaslkdasjdklasjkdalsdjkaljdklsa');

      that.setData({
        userInfo: getApp().globalData.userInfo,

      }); 

      if (user.checkPermissionByFuncName("police-check")) {
        that.setData({
          op_status: 1
        });
      } else {
        that.setData({
          op_status: 0
        });
      }

      if (user.checkPermissionByFuncName("house-check")) {
        that.setData({
          house_check: 1
        });
      } else {
        that.setData({
          house_check: 0
        });
      }
      if (user.checkPermissionByFuncName("admin-auth") || user.checkPermissionByFuncName("data-auth") || user.checkPermissionByFuncName("function-auth")) {
        that.setData({
          fpgly_status: 1
        });
      } else {
        that.setData({
          fpgly_status: 0
        });
      }
      if (user.checkPermissionByFuncName("feedback-check")) {
        that.setData({
          wtfk_status: 1
        });
      } else {
        that.setData({
          wtfk_status: 0
        });
      }
      
      if (user.checkPermissionByFuncName("complain-check")) {
        that.setData({
          jbfk_status: 1
        });
      } else {
        that.setData({
          jbfk_status: 0
        });
      }
      if (user.checkPermissionByFuncName("error-log-check")) {
        that.setData({
          ycrz_status: 1
        });
      } else {
        that.setData({
          ycrz_status: 0
        });
      }
      if (user.checkPermissionByFuncName("house-notice")) {
        that.setData({
          fbgg_status: 1
        });
      } else {
        that.setData({
          fbgg_status: 0
        });
      }
      if (user.checkPermissionByFuncName("house-notice-check")) {
        that.setData({
          shgg_status: 1
        });
      } else {
        that.setData({
          shgg_status: 0
        });
      }
      if (user.checkPermissionByFuncName("house-adv")) {
        that.setData({
          xcy_status: 1
        });
      } else {
        that.setData({
          xcy_status: 0
        });
      }
      if (user.checkPermissionByFuncName("police-check")) {
        that.setData({
          jwhc_status: 1
        });
      } else {
        that.setData({
          jwhc_status: 0
        });
      }
      if (user.checkPermissionByFuncName("publish-building-task")) {
        that.setData({
          xfrw_status: 1
        });
      } else {
        that.setData({
          xfrw_status: 0
        });
      }

      if (user.checkPermissionByFuncName("icc-device-manage")) {
        that.setData({
          sbgl_status: 1
        });
      } else {
        that.setData({
          sbgl_status: 0
        });
      }
      
      if (user.checkPermissionByFuncName("person-check")) {
        that.setData({
          sbhc_status: 1
        });
      } else {
        that.setData({
          sbhc_status: 0
        });
      }

      if (user.checkPermissionByFuncName("police-director-mailbox")) {
        that.setData({
          szztm_status: 1
        });
      } else {
        that.setData({
          szztm_status: 0
        });
      }

      if (user.checkPermissionByFuncName("yiqi-service-police-team")) {
        that.setData({
          zqfwm_status: 1
        });
      } else {
        that.setData({
          zqfwm_status: 0
        });
      }
    });
  },
  doAuth: function () {
    // user.doAuth();
    wx.navigateTo({
      url: '/pages/new/userCheck/userCheck'
    })
  },
  doSetUserInfo: function () {
    wx.navigateTo({
      url: '/pages/new/mine/info'
    })
  },
  openSetting() { 
    wx.openSetting()
  },
  // doSetLocation: function () {
  //   var that = this;
  //   var userinfo = that.data.userInfo;
     
  //   if (!that.data.userInfo.isOpenLocation) {
  //     wx.getLocation({
  //       type: 'wgs84',
  //       success(res) { 
  //         app.globalData.userInfo.isOpenLocation = true;
  //       },
  //       fail(res) { 
  //         wx.openSetting({}) 
  //       },
  //     }) 
  //   } else {
  //     userinfo.isOpenLocation = true;
  //     that.setData({
  //       userInfo: userinfo,
  //     });
  //     wx.showToast({
  //       title: '您已授权',
  //       icon: 'none',
  //       duration: 2000
  //     })
  //   }
  // },
  // doSetNotice: function () {
  //   var that = this;
  //   var userinfo = that.data.userInfo;

  //   if (!that.data.userInfo.isOpenNotice) {
  //     if (wx.getStorageSync('accept') == '0') {
  //       wx.requestSubscribeMessage({
  //         tmplIds: ['03E4vnN66C-ZMmqMdVxGwD5IeI5cCcVzPwFzki-TiOY'],
  //         success(res) {
  //           userinfo.isOpenNotice = true;
  //           wx.showToast({
  //             title: '授权成功',
  //             icon: 'none',
  //             duration: 2000,
  //             success() {
  //               wx.setStorageSync('accept', '1')
  //             }
  //           })
  //         },
  //         fail(res) {
  //           userinfo.isOpenNotice = false;
  //           that.setData({
  //             userInfo: userinfo,
  //           });
  //           wx.showToast({
  //             title: '授权失败',
  //             icon: 'none',
  //             duration: 2000
  //           })
  //         }
  //       })
  //     } else {
  //       userinfo.isOpenNotice = true;
  //       that.setData({
  //         userInfo: userinfo,
  //       });
  //       wx.showToast({
  //         title: '您已授权',
  //         icon: 'none',
  //         duration: 2000
  //       })
  //     }
  //   } else {
  //     userinfo.isOpenNotice = true;
  //     that.setData({
  //       userInfo: userinfo,
  //     });
  //     wx.showToast({
  //       title: '您已授权',
  //       icon: 'none',
  //       duration: 2000
  //     })
  //   }
  // },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },


})