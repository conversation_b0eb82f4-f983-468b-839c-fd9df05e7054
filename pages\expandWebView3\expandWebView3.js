// pages/expandWebView/expandWebView.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    web_url: '',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(query) {
    let self = this;
    let token = wx.getStorageSync('token');
    let scene = decodeURIComponent(query.scene)
    let targetUrl = "https://zzsbui.jxjkga.cn/#/qyzzsb2wm?utoken=" + encodeURIComponent(token);
    let paramArray = scene.split("&");
    for (var i = 0; i < paramArray.length; i++) {
      let key = paramArray[i].split("=")[0];
      let value = paramArray[i].split("=")[1];
      targetUrl = targetUrl + "&" + key + "=" + value
    }
    self.setData({
      web_url: targetUrl
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

 
})