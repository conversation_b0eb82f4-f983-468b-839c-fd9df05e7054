/* pages/new/szzth/szzth.wxss */

.box {
  padding: 20rpx 60rpx;
  background-color: #f1f1f4;
  /* height: 100vh;
  height: calc(100vh - 40rpx); */
}

.title {
  color: #1a477b;
  font-weight: 800;
  font-style: italic;
  /* font-size: 18px; */
}

.title1 {
  color: #858587;
  font-weight: 700;
  border-bottom: 1px solid #949496;
  margin-top: 40rpx;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
}

.inp_box {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.inp_label {
  width: 200rpx;
  font-weight: 550;
}
.weui_input {
  background-color: #fff;
  height: 70rpx;
  width: 100%;
  padding-left: 20rpx;
  border-radius: 10rpx;
}

.inp_label1 {
  font-weight: 550;
  margin-bottom: 20rpx;
}
.textarea_inp {
  height: 200rpx;
  border-radius: 10rpx;
  padding: 20rpx;
  background-color: #fff;
}
.picker {
  line-height: 70rpx;
}

.box2 {
  margin-top: 30rpx;
}
.title2 {
  text-align: center;
  color: #9a9a9d;
}

.box3 {
  display: flex;
  width: 510rpx;
  margin: 0 auto;
}
.img {
  width: 150rpx;
  height: 150rpx;
  margin: 0 10rpx;
}
.text {
  text-align: center;
  margin-top: -25rpx;
  margin-bottom: 40rpx;
}

.sbm {
  width: 90%;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  color: #fff;
  background-color: #1a477b;
}
.sbm1 {
  width: 90%;
  border-radius: 40rpx;
  height: 80rpx;
  color: #187de8;
  background-color: #f1f1f4;
}