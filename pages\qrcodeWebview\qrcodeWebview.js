// pages/system/system.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    web_url: '',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (query) {
    let self = this;
    let token = wx.getStorageSync('token');
    let scene = decodeURIComponent(query.scene)
    self.setData({
      web_url: scene + '?utoken=' + token
    })
    // wx.setNavigationBarTitle({
    //   title: options.title
    // })
    // wx.setNavigationBarColor({
    //   frontColor: options.fontcolor,
    //   backgroundColor: options.bgcolor,
    //   animation: {
    //     duration: 400,
    //     timingFunc: 'easeIn'
    //   }
    // })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },


})