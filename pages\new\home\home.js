// pages/mine/mine.js

const http = require("../utils/httpUtils.js");
var user = require('../../../pages/new/utils/user.js')
Page({

  /**
   * 页面的初始数据
   */
  data: {wgyr_status: 0,
    userInfo: null,
    imgUrls: [
      '/images/new-banner.png'
    ],
    indicatorDots: true,
    vertical: false,
    autoplay: true,
    circular: true,
    interval: 5000,
    duration: 500,
    previousMargin: 0,
    nextMargin: 0,
    SwiperHeight: '',
    noticeList: [],
    imgs:[]

  },
  //跳转到抽奖界面
  goPrize: function () {
    let self = this;

    if (self.data.userInfo.isLogin) {
      if (self.data.userInfo.isAuth) {
        wx.navigateTo({
          url: '../prize/prize',
        })
      } else {
        self.doAuth();
      }
    } else {
      wx.showModal({
        title: '提示',
        content: '请先注册并登录',
        success(res) {
          if (res.confirm) {
            wx.redirectTo({
              url: '/pages/new/user/login'
            })
          } else if (res.cancel) {
            wx.showToast({
              title: '登录失败',
              icon: 'none',
              duration: 2000
            })
          }
        }
      })
    }
    // if(wx.getStorageSync('lottery') == '0'){

    // }else{
    //   wx.navigateTo({
    //     url: self.data.ll_type == '1' ? '../huaShu/huaShu?code=' + self.data.ll_code : '../getMoney/getMoney?code=' + self.data.ll_code,
    //   })
    // }  
  },
  changeProperty: function (e) {
    var propertyName = e.currentTarget.dataset.propertyName
    var newData = {}
    newData[propertyName] = e.detail.value
    this.setData(newData)
  },
  changeIndicatorDots: function (e) {
    this.setData({
      indicatorDots: !this.data.indicatorDots
    })
  },
  changeAutoplay: function (e) {
    this.setData({
      autoplay: !this.data.autoplay
    })
  },
  intervalChange: function (e) {
    this.setData({
      interval: e.detail.value
    })
  },
  durationChange: function (e) {
    this.setData({
      duration: e.detail.value
    })
  },
  bindSwiperHeight: function (e) {
    var that = this;
    var winWid = wx.getSystemInfoSync().windowWidth; //获取当前屏幕的宽度
    var imgh = e.detail.height; //图片高度
    var imgw = e.detail.width;
    var swiperH = winWid * imgh / imgw + "px"
    //等比设置swiper的高度。  即 屏幕宽度 / swiper高度 = 图片宽度 / 图片高度    ==》swiper高度 = 屏幕宽度 * 图片高度 / 图片宽度
    that.setData({
      SwiperHeight: swiperH
    })
  },
  /**跳转页面 */
  toPage: function (e) {
    let self = this;
    if(!e.currentTarget.dataset.url){
      return
    }
    let url = e.currentTarget.dataset.type == 'h5' ? '../../myWebview/myWebview?h5=' + e.currentTarget.dataset.url + '&bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor + '&title=' + e.currentTarget.dataset.title : e.currentTarget.dataset.url + '?bgcolor=' + e.currentTarget.dataset.bgcolor + '&fontcolor=' + e.currentTarget.dataset.fontcolor;
    if (e.currentTarget.dataset.type == 'alert') {
      wx.showToast({
        title: e.currentTarget.dataset.url,
        icon: 'none',
        duration: 2000
      })
    } else {
       
      if (self.data.userInfo.isLogin) {
        if (self.data.userInfo.isAuth) {
          wx.navigateTo({
            url: url,
          })
        } else {
          self.doAuth();
        }
      } else {
        wx.showModal({
          title: '提示',
          content: '请先注册并登录',
          success(res) {
            if (res.confirm) {
              wx.redirectTo({
                url: '/pages/new/user/login'
              })
            } else if (res.cancel) {
              wx.showToast({
                title: '登录失败',
                icon: 'none',
                duration: 2000
              })
            }
          }
        })
      }
    }
  },
  /**验证是否人证核实 */
  doAuth: function () {
    user.doAuth();
  },
  doLogin: function () {
    var that = this;
    user.getUserInfo().then((res) => {
      that.setData({
        userInfo: getApp().globalData.userInfo
      });
      if (user.checkPermissionByFuncName("foreigner-manage")) {
        that.setData({
          wgyr_status: 1
        });
      } else {
        that.setData({
          wgyr_status: 0
        });
      }
    });
    console.log(this.data);
  },
  bindHouseSelectRole: function () {
    var that = this;
    wx.showToast({
      title: '请选择角色',
      icon: 'none',
      duration: 2000
    })
    wx.showActionSheet({
      itemList: ['我是房东', '我是租客', '自住'],
      success(res) {
        if (that.data.userInfo.isLogin) {
          if (that.data.userInfo.isAuth) {
            if (res.tapIndex == 0) {
              wx.navigateTo({
                url: '/pages/new/house/declareFangDong/declareFangDong/declareFangDong?type=1',
              })
            } else if (res.tapIndex == 2) {
              wx.navigateTo({
                url: '/pages/new/house/declareFangDong/declareFangDong/declareFangDong?type=2',
              })
            } else {
              wx.navigateTo({
                url: '/pages/new/house/declareZuKe/searchHouse/searchHouse',
              })
            }
          } else {
            that.doAuth();
          }
        } else {
          that.doLogin();
        }
      },
      fail(res) {
        console.log(res.errMsg)
      }
    })
  },

  getnotice: function () {
    if (wx.getStorageSync('token')) {
      http.get('house/notice/list', {
        page_num: 1,
        page_size: 2,
        token: wx.getStorageSync('token')
      }, res => {
        if (res.data.success == 1) {
          let mynoticeList = res.data.data.data_list;
          this.setData({
            noticeList: mynoticeList
          });
        }
      })
    }
  },
  getimgs: function () { 
      http.get('base/getAdvertisePicInfo', {
       type:"小程序主页"
      }, res => {
        if (res.data.success == 1) {
          let myImgs = res.data.data;
          this.setData({
            imgs: myImgs
          });
        }
      }) 
  }, 
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {var that = this;
    user.getUserInfo(false).then((res) => {
      this.setData({
        userInfo: getApp().globalData.userInfo
      });
      if (user.checkPermissionByFuncName("foreigner-manage")) {
        that.setData({
          wgyr_status: 1
        });
      } else {
        that.setData({
          wgyr_status: 0
        });
      }
    });
    console.log(getApp().globalData);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    var that = this;
    this.getimgs(); 

    if(wx.getStorageSync('token') ) {
    this.getnotice();
    }
    if (user.checkPermissionByFuncName("foreigner-manage")) {
      that.setData({
        wgyr_status: 1
      });
    } else {
      that.setData({
        wgyr_status: 0
      });
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})