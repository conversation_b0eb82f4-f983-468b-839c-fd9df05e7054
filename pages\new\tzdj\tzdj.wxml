<!--pages/new/tzdj/tzdj.wxml-->
<view class="container" style="background:#fff;">
  <watermark></watermark>
  <view class="title">【退租】报送信息</view>
  <view>
    <form catchsubmit="formSubmit">
      <view><span class="red">*</span>1.请选择日期</view>
      <view class="section">
        <picker mode="date" value="{{rent_unrent_time}}" name="rent_unrent_time"  bindchange="bindDateChange">
          <view class="picker">
            当前选择: {{rent_unrent_time}}
          </view>
        </picker>
      </view>
   
  
    <view><span class="red">*</span>2.退租人员姓名：</view>
    <view>
      <input name="name" value="{{name}}" bindinput="getname" class="ipt" placeholder=""/>
    </view>
    <view><span class="red">*</span>6.退租人员身份证号码</view>
    <view>
      <input name="identity" value="{{identity}}" bindinput="getidentity" type="idcard" class="ipt"  placeholder="" />
    </view>
    <view><span class="red">*</span>7.请输入您的手机号码：</view>
    <view>
      <input name="phone" value="{{phone}}" bindinput="getphone" class="ipt" type="number" placeholder="" />
    </view>
    <view><span class="red">*</span>8.请选择您所在的社区：</view>
    <view class="section">
    <picker  bindchange="bindMultiPickerChange" bindcolumnchange="bindMultiPickerColumnChange" range="{{defaultarray}}" value="{{multiIndex}}" range-key="text">
已选中：{{area_location_name}}
<!-- {{defaultarray[0][multiIndex[0]].text}}-{{defaultarray[1][multiIndex[1]].text}}-{{defaultarray[2][multiIndex[2]].text}}-{{defaultarray[3][multiIndex[3]].text}} -->
</picker>
</view>
    <view ><span class="red">*</span>9.退租详细住址如: 晴湾几幢几零几【要详细填到几幢几零几】</view>
    <view>
      <textarea name="detail_address" value="{{detail_address}}" bindinput="getaddress" class="textaddress"/>
    </view>
    
      <view class="btn-area">
        <button style="margin: 30rpx 0;background:#0081ff;color:#fff;"  bindtap="formSubmit" disabled="{{disabled}}">提交</button>
      </view>
    </form>
  </view>
</view>

