var getProto=Object.getPrototypeOf||function(t){return t.__proto__},hasOwn=Object.prototype.hasOwnProperty,fnToString=hasOwn.toString,ObjectFunctionString=fnToString.call(Object),isPlainObject=function(t){var n,o;return!(!t||"[object Object]"!==toString.call(t))&&(!(n=getProto(t))||"function"==typeof(o=hasOwn.call(n,"constructor")&&n.constructor)&&fnToString.call(o)===ObjectFunctionString)},isFunction=function(t){return"function"==typeof t},extend=function(){var t,n,o,r,e,i,c=arguments[0]||{},a=1,u=arguments.length,f=!1;for("boolean"==typeof c&&(f=c,c=arguments[a]||{},a++),"object"==typeof c||isFunction(c)||(c={}),a===u&&(c=this,a--);a<u;a++)if(null!=(t=arguments[a]))for(n in t)o=c[n],c!==(r=t[n])&&(f&&r&&(isPlainObject(r)||(e=Array.isArray(r)))?(e?(e=!1,i=o&&Array.isArray(o)?o:[]):i=o&&isPlainObject(o)?o:{},c[n]=extend(f,i,r)):void 0!==r&&(c[n]=r));return c};module.exports={extend:extend};