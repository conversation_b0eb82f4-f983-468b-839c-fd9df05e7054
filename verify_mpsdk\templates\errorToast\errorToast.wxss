.weui-mask { position: fixed; z-index: 1000; top: 0; right: 0; left: 0; bottom: 0; background: rgba(0, 0, 0, 0.8); } .weui-dialog { position: fixed; z-index: 5000; width:75%; max-width: 300px; top: 50%; left: 50%; -webkit-transform: translate(-50%, -50%); transform: translate(-50%, -50%); background-color: #FFFFFF; text-align: center; overflow:inherit; border-radius:5px; } .weui-dialog .weui-icon_area { position: absolute; top: 0; left: 50%; transform: translate(-50%, -50%); font-size:0; } .weui-dialog__hd { padding: 1.3em 1.6em 0.5em; padding-top: 50px; margin-bottom: 20px; line-height: inherit; } .weui-dialog__ft { position: relative; line-height: 48px; font-size: 18px; border-top:.5px solid #ccc; } .weui-dialog__btn_primary { color: #2d72f1; } .weui-dialog .weui-icon_area .iconbg{ position: absolute; top: 10rpx; bottom:10rpx; left:10rpx; right:10rpx; background: #fff; border-radius:100%; } .weui-dialog.style2 .weui-icon_area .icon-box-img{ position: relative; } .weui-dialog__title { font-weight: 400; font-size: 18px; } .bg-gray { background: rgba(155,155,155,.1); }