<!--pages/new/shop/info.wxml-->
<view class="page">
  <watermark></watermark>
	<view class="page__bd" wx:if="{{submit.code==-1}}">
		<view class="weui-cells__title">所在小区</view>
		<view class="weui-search-bar">
			<view class="weui-search-bar__form">
				<view class="weui-search-bar__box">
					<icon class="weui-icon-search_in-box" type="search" size="14"></icon>
					<input type="text" class="weui-search-bar__input" placeholder="关键字" value="{{inputXQVal}}" focus="{{inputXQShowed}}" bindinput="inputXQTyping" />
					<view class="weui-icon-clear" wx:if="{{inputXQVal.length > 0}}" bindtap="clearXQInput">
						<icon type="clear" size="14"></icon>
					</view>
				</view>
				<label class="weui-search-bar__label" hidden="{{inputXQShowed}}" bindtap="showXQInput">
					<icon class="weui-icon-search" type="search" size="14"></icon>
					<view class="weui-search-bar__text">关键字搜索小区</view>
				</label>
			</view>
			<view class="weui-search-bar__cancel-btn" hidden="{{!inputXQShowed}}" bindtap="hideXQInput">取消</view>
		</view>
		<view style="overflow-y:scroll;max-height:200px">
			<view class="weui-cells searchbar-result" wx:if="{{inputXQVal.length > 0}}">
				<block wx:for="{{areaSearchResult}}" wx:for-item="item" wx:key="index">
					<view bindtap="getAreaSearchResult" data-result="{{item}}" class="weui-cell" hover-class="weui-cell_active">
						<view class="weui-cell__bd" style="font-size: 14px;">
							<view>
								<block wx:for="{{item}}" wx:for-item="result" wx:key="*this" wx:for-index="itemIndex">
									<label wx:if="{{result.is_match==1}}" style="color:red;">{{result.name}}</label>
									<label wx:if="{{result.is_match==0}}">{{result.name}}</label>
									<label wx:if="{{itemIndex!=item.length-1}}" style="color:#ccc;margin-left:2px;margin-right:3px;">/</label>
								</block>
							</view>
						</view>
						<view class="weui-cell__ft weui-cell__ft_in-access"></view>
					</view>
				</block>
			</view>
		</view>
		<view class="weui-cells weui-cells_after-title">
			<view class="weui-cell">
				<view class="weui-cell__bd">
					<label class="weui-textarea">
						<picker mode="multiSelector" bindcancel="bindcancel" bindchange="bindconfirm" bindcolumnchange="bindMultiPickerColumnChange" value="{{areaIndex}}" range="{{areaArray}}">
							<view class="picker" style="font-size:15px;">
								<block wx:for="{{areaInfo}}" wx:for-item="item" wx:key="index" wx:for-index="index">
									<label wx:if="{{index==areaInfo.length-1}}" style="color:red;">{{item.name}}</label>
									<label wx:if="{{index!=areaInfo.length-1}}">{{item.name}}</label>
									<label wx:if="{{index!=areaInfo.length-1}}" style="color:#ccc;margin-left:2px;margin-right:3px;">/</label>
								</block>
								<label wx:if="{{areaInfo.length==0}}" style="color:gray;">手动选择小区</label>
							</view>
						</picker>
					</label>
				</view>
			</view>
		</view>
		<view class="weui-cells weui-cells_after-title">
			<block wx:for="{{area}}" wx:for-index="index" wx:key="this">
				<view class="weui-cell weui-cell_select" style="padding-left: 16px;">
					<view class="weui-cell__hd">
						<view class="weui-label">{{item.lvl}}</view>
					</view>
					<view class="weui-cell__bd">
						<picker bindchange="bindAreaChange" value="{{0}}" range="{{item.list}}" range-key="{{'name'}}" data-area-index="{{index}}">
							<view class="weui-select">{{item.list[item.current].name}}</view>
						</picker>
					</view>
				</view>
			</block>
		</view>
		<view class="weui-cells__title">门牌号</view>
		<view class="weui-search-bar">
			<view class="weui-search-bar__form">
				<view class="weui-search-bar__box">
					<icon class="weui-icon-search_in-box" type="search" size="14"></icon>
					<input type="text" class="weui-search-bar__input" placeholder="示例：1幢101" value="{{inputMPVal}}" focus="{{inputMPShowed}}" bindinput="inputMPTyping" />
					<view class="weui-icon-clear" wx:if="{{inputMPVal.length > 0}}" bindtap="clearMPInput">
						<icon type="clear" size="14"></icon>
					</view>
				</view>
				<label class="weui-search-bar__label" hidden="{{inputMPShowed}}" bindtap="showMPInput">
					<icon class="weui-icon-search" type="search" size="14"></icon>
					<view class="weui-search-bar__text">关键字搜索门牌</view>
				</label>
			</view>
			<view class="weui-search-bar__cancel-btn" hidden="{{!inputMPShowed}}" bindtap="hideMPInput">取消</view>
		</view>
		<view style="overflow-y:scroll;max-height:200px">
			<view class="weui-cells searchbar-result" wx:if="{{inputMPVal.length > 0}}">
				<block wx:for="{{mpSearchResult}}" wx:for-item="item" wx:key="index">
					<view bindtap="getMPSearchResult" data-result="{{item}}" class="weui-cell" hover-class="weui-cell_active">
						<view class="weui-cell__bd" style="font-size: 14px;">
							<view>
								<label>{{item.building_room_name}}</label>
							</view>
						</view>
						<view class="weui-cell__ft weui-cell__ft_in-access"></view>
					</view>
				</block>
			</view>
		</view>
		<view class="weui-cells weui-cells_after-title">
			<view class="weui-cell">
				<view class="weui-cell__bd">
					<label class="weui-textarea">
						<picker mode="multiSelector" bindcancel="bindcancel" bindchange="bindconfirm" bindcolumnchange="bindMultiPickerMPChange" value="{{mpIndex}}" range="{{mpArray}}">
							<view class="picker" style="font-size:15px;">
								<label wx:if="{{house.address_id!=0}}">{{house.address}}</label>
								<label wx:if="{{house.address_id==0}}" style="color:gray;">手动选择门牌</label>
							</view>
						</picker>
					</label>
				</view>
			</view>
		</view>
		<view class="weui-cells__title">房屋类型</view>
		<view class="weui-cells weui-cells_checkbox weui-cells_after-title">
			<label class="weui-cell weui-check__label">
				<checkbox class="weui-check" value="{{house.house_type_name}}" checked="checked" />
				<view class="weui-cell__hd weui-check__hd_in-checkbox">
					<icon class="weui-icon-checkbox_success" type="success" size="23"></icon>
				</view>
				<view class="weui-cell__bd">{{house.house_type_name}}</view>
			</label>
		</view>
		<view  wx:if="{{house.house_type_name=='自住'}}" class="weui-cells weui-cells_after-title">
			<view class="weui-cell weui-cell_switch">
				<view class="weui-cell__bd">是否出租
					<label class="weui-media-box__desc">(打开后可以通过手机号搜索到)</label>
				</view>
				<view class="weui-cell__ft">
					<switch bindchange="bindIsRent" />
				</view>
			</view>
		</view>
		<!-- <view class="weui-cells weui-cells_after-title">
			<view class="weui-cell">
				<view class="weui-cell__bd">
					<textarea class="weui-textarea" placeholder="示例：5幢302室" style="height: 2em" bindinput="bindAddress" value="{{house.address}}" />
					</view>
      </view>
    </view> -->
		<view class="weui-cells__title" style="display:none;">房东信息（我）</view>
		<view class="weui-panel weui-panel_access myweui-row" style="display:none;">
			<view class="weui-panel__bd myweui-row-body">
				<navigator url="" class="weui-media-box weui-media-box_appmsg" hover-class="weui-cell_active">
					<view class="weui-media-box__hd weui-media-box__hd_in-appmsg">
						<image class="weui-media-box__thumb" src="{{house.owner_photo}}" />
					</view>
					<view class="weui-media-box__bd weui-media-box__bd_in-appmsg">
						<view class="weui-media-box__title">{{house.owner_realname}}
						</view>
						<view class="weui-media-box__desc">
							<label class='product-sku'>{{house.owner_mobilephone_number}} </label>
						</view>
					</view>
				</navigator>
			</view>
		</view>
		<view style="display:none;" class="weui-cells__title">备注</view>
		<view style="display:none;" class="weui-cells weui-cells_after-title">
			<view class="weui-cell">
				<view class="weui-cell__bd">
					<textarea class="weui-textarea" placeholder="请输入备注" style="height: 3.3em" bindinput="bindRemark" value="{{house.remark}}" />
					<view  wx:if="{{false}}" class="weui-textarea-counter">0/200</view>
        </view>
      </view>
    </view>
    <view style="display:none;" class="weui-cells__title">实拍照片</view>
    <view style="display:none;" class="weui-cells">
      <view class="weui-cell">
        <view class="weui-cell__bd">
          <view class="weui-uploader">
            <view class="weui-uploader__hd">
              <view class="weui-uploader__overview">
                <view class="weui-uploader__title">上传图片最大尺寸3M</view>
                <view class="weui-uploader__info">{{files.length}}/9</view>
              </view>
              <view class="weui-uploader__tips" style="display:none;">
                上传提示
              </view>
            </view>
            <view class="weui-uploader__bd">
              <view class="weui-uploader__files" id="uploaderFiles">
                <block wx:for="{{files}}" wx:key="*this">
                  <view class="weui-uploader__file weui-uploader__file_status" bindtap="previewImage" id="{{item}}">
                    <image class="weui-uploader__img" src="{{item.file_path}}" mode="aspectFill" />
                    <view wx:if="{{item.status==-1}}" class="weui-uploader__file-content">
                      {{item.progress}}
                    </view>
                    <view wx:if="{{item.status==0}}" class="weui-uploader__file-content">
                      <icon type="warn" size="23" color="#F43530"></icon>
                    </view>
                  </view>
                </block>
              </view>
              <view class="weui-uploader__input-box">
                <view class="weui-uploader__input" bindtap="bindChooseImage"></view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view style="padding:16px;">
      <button class="weui-btn" type="primary" bindtap="bindSave">保存</button>
		 
    </view>
  </view>
  <view class="weui-msg" wx:if="{{submit.code>=0}}">
    <view class="weui-msg__icon-area">
      <icon type="{{submit.code==0?'success':'warn'}}" size="64"></icon>
    </view>
    <view class="weui-msg__text-area">
      <view class="weui-msg__title">{{submit.code==0?'操作成功':'操作失败'}}</view>
      <view class="weui-msg__desc">{{submit.msg}}
      </view>
    </view>
    <view class="weui-msg__opr-area">
      <view class="weui-btn-area">
        <button class="weui-btn" type="primary">提交审核</button>
      </view>
    </view>
    <view class="weui-msg__opr-area">
      <view class="weui-btn-area">
        <button class="weui-btn" type="default" bindtap="bindGotoList">完成</button>
      </view>
    </view>
    <view class="weui-msg__tips-area">
      <view class="weui-msg__tips">如果所有资料已确认无误，您可以直接提交审核</view>
    </view>
  </view>
</view>