// pages/new/szzth/szzth.js

// const user = require("../new/utils/user");
const user = require("../../new/utils/user");
var app = getApp();
var url = app.globalData.requestUrl;
const FormData = require("../utils/formData.js");


Page({

  /**
   * 页面的初始数据
   */
  data: {
    index: null,
    array: ['点赞', '投诉'],
    type: null,
    police_station_code: null,
    police_title: '直通所长，欢迎您的监督',
    user_address: null,
    identity: null,
    name: null,
    phone: null,
    disabled: false,
    project: null
  },
  doAuth: function () {
    user.doAuth();
  },
  // 用户隐私信息认证代码
  alertSix() {
    console.log('没有获取到SixTip进行路由跳转');
    wx.redirectTo({
      url: '/pages/Bullet/index/index'
    })
  },
  setIndex: function (e) {
    console.log(e);
    this.setData({
      index: e.detail.value
    })
  },
  setType: function (e) {
    console.log(e);
    this.setData({
      type: e.target.dataset.value
    })
  },
  setUser_address: function (e) {
    this.setData({
      user_address: e.target.dataset.value
    })
  },
  setProject: function (e) {
    this.setData({
      project: e.detail.value
    })
  },
  setName: function (e) {
    this.setData({
      name: e.detail.value
    })
  },
  setContent: function (e) {
    this.setData({
      content: e.detail.value
    })
  },
  submit: function (e) {
    if (this.data.name == null) {
      wx.showToast({
        title: "请输入姓名",
        icon: "none",
      });
      return false;
    }
    if (this.data.project == null) {
      wx.showToast({
        title: "请输入单位名称",
        icon: "none",
      });
      return false;
    }
    if (this.data.index == null) {
      wx.showToast({
        title: "请选择事件类型",
        icon: "none",
      });
      return false;
    }
    if (this.data.content == null) {
      wx.showToast({
        title: "请输入事件内容",
        icon: "none",
      });
      return false;
    }
    this.setData({
      disabled: true
    })
    let then = this
    let formData = new FormData();
    //formData.append('satisfaction_level', this.data.type)
    // formData.append('user_address', this.data.user_address)
    formData.append('content', e.detail.value.content)
    //formData.append('police_station_code', wx.getStorageSync('police_type').split('=')[1])
    formData.append('police_station_code', 'CN')
    formData.append('director_mailbox_type', this.data.array[this.data.index].index_name_cn)
    formData.append('company_name', this.data.project)
    formData.append('name', this.data.name)

    let datas = formData.getData();
    wx.request({
      method: 'post',
      url: 'https://zzsbapi.jxjkga.cn/storemgmt/company/submitYiQiServicePoliceTeam',
      header: {
        token: wx.getStorageSync('token'),
        'content-type': datas.contentType
      },
      data: datas.buffer,
      success(res) {
        if (res.data.success == 1) {

          wx.showToast({
            title: '提交成功',
            icon: 'success',
            duration: 2000,
            success: function () {
              wx.switchTab({
                url: '../homeNew/homeNew'
              })
              then.setData({
                disabled: true
              })
            }
          })
        } else {
          then.setData({
            disabled: false
          })
          wx.showToast({
            title: '提交失败',
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (query) {
    let then = this
    wx.request({
      url: `https://zzsbapi.jxjkga.cn/storemgmt/base/getDicListByCode?dic_code=yiqi_service_type`,
      success(res) {
        console.log(res, 'res');
        then.setData({
          array: res.data.data
        })
      }
    })
    let self = this;
    let token = wx.getStorageSync('token');
    let scene = decodeURIComponent(query.scene)
    //debugger
    self.setData({
      police_station_code: scene.split('=')[1]
    })
    if (scene != 'undefined' && scene != '')
      wx.setStorageSync('police_type', scene);

    wx.getPrivacySetting({
      success: res => {
        console.log(res) // 返回结果为: res = { needAuthorization: true/false, privacyContractName: '《xxx隐私保护指引》' }
        if (res.needAuthorization) {
          wx.setStorageSync('soucePage', 'yqfwd');
          // 需要弹出隐私协议
          this.alertSix();
        } else {
          if (token) {
            new Promise(function (resolve, reject) {
              if (wx.getStorageSync('token') && wx.getStorageSync('token') != '') {
                wx.request({
                  method: 'GET',
                  url: url + '/storemgmt/base/user/info/byToken',
                  header: {
                    token: wx.getStorageSync('token')
                  },
                  success(res) {
                    if (res.data.success == 1) {
                      console.log(res.data);
                      self.setData({
                        phone: res.data.data.phone
                      })

                      // wx.setStorageSync('function_set', JSON.stringify(res.data.data.function_set) );
                      app.globalData.userInfo.isLogin = true;
                      if (res.data.data.identity != null && res.data.data.identity != '') {
                        app.globalData.userInfo.isAuth = true;
                      } else {
                        app.globalData.userInfo.isAuth = false;
                      }
                      app.globalData.userInfo.data_permission = res.data.data.data_permission == null ? '' : res.data.data.data_permission;
                      if (app.globalData.userInfo.data_permission != '') {
                        let strArray = app.globalData.userInfo.data_permission.split(',');
                        let permissionArray = [];
                        for (let item in strArray) {
                          if (strArray[item].indexOf('xq') > -1) {
                            permissionArray.push(strArray[item].split('xq')[1])
                          }
                        }
                        app.globalData.userInfo.permissionArray = permissionArray;
                      }
                      app.globalData.userInfo.all_function = res.data.data.all_function == null ? '' : res.data.data.all_function;
                      app.globalData.userInfo.id = res.data.data.id == null ? '' : res.data.data.id;
                      app.globalData.userInfo.realname = res.data.data.name == null ? '' : res.data.data.name;
                      app.globalData.userInfo.id_number = res.data.data.identity == null ? '' : res.data.data.identity;
                      if (res.data.data.best_frame_url != null && res.data.data.best_frame_url != '') {
                        app.globalData.userInfo.photo = app.globalData.requestUrl + res.data.data.best_frame_url;
                      }
                      app.globalData.userInfo.mobilephone_number = res.data.data.phone == null ? '' : res.data.data.phone;
                      app.globalData.userInfo.car_number = res.data.data.car_number == null ? '' : res.data.data.car_number;
                      app.globalData.userInfo.address_reg = res.data.data.address == null ? '' : res.data.data.address;
                      app.globalData.userInfo.professional = res.data.data.occupation == null ? '' : res.data.data.occupation;
                      app.globalData.userInfo.company = res.data.data.user_org_name == null ? '' : res.data.data.user_org_name;
                      resolve();
                    } else {
                      resolve();
                      if (isRedirect != false) {
                        wx.redirectTo({
                          url: '/pages/new/user/login?_info=登录已过期,请重新登录'
                        })
                      }
                    }
                  }
                })
              } else {
                resolve();
                if (isRedirect != false) {
                  wx.redirectTo({
                    url: '/pages/new/user/login',
                  })
                }
              }
            })
          } else {
            wx.showModal({
              title: '提示',
              content: '请先注册并登录',
              success(res) {
                if (res.confirm) {
                  wx.redirectTo({
                    url: '/pages/new/user/login'
                  })
                } else if (res.cancel) {
                  wx.showToast({
                    title: '登录失败',
                    icon: 'none',
                    duration: 2000
                  })
                }
              }
            })
          }
        }
      },
      fail: () => {},
      complete: () => {}
    });

  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})