Component({
  data: {
    userInfo: '',
    formattedTime: ''
  },
  // 以下是旧式的定义方式，可以保持对 <2.2.3 版本基础库的兼容
  attached: function() {
    // 在组件实例进入页面节点树时执行
    const now = new Date();
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');
      
    const formattedTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    setTimeout(() => {
      this.setData({
      userInfo: getApp().globalData.userInfo,
      formattedTime: formattedTime
    })
    // this.userInfo.asc_num = this.userInfo.asc_num
   
    console.log(getApp().globalData.userInfo,formattedTime);
  },1000)
  },
  detached: function() {
    // 在组件实例被从页面节点树移除时执行
  },
  // ...
})