<template name="verifyIndex"><view><view id="{{Common.IsWxNative ? 'index-topNative' : 'index-top'}}"><view id="index-top-1st" class="{{Common.IsWxNative ? 'index-hint color-grey' : 'index-hint'}}">您即将进行{{}}</view><view id="index-top-2nd" class="{{Common.IsWxNative ? 'index-title color-black' : 'index-title'}}">{{page.index.clientName}}</view><view class="{{Common.IsWxNative ? 'index-title color-black' : 'index-title'}}">{{page.index.businessName}}</view><view class="{{Common.IsWxNative ? 'index-hint color-grey' : 'index-hint'}}" style="margin-top:13rpx">{{page.index.certificationCenter}}</view></view><view class="{{Common.IsWxNative ? 'index-btnNative' : 'index-btn'}}"><button type="primary" bindtap="indexToNext" disabled="{{indexDisableBtn}}" hover-class="{{Common.IsWxNative ? 'btn-hoverNative' : 'btn-hover'}}">{{page.index.nextBtnName}}</button></view><view class="index-protocol"><checkbox-group bindchange="checkboxChange" style="display:inline-flex;align-items:center;margin-right:5px;color:#989898;font-size:12px;"><label class="index-protocol-left"><checkbox checked="{{indexChecked}}"></checkbox>我已认证阅读并同意</label></checkbox-group><label id="index-protocol-right" class="{{Common.IsWxNative ? 'color-green' : ''}}" bindtap="switchIndexRule">{{page.index.protocolTitle}}</label></view><view class="verify-footer"><view class="verify-footer-logo" wx:if="{{!page.index.isHideTipsLogo}}"><image src="https://beta.gtimg.com/GodIdent/huiyan/img/hylogo.png" bindtap="switchAboutDlg"></image></view><view></view></view><view class="js_dialog" id="iosDialog1" style="opacity: 1;" wx:if="{{index_rule}}"><view class="weui-mask"></view><view class="weui-dialog protocol" style="width:80%"><view class="weui-dialog__hd" style="padding:0;margin:0;padding:1.3em 1.6em 0.5em;"><strong class="weui-dialog__title">{{protocol.title}}</strong></view><view class="weui-dialog__bd" style="padding:0 0.8em 0.8em;min-height:40px;font-size:15px;line-height:1.3;word-wrap:break-word;word-break:break-all;color:#999999;"><rich-text nodes="{{protocol.clientContent}}" style="text-indent: 25px;"></rich-text><view wx:if="{{protocol.clientContent}}" class="line"></view><view wx:if="{{protocol.content}}"><rich-text nodes="{{protocol.content}}" style="text-indent: 25px;"></rich-text></view></view><view class="weui-dialog__ft"><view bindtap="switchIndexRule" class="{{Common.IsWxNative ? 'weui-dialog__btn weui-dialog__btn_primary color-green' : 'weui-dialog__btn weui-dialog__btn_primary'}}">我知道了</view></view></view></view><view class="js_dialog" id="iosDialog1" style="opacity: 1;" wx:if="{{show_about_dlg}}"><view class="weui-mask"></view><view class="weui-dialog about" style="width:80%"><view class="weui-dialog__hd" style="padding:0;margin:0;padding:1.3em 1.6em 0.5em;"><strong class="weui-dialog__title">{{about.title}}</strong></view><view class="weui-dialog__bd" style="padding:0 0.8em 0.8em;min-height:40px;font-size:15px;line-height:1.3;word-wrap:break-word;word-break:break-all;color:#999999;text-align:left;"><text>{{about.content}}</text></view><view class="weui-dialog__ft"><view bindtap="switchAboutDlg" class="{{Common.IsWxNative ? 'weui-dialog__btn weui-dialog__btn_primary color-green' : 'weui-dialog__btn weui-dialog__btn_primary'}}">我知道了</view></view></view></view></view></template>