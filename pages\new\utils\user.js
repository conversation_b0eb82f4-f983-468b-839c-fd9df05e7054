var app = getApp();
var url = app.globalData.requestUrl;

/*获取用户信息 */
var getUserInfo = function (isRedirect) {
  return new Promise(function (resolve, reject) {

    if (wx.getStorageSync('token') && wx.getStorageSync('token') != '') {
      wx.request({
        method: 'GET',
        url: url + '/storemgmt/base/user/info/byToken',
        header: {
          token: wx.getStorageSync('token')
        }, 
        success(res) {
          console.log(res)
          if (res.data.success == 1) {
            console.log(res.data,'个人信息接口获取11');
            // wx.setStorageSync('function_set', JSON.stringify(res.data.data.function_set) );
            app.globalData.userInfo.isLogin = true;
            if (res.data.data.identity != null && res.data.data.identity != '') {
              app.globalData.userInfo.isAuth = true;
            } else {
              app.globalData.userInfo.isAuth = false;
            }
            //获取加密过的身份证号码 用ASCII码互转成身份证号码
            if (res.data.data.asc_num != null && res.data.data.asc_num != '') {
              let new_asc_num = "";
              var array = res.data.data.asc_num.split('');
              for(var i=0;i<array.length;i++){
                new_asc_num += String.fromCharCode(array[i].charCodeAt() -20);
              }
              app.globalData.userInfo.asc_num = new_asc_num;
              //console.log("转码的身份证"+new_asc_num)
            } 

            // if (wx.getStorageSync('accept') == "1") {
            //   app.globalData.userInfo.isOpenNotice = true;
            // } 
            // wx.getSetting({
            //   success: (res) => { 
            //     if (res.authSetting['scope.userLocation']) {
            //       app.globalData.userInfo.isOpenLocation = true;
            //     }else{
            //       // wx.getLocation({
            //       //   type: 'wgs84',
            //       //   success (res) {
            //       //     app.globalData.userInfo.isOpenLocation = true;
            //       //   }
            //       //  })
            //     }
            //   }
            // }) 
            app.globalData.userInfo.data_permission = res.data.data.data_permission == null ? '' : res.data.data.data_permission;
            if (app.globalData.userInfo.data_permission != '') {
              let strArray = app.globalData.userInfo.data_permission.split(',');
              let permissionArray = [];
              for (let item in strArray) {
                if (strArray[item].indexOf('xq') > -1) {
                  permissionArray.push(strArray[item].split('xq')[1])
                }
              }
              app.globalData.userInfo.permissionArray = permissionArray;
            }
            app.globalData.userInfo.all_function = res.data.data.all_function == null ? '' : res.data.data.all_function;
            app.globalData.userInfo.id = res.data.data.id == null ? '' : res.data.data.id;
            app.globalData.userInfo.realname = res.data.data.name == null ? '' : res.data.data.name;
            app.globalData.userInfo.is_jsq_admin = res.data.data.is_jsq_admin == 1 ? true : false;
            app.globalData.userInfo.id_number = res.data.data.identity == null ? '' : res.data.data.identity;
            app.globalData.userInfo.openid = res.data.data.openid == null ? '' : res.data.data.openid;
            if (res.data.data.best_frame_url != null && res.data.data.best_frame_url != '') {
              app.globalData.userInfo.photo = imgUL_Get( res.data.data.best_frame_url)
   console.log(app.globalData.userInfo.photo,'app.globalData.userInfo.photo');
              // app.globalData.userInfo.photo = app.globalData.requestUrl + res.data.data.best_frame_url 图片地址路径;
            }
            app.globalData.userInfo.mobilephone_number = res.data.data.phone == null ? '' : res.data.data.phone;
            app.globalData.userInfo.car_number = res.data.data.car_number == null ? '' : res.data.data.car_number;
            app.globalData.userInfo.address_reg = res.data.data.address == null ? '' : res.data.data.address;
            app.globalData.userInfo.professional = res.data.data.occupation == null ? '' : res.data.data.occupation;
            app.globalData.userInfo.company = res.data.data.user_org_name == null ? '' : res.data.data.user_org_name;
            
            app.globalData.userInfo.is_fkgzz_state = res.data.data.is_fkgzz_admin == null ? '': res.data.data.is_fkgzz_admin;
            app.globalData.userInfo.is_jxyjb_admin = res.data.data.is_jxyjb_admin == null ? '': res.data.data.is_jxyjb_admin;
            resolve();
          } else {
            resolve();
            debugger
            if (isRedirect != false) {
              wx.redirectTo({
                url: '/pages/new/user/login?_info=登录已过期,请重新登录'
              })
            }
          }
        }
      })
    } else {
      resolve();
      if (isRedirect != false) {
        wx.redirectTo({
          url: '/pages/new/user/login',
        })
      }
    }
  })
}

/* 实名认证 */
var doAuth = function (success) {
  getUserInfo().then((res) => {
    if (app.globalData.userInfo.isAuth) {
      wx.showToast({
        title: '实名认证已通过',
        icon: 'none',
        duration: 2000
      })
      return true;
    } else {

      wx.showModal({
        title: '警告！',
        content: '请务必使用本人身份证进行认证,冒用他人身份是违法行为！谢谢您的配合。',
        confirmText: '我知道了',
        confirmColor: '#008000',
        cancelText: '暂不认证',
        cancelColor: '#ff0000',
        success(res) {
          if (res.confirm) {
            console.log(res)
            //提示信息
            wx.request({
              method: 'POST',
              url: url + '/storemgmt/base/face/detectAuth',
              data: {
                // IdCard: '',
                // Name: ''
              },

              
              header: {
                'content-type': 'application/x-www-form-urlencoded' // 默认值
              },
              success(res) {
                if (res.data.success == 1) {
                  let BizToken = res.data.data.bizToken; // 去客户后端调用DetectAuth接口获取BizToken
                  console.log('BizToken============'+BizToken)
                        // 调用实名核身功能
                        wx.startVerify({
                          data: {
                            token: BizToken // BizToken
                          },
                          success: (res) => { // 验证成功后触发
                            wx.showToast({
                              title: '验证成功',
                              icon: 'none',
                              duration: 2000
                            })
                            // res 包含验证成功的token, 这里需要加500ms延时，防止iOS下不执行后面的逻辑
                            setTimeout(() => {
                              // 验证成功后，拿到token后的逻辑处理，具体以客户自身逻辑为准
                              wx.request({
                                method: 'POST',
                                url: url + '/storemgmt/base/face/getDetectInfo',
                                data: {
                                  bizToken: BizToken
                                },
                                header: {
                                  'content-type': 'application/x-www-form-urlencoded', // 默认值
                                  token: wx.getStorageSync('token')
                                },
                                success(res) {
                                  if (res.data.success == 1) {
                                    wx.showToast({
                                      title: '认证成功',
                                      icon: 'none',
                                      duration: 2000
                                    })
                                    getUserInfo().then(()=>{
                                      
                                      if(success){
                                        success()
                                      }else{
                                        wx.switchTab({
                                          url: '/pages/new/homeNew/homeNew',
                                        })
                                      }
                                    })
                                  } else {
                                    wx.showToast({
                                      title: res.data.error_msg,
                                      icon: 'none',
                                      duration: 2000
                                    })
                                  }
                                  console.log(res.data);
                                  
                                },
                                fail: (err) => {
                                  wx.showToast({
                                    title: '验证成功后调用失败',
                                    icon: 'none',
                                    duration: 2000
                                  })
                                }
                              })
                            }, 500);

                          },
                          fail: (err) => { // 验证失败时触发
                            // err 包含错误码，错误信息，弹窗提示错误
                            setTimeout(() => {
                              wodal({
                                title: "提示",
                                content: err.ErrorMsg,
                                showCancel: false
                              })
                            }, 500);
                          }
                        });
                  // wx.showActionSheet({
                  //   itemList:['简易认证','腾讯认证'],
                  //   success ({tapIndex}) {
                  //     if(tapIndex==1){
                        
                  //     }
                  //     console.log(res.tapIndex)
                  //   },
                  //   fail (res) {
                  //     console.log(res.errMsg)
                  //   }
                  // })

                  
                } else {
                  wx.showToast({
                    title: res.data.error_msg,
                    icon: 'none',
                    duration: 2000
                  })
                }
              }
            })

          } else if (res.cancel) {
            return;
          }
        }
        
      })

    }
  });
}
var  imgUL_Get =function (url){
  var token =encodeURIComponent( wx.getStorageSync('token'))
  return 'https://zzsbapi.jxjkga.cn/storemgmt/base/imgAccessControl2?img_url=' +url+'&token='+token
}
var checkPermission = function (building_id,func_name) {
  let flag1 = false;
  let flag2 = false;
  let permissionArray = getApp().globalData.userInfo.permissionArray;
  let all_function = getApp().globalData.userInfo.all_function;
  // 判断地区权限
  for (let item in permissionArray) {
    if (building_id.toString().indexOf(permissionArray[item]) > -1) {
      flag1 =  true;
      break;
    }
  }
  //判断功能权限
  for (let item2 in all_function) {
    if (all_function[item2] == func_name ) {
      flag2 =  true;
      break;
    }
  }
  return (flag1&&flag2);
}

var checkPermissionByFuncName = function (func_name) {
  let all_function = getApp().globalData.userInfo.all_function;
  //判断功能权限
  for (let item2 in all_function) {
    if (all_function[item2] == func_name ) {
      return (true);
    }
  }
  return (false);
  
}

var get_is_fkgzz_state = function (){
  
  let is_fkgzz_state = getApp().globalData.userInfo.is_fkgzz_state;
  return is_fkgzz_state;
}

var checkPhone = function (phone) {
  let str = /^1\d{10}$/
  if (str.test(phone)) {
    return true
  } else {

    return false

  }
}

/* output */
module.exports = {
  getUserInfo: getUserInfo,
  doAuth: doAuth,
  checkPermission: checkPermission,
  imgUL_Get:imgUL_Get,
  checkPhone: checkPhone,
  checkPermissionByFuncName:checkPermissionByFuncName,
  get_is_fkgzz_state:get_is_fkgzz_state
}