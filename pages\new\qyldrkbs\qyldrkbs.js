// pages/new/zkdj/zkdj.js
const http = require("../utils/httpUtils.js");
const FormData = require("../utils/formData.js");
var user = require('../utils/user.js')
var app = getApp();
var url = app.globalData.requestUrl;
var columnsArr = app.globalData.columnsArr;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    disabled: false,
    identitytype: false,
    data_type: 1,
    company_id: '',
    rent_unrent_time: '',
    rent_contract_img_file: '',
    face_img_file: '',
    identity: '',
    address: '',
    area_location_id: '',
    area_location_name: '',
    company_name: '',
    phone: '',
    detail_address: '',
    name: '',
    house_type: '',
    columnsArr: [],
    defaultarray: [
      [],
      [],
      [],
      []
    ],
    multiIndex: [0, 0, 0, 0],
  },
  formSubmit: function (e) {
    console.log(this.data.identitytype);
    console.log(e.detail.value)
    let that = this;

    if (e.detail.value.rent_unrent_time == null || e.detail.value.rent_unrent_time == '') {
      wx.showToast({
        title: "请选择日期",
        icon: "none",
      });
      return false;
    }
    if (e.detail.value.name == '') {
      wx.showToast({
        title: "请填写姓名",
        icon: "none",
      });
      return false;
    }
    if (e.detail.value.identity == '') {
      wx.showToast({
        title: "请填写身份证号",
        icon: "none",
      });
      return false;
    }
    if (e.detail.value.phone == "") {
      wx.showToast({
        title: "请输入联系电话",
        icon: "none",
      });
      return false;
    }
    if (that.data.face_img_file == '') {
      wx.showToast({
        title: "请上传头像",
        icon: "none",
      });
      return false;
    }
    if (that.data.rent_contract_img_file == '') {
      wx.showToast({
        title: "请上传身份证照片",
        icon: "none",
      });
      return false;
    }

   
    // 验证电话格式
    if (!/^(\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}$/.test(e.detail.value.phone)) {
      wx.showToast({
        title: "手机号码有误",
        duration: 2000,
        icon: "none",
      });

      return false;
    }
    that.setData({
      disabled: true
    })
    let formData = new FormData();
    formData.append('area_location_id', that.data.area_location_id)
    formData.append('area_location_name', that.data.area_location_name)
    formData.append('company_id', that.data.company_id)
    formData.append('company_name', that.data.company_name)
    formData.append('detail_address', that.data.detail_address)
    formData.append('entry_date', e.detail.value.rent_unrent_time)
    formData.append("name", e.detail.value.name);
    formData.append('identity', e.detail.value.identity)
    formData.append('phone', e.detail.value.phone)
    formData.appendFile('identity_img_file', that.data.rent_contract_img_file[0].path)
    formData.appendFile('face_img_file', that.data.face_img_file[0].path)


    let datas = formData.getData();
    wx.request({
      method: 'post',
      url: 'https://zzsbapi.jxjkga.cn/storemgmt/company/ldrkPush',
      header: {
        token: wx.getStorageSync('token'),
        'content-type': datas.contentType
      },
      data: datas.buffer,
      success(res) {
        if (res.data.success == 1) {

          wx.showToast({
            title: '提交成功',
            icon: 'success',
            duration: 2000,
            success: function () {
              wx.switchTab({
                url: '../homeNew/homeNew'
              })
            }
          })
        } else {
          that.setData({
            disabled: false
          })
          wx.showToast({
            title: '提交失败',
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },

  getphone: function (e) {
    this.setData({
      phone: e.detail.value
    })
  },
  getidentity: function (e) {
    this.setData({
      identity: e.detail.value
    })
    this.verifyIdentity_PD(e.detail.value)
  },
  getname: function (e) {
    this.setData({
      name: e.detail.value
    })
  },
  radioChange: function (e) {
    this.setData({
      house_type: e.detail.value
    })
  },
  getface: function () {
    let _this = this;
    wx.chooseImage({
      count: 1,
      success(res) {
        //此处会返回图片暂存路径和文件大小
        const data = res.tempFiles;
        console.log(data)
        _this.setData({
          face_img_file: data
        })
      }
    })
  },
  getrent: function () {
    let _this = this;
    wx.chooseImage({
      count: 1,
      success(res) {
        //此处会返回图片暂存路径和文件大小
        const data = res.tempFiles;
        const file = res.tempFilePaths
        console.log(file)
        _this.setData({
          rent_contract_img_file: data
        })
      }
    })
  },
  _onDelTab(e) {
    // 获取图片索引
    let idx = e.currentTarget.dataset.idx;
    let delFile = this.data.rent_contract_img_file[idx];
    console.log(delFile);
    this.data.rent_contract_img_file.splice(idx, 1);
    this.setData({
      rent_contract_img_file: this.data.rent_contract_img_file
    })
  },
  _onDelFace(e) {
    // 获取图片索引
    let idx = e.currentTarget.dataset.idx;
    let delFile = this.data.face_img_file[idx];
    console.log(delFile);
    this.data.face_img_file.splice(idx, 1);
    this.setData({
      face_img_file: this.data.face_img_file
    })
  },
  upload: function () {
    let _this = this;
    wx.chooseImage({
      count: 1,
      success(res) {
        //此处会返回图片暂存路径和文件大小
        const data = res.tempFiles;
      }
    })
  },
  bindDateChange: function (e) {
    console.log('picker发送选择改变，携带值为', e.detail.value)
    this.setData({
      rent_unrent_time: e.detail.value
    })
  },
  transAreaData(arrData) {
    let arr = [];
    let obj = {
      text: '',
      children: []
    }
    if (arrData.length > 0) {
      arrData.forEach(item => {
        if (item.childTrees && item.childTrees.length > 0) {
          arr.push({
            id: item.id,
            text: item.name,
            children: this.transAreaData(item.childTrees)
          })
        } else {
          arr.push({
            id: item.id,
            text: item.name
          });
        }
      });
    } else {
      arr.push({
        text: ''
      })
    }
    return arr;
  },
  bindMultiPickerChange: function (e) {
    console.log('picker发送选择改变，携带值为', e.detail.value)
    // var that = this;
    // var defaultarray = that.data.defaultarray,
    // var area_location_name =defaultarray[0][multiIndex[0]].text+defaultarray[1][multiIndex[1]].text+defaultarray[2][multiIndex[2]].text+defaultarray[3][multiIndex[3]].text
    this.setData({
      multiIndex: e.detail.value,
      // area_location_name:area_location_name
    })
  },


  verifyIdentity_PD(val) {
    var that = this;
    if (val.length == 18) {
      let param = new FormData() // 创建form对象
      param.append('identity', val) // 通过append向form对象添加数据
      let datas = param.getData();
      wx.request({
        method: 'post',
        url: 'https://zzsbapi.jxjkga.cn/storemgmt/base/verifyIdentity',
        header: {
          token: wx.getStorageSync('token'),
          'content-type': datas.contentType
        },
        data: datas.buffer,
        success(res) {
          if (res.data.success == 1) {
            wx.showToast({
                title: '身份证格式正确',
                icon: 'success',
                duration: 2000
              }),
              that.setData({
                identitytype: true
              });
          } else {
            wx.showToast({
                title: '身份证格式错误',
                icon: 'none',
                duration: 2000
              }),
              that.setData({
                identitytype: false
              });
          }
        },
        fail(res) {
          console.log(res)
        }
      })
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (query) {
    let scene = decodeURIComponent(query.scene)
    let paramArray = scene.split("&");
    for (var i = 0; i < paramArray.length; i++) {
      this.company_id =paramArray[i].split("=")[1];
    }
    console.log( this.company_id,'paramArray 路由传参');
// 通过id获取企业信息（无需登录）
// /company/getLdrkPushCompanyInfoById
http.get('/company/getLdrkPushCompanyInfoById', {
  company_id:this.company_id 
}, res => {
  if (res.data.success == 1) {
    let tempData = res.data.data;
    this.setData({
      detail_address:tempData.address,
      area_location_id:tempData.area_location_id,
      area_location_name:tempData.area_location_name,
      company_name:tempData.company_name,
      company_id:tempData.id,
    })
    console.log(tempData,'tempData');
  }})
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    //   var defaultarray = this.data.defaultarray
    //   var array = this.data.columnsArr
    //  console.log(this.data.columnsArr)
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },


})