/* verify_mpsdk/index/index.wxss */

@import "../templates/navTip/navTip.wxss";
@import "../templates/errorToast/errorToast.wxss";
@import "../templates/sms/sms.wxss";
@import "../templates/ocr/ocr.wxss";
@import "../templates/livingbody/livingbody.wxss";
@import "../templates/showAuthToast/showAuthToast.wxss";
@import "../templates/success/success.wxss";
@import "../templates/notice/notice.wxss";

#fix-full-page {
  position:fixed;top:0;bottom:0;left:0;right:0;
  width:100%;height:100%;
}

#fix-full-page button[disabled] {
  background:#B3B3B3;
  color: rgba(255,255,255,.6);
}

.verify-gray-container {
 background-color:#F8F8F8;
}

.verify-absolute-bg {
  position:absolute;top:0;bottom:0;width:100%;
}
.verify-white-bg {
  background:#fff;
}

.hint-error {
  padding:10px;
  color:#ef4b46;
  font-size:14px;
  height: 19px;
}

.full-weight-height {
  width:100%;
  height:100%;
}

#index-top {
  height: 650rpx;width: 750rpx;
  text-align: center;
  color: white;
  background: url(http://beta.gtimg.com/GodIdent/huiyan/img/index-bg.png) no-repeat 0 0 / 100% 100%;
}

#index-topNative {
  height: 650rpx;width: 750rpx;
  text-align: center;
  color: white;
  background: url(https://s.beta.gtimg.com/GodIdent/huiyan-h5/images-wx/index-bg.png) no-repeat 0 0 / 100% 100%;
}

.color-grey {
  color: #a3a3a3 !important;
}
.color-black {
  color: #000 !important;
}
.color-green {
  /* color: #18ae17 !important; */
  color: #04be02 !important;
}

.index-hint {
  font-size: 12px;
}

.index-title {
  font-size: 56rpx;
}

#index-top-1st {
  padding-top: 320rpx;
}

#index-top-2nd {
  padding-top: 20rpx;
}

.index-btn {
  margin: 30rpx 30rpx 10rpx 30rpx;
}

.index-btn button{
  background-color:#2d72f1;
  color:white;
}

.index-btnNative {
  margin: 30rpx 30rpx 10rpx 30rpx;
}

.index-btnNative button{
  /* background-color:#2d72f1; */
  color:white;
}

.index-btnNative button[disabled] {
  color:rgba(255, 255, 255, 0.6) !important;
}
.index-btnNative button[disabled][type=primary] {
  background-color:#9ED99D !important;
}


.weui-dialog.protocol .weui-dialog__bd{
    text-align:left;
    max-height:300px;
    overflow-y:auto;
}

.line {
    height: 2rpx;
    background: #888;
    margin: 20rpx;
}

.index-protocol {
  display:inline-flex;align-items:center;
  /* height:20px; */
  margin-left: 30rpx;
  font-size: 12px;
}

.index-protocol-left {
  display:inline-flex;
  color:#989898;
}

.index-protocol checkbox {
  zoom:0.6;
  width:35px;
  /* height:20px; */
  margin-top:2px;
}

#index-protocol-right {
  margin-left:5px;
  color:#007aff;
}

.verify-footer {
  position:absolute;bottom:0;
  width:100%;
  padding:30rpx 0;
  text-align:center;
}
.verify-footer-logo {
  height:16px;
}
.verify-footer-logo  image {
  width:88px;height:17px;
}

#index-footer-about {
  font-size:10px;
  color:#989898;
  margin-top:8rpx;
  border-bottom:thin solid #2473e9;
}

/* btn hover class, must add !important in mp */
.btn-hover {
  /* background: rgba(45,114,241) !important; */
  background: rgb(11, 89, 235) !important;
}
.btn-hoverNative {
  /* #1AAD19 */
  background: rgba(26,173,25,.8) !important;
}

.verifyFailWarn {
  display:flex;flex-direction:row; align-items:center;justify-content:center;
  margin-bottom:5px;padding:0 20px;
  text-align:center;font-weight:400;font-size:17px;
}

.verifyFailTop {
  width:100%;margin:126rpx 0 50rpx 0;
  text-align:center;
}

.page-body-link{
   font-size:14px;
   color:#007fff;
   text-align: center;
   margin-top: 15px;
 }

 .verifycamFullScreen {
   position:absolute;top:0;bottom:0;
   width:100%;
 }