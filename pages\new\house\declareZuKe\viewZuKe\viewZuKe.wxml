<!--pages/new/house/member/view.wxml-->
<watermark></watermark>
<view class="page">
	<view class="page__bd">
		<view class="weui-cells__title">{{type == 3?"家庭成员信息":"租客信息"}}</view>
		<view class="weui-panel weui-panel_access myweui-row">
			<view class="weui-panel__bd myweui-row-body">
				<navigator url="" class="weui-media-box weui-media-box_appmsg" hover-class="weui-cell_active">
					<view class="weui-media-box__hd weui-media-box__hd_in-appmsg">
						<image class="weui-media-box__thumb" src="{{member.member_photo}}" />
					</view>
					<view class="weui-media-box__bd weui-media-box__bd_in-appmsg">
						<view class="weui-media-box__title">{{member.member_realname}}
						</view>
						<view class="weui-media-box__desc">
							<label class='product-sku'>{{member.member_mobilephone_number}}</label>
						</view>
					</view>
				</navigator>
			</view>
			<view class="weui-cells weui-cells_after-title">
				<view class="weui-cell ">
					<view class="weui-cell__hd">
						<view class="weui-label">房间号</view>
					</view>
					<view class="weui-cell__bd">
						{{member.room_number}}
					</view>
				</view>
				<view wx:if="{{type!=3}}" class="weui-cell ">
					<view class="weui-cell__hd">
						<view class="weui-label">租赁时间起</view>
					</view>
					<view class="weui-cell__bd">
						<view class="weui-input">{{member.live_date_begin}}</view>
					</view>
				</view>
				<view wx:if="{{type!=3}}"  class="weui-cell ">
					<view class="weui-cell__hd">
						<view class="weui-label">租赁时间止</view>
					</view>
					<view class="weui-cell__bd">
						<view class="weui-input">{{member.live_date_end}}</view>
					</view>
				</view>
			</view>
		</view>
		<view class="weui-cells__title">备注</view>
		<view class="weui-cells weui-cells_after-title">
			<view class="weui-cell">
				<view class="weui-cell__bd">
					<label class="weui-textarea">{{member.remark==""|| remark==null?"无":member.remark}}  </label>
				</view>
			</view>
		</view>
		<view class="weui-loadmore">
			<view class="weui-loadmore__tips weui-loadmore__tips_in-line">
				<label class='tips-icon'>申请时间</label>: {{member.intime}}</view>
		</view>
		<view style="background-color:#fff;height:20px;"></view>
		<view class="weui-cells__title">审批信息</view>
		<view class="weui-panel weui-panel_access myweui-row">
			<view class="weui-cells weui-cells_form">
				<view class="qwui-timeline">
					<view class="qwui-timeline-item" wx:for="{{checks}}" wx:for-item="item">
						<icon class="qwui-timeline-axis iconfont icon-{{item.icon}} {{item.colorClass}}"></icon>
						<view class="qwui-timeline-content qwui-text">
							<view class="qwui-timeline-title">
								{{item.step_name}}
								<label class="qwui-timeline-title-info">
									<icon class="iconfont icon-android"></icon>{{item.time}}
								</label>
							</view>
							<view class="qwui-timeline-details">
								<label class="qwui-timeline-details-light">{{item.username}}</label>
								<label class="qwui-timeline-details-deep">{{item.content}}</label>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</view>
<view wx-if="{{op_status==-1}}" class="check-btn-box">
	<view wx:if="{{checkData.status==0}}" class="check-btn-input">
		<view class="check-btn-txt">
			<label>理由</label>
		</view>
		<view>
			<input class="weui-input" id="check_content" bindinput="setCheckContent" placeholder="请输入拒绝理由" />
		</view>
	</view>
	<view class="check-btn-line">
		<view class="check-btn-txt">
			<label>审核</label>
		</view>
		<view bindtap="bindCheck" data-status='1' class="check-btn-status1">
			<label>通过</label>
		</view>
		<view bindtap="bindCheck" data-status='0' class="check-btn-status0">
			<label>拒绝</label>
		</view>
	</view>
</view>

<view wx-if="{{op_status==-2}}" class="check-btn-box">
	<view class="check-btn-line">
		<view class="check-btn-txt">
			<label>操作</label>
		</view>
		<view bindtap="bindCheck" data-status='-1' class="check-btn-status1">
			<label>提交审核</label>
		</view>
		<view bindtap="bindCheck" data-status='-2' class="check-btn-status-2">
			<label>修改资料</label>
		</view>
	</view>
</view>
<!-- <view wx-if="{{op_status==-4&&type!=3}}" class="check-btn-box">
	<view class="check-btn-line">
		<view class="check-btn-txt">
			<label>操作</label>
		</view>
		<view bindtap="xuzuCheck" data-isxuzu='1' class="check-btn-status1">
			<label>续租</label>
		</view>
		<view bindtap="xuzuCheck" data-isxuzu='0' class="check-btn-status-2">
			<label>退租</label>
		</view>
	</view>
  <modal hidden="{{showNewDataMoadl}}" title="请选择续租截止时间" confirm-text="确认" cancel-text="取消" bindcancel="cancelNewDate" bindconfirm="confirmNewDate">	  
       <picker mode="date" value="{{date}}" bindchange="bindDateChange">
              <view style="text-align:center" class="weui-input">{{change_live_date_end}}</view>
       </picker>						 
   </modal>
	 <modal hidden="{{showOldDataMoadl}}" title="请确认退租时间" confirm-text="确认" cancel-text="取消" bindcancel="cancelOldDate" bindconfirm="confirmOldDate">	  
       <picker mode="date" value="{{date}}" bindchange="bindDateChange">
              <view style="text-align:center" class="weui-input">{{change_live_date_end}}</view>
       </picker>						 
   </modal>  
</view> -->