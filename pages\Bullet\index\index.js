Page({
  handleAgreePrivacyAuthorization() {
    let soucePage = wx.getStorageSync('soucePage');
    let utonken = wx.getStorageSync('token');

    if (soucePage == 'expandWebView4') {
      console.log("进入了跳转到登入页面")
      wx.redirectTo({
        url: '/pages/expandWebView4/expandWebView4'
      })
    } else if (soucePage == 'lkzzbs') {
      wx.redirectTo({
        url: '/pages/new/lkzzbs/lkzzbs'
      })
    } else if (soucePage == 'visit') {
      wx.redirectTo({
        url: '/pages/building/visit'
      })
    }
    else if (soucePage == 'szzth') {
      wx.redirectTo({
        url: '/pages/new/szzth/szzth'
      })
    }
    else if (soucePage == 'yqfwd') {
      wx.redirectTo({
        url: '/pages/new/yqfwd/yqfwd'
      })
    } else {
      wx.switchTab({
        url: '/pages/new/homeNew/homeNew'
      })
    }
  },
  disagree(e) {
    wx.exitMiniProgram()
    console.log("用户拒绝隐私授权, 未同意过的隐私协议中的接口将不能调用")
  },
})