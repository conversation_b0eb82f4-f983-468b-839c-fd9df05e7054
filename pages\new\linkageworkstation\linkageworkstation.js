// pages/new/linkageworkstation/linkageworkstation.js
const http = require("../utils/httpUtils.js");
var user = require('../../../pages/new/utils/user.js')
var app = getApp();
var url = app.globalData.requestUrl;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    winWid: '',
    SwiperHeight:'',
    userInfo: null,
    ldWorkToken:'',
    utoken:'',
    stamp:''
  },
  getLdWorkToken(){
    let self = this;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/base/getLdWorkToken',
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          let ldWorkToken = res.data.data
          // console.log(ldWorkToken)
          self.setData({
            ldWorkToken:ldWorkToken
          })
        } else {
         
        }
      },
      fail(res) {
        console.log(res)
      }
    })
 
  },
  bindSwiperHeight: function (e) {
    var that = this;
    var winWid = wx.getSystemInfoSync().windowWidth; //获取当前屏幕的宽度
    var imgh = e.detail.height; //图片高度
    var imgw = e.detail.width;
    var swiperH = winWid * imgh / imgw + "px"
    //等比设置swiper的高度。  即 屏幕宽度 / swiper高度 = 图片宽度 / 图片高度    ==》swiper高度 = 屏幕宽度 * 图片高度 / 图片宽度
    that.setData({
      winWid: winWid,
      SwiperHeight: swiperH
    })
  },
  toDclMiniProgram(){
    let self = this;
    const ldWorkToken =self.data.userInfo.ldWorkToken
    // const name = self.data.userInfo.realname
    // const idNumber =self.data.userInfo.id_numbe
    // const phone = self.data.userInfo.mobilephone_number
    
    if (self.data.userInfo.isLogin) {
      if (self.data.userInfo.isAuth) {
        wx.navigateToMiniProgram({
          appId: 'wx84685556d92ab2a7',
          path: '/pages/biz/event/list_todo?ldWorkToken='+ldWorkToken,
          extraData: {
            foo: 'bar'
          },
          envVersion: 'release',
          success(res) {
            // 打开成功
      }
        })
      
      } else {
        // self.doAuth();
        wx.navigateTo({
          url: '/pages/new/userCheck/userCheck'
        })
      }
    } else {
      wx.showModal({
        title: '提示',
        content: '请先注册并登录',
        success(res) {
          if (res.confirm) {
            wx.redirectTo({
              url: '/pages/new/user/login'
            })
          } else if (res.cancel) {
            wx.showToast({
              title: '登录失败',
              icon: 'none',
              duration: 2000
            })
          }
        }
      })
    }


  },
  toCLZMiniProgram(){
    let self = this;
    const ldWorkToken =self.data.userInfo.ldWorkToken
    // const name = self.data.userInfo.realname
    // const idNumber =self.data.userInfo.id_numbe
    // const phone = self.data.userInfo.mobilephone_number

    if (self.data.userInfo.isLogin) {
      if (self.data.userInfo.isAuth) {
        wx.navigateToMiniProgram({
          appId: 'wx84685556d92ab2a7',
          path: '/pages/biz/event/list?status=-1'+'&ldWorkToken='+ldWorkToken,
          extraData: {
            foo: 'bar'
          },
          envVersion: 'release',
          success(res) {
            // 打开成功
      }
        })
      
      } else {
        // self.doAuth();
        wx.navigateTo({
          url: '/pages/new/userCheck/userCheck'
        })
      }
    } else {
      wx.showModal({
        title: '提示',
        content: '请先注册并登录',
        success(res) {
          if (res.confirm) {
            wx.redirectTo({
              url: '/pages/new/user/login'
            })
          } else if (res.cancel) {
            wx.showToast({
              title: '登录失败',
              icon: 'none',
              duration: 2000
            })
          }
        }
      })
    }
  },
  toSBMiniProgram(){
    let self = this;
    const ldWorkToken =self.data.userInfo.ldWorkToken
    // const name = self.data.userInfo.realname
    // const idNumber =self.data.userInfo.id_numbe
    // const phone = self.data.userInfo.mobilephone_number

    if (self.data.userInfo.isLogin) {
      if (self.data.userInfo.isAuth) {
        wx.navigateToMiniProgram({
          appId: 'wx84685556d92ab2a7',
          path: '/pages/biz/event/info?ldWorkToken='+ldWorkToken,
          extraData: {
            foo: 'bar'
          },
          envVersion: 'release',
          success(res) {
            // 打开成功
      }
        })
      
      } else {
        // self.doAuth();
        wx.navigateTo({
          url: '/pages/new/userCheck/userCheck'
        })
      }
    } else {
      wx.showModal({
        title: '提示',
        content: '请先注册并登录',
        success(res) {
          if (res.confirm) {
            wx.redirectTo({
              url: '/pages/new/user/login'
            })
          } else if (res.cancel) {
            wx.showToast({
              title: '登录失败',
              icon: 'none',
              duration: 2000
            })
          }
        }
      })
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    that.getLdWorkToken()
    user.getUserInfo(false).then((res) => {
      this.setData({
        userInfo: getApp().globalData.userInfo
      });
    });

    var timestamp = Date.parse(new Date());
    timestamp = timestamp / 1000;
    console.log("当前时间戳为：" + timestamp)
    this.setData({
      stamp:timestamp,
      utoken:encodeURIComponent(wx.getStorageSync('token')),

    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    var that = this;
    user.getUserInfo(false).then((res) => {
      
      that.setData({
        userInfo: getApp().globalData.userInfo
      });
      if (!that.data.userInfo.isLogin) {    
        // wx.showModal({
        //   title: '提示',
        //   content: '请先注册并登录',
        //   success(res) {
        //     if (res.confirm) {
        //       that.setData({ isClose:false}) 
        //       wx.redirectTo({
        //         url: '/pages/new/user/login'
        //       })
        //     } else if (res.cancel) {
        //       wx.showToast({
        //         title: '登录失败',
        //         icon: 'none',
        //         duration: 2000
        //       })
        //     }
        //   }
        // })
      }else{  
        // if (this.data.isClose) {
        //   this.setData({ isClose:false}) 
        //   let url ='https://alirobot.jxjkga.cn/';
        //    wx.navigateTo({
        //     url: url,
        //   })
        //  }else{
        //   this.setData({ isClose:true}) 
        //   wx.switchTab({
        //     url: '/pages/new/homeNew/homeNew',
        //    })
        //  }
      }
    }); 
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})