/* pages/index/index.wxss */
image{
  display: block;
}
.container{
  background: #fff;
  height: 100%;
}
.msg-setting{
  position: absolute;
  width: 100%;
  top: -15rpx;
  display: flex;
  flex-direction: row-reverse;
}
.msg-setting .msg{
  width: 40rpx;
  position: relative;
}
.msg-setting .msg .msg-num{
  position: absolute;
  display: block;
  width: 30rpx;
  height: 30rpx;
  top: -8rpx;
  right: -10rpx;
  color: #fff;
  background: #f41b25;
  font-size: 24rpx;
  border-radius: 50%;
  text-align: center;
}
.msg-setting .setting{
  width: 40rpx;
  margin-right: 30rpx;
}
.top{
  display: flex;
  flex-direction: row;
  width: 680rpx;
  margin: 0 auto;
  margin-top: 40rpx;
  justify-content: space-between;
  position: relative;
}
.top .weather-address{
  margin-top: 70rpx;
}
.top .weather-address .address text{
  font-size: 48rpx;
}
.top .weather-address .weather{
  margin-top: 10rpx;
}
.top .weather-address .weather text{
  font-size: 24rpx;
  margin-right: 6rpx;
}
.top .weather-address .weather text.bg{
  background: #defff8;
  color: #5fd7ba;
  border: 1px solid #e5f6f3;
  border-radius: 3px;
  padding: 0 10rpx;
}
.top .top-img{
  width: 480rpx;
}
.person-info{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  position: relative;
  width: 620rpx;
  margin: 0 auto;
  border: 1px solid #f3f3f3;
  border-radius: 3px;
  padding: 20rpx 30rpx;
  box-shadow: 3px 3px 3px 1px #f3f3f3;
}
.person-info .avatar-name{
  display: flex;
  flex-direction: row;
}
.person-info .avatar{
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
}
.person-info .name{
  line-height: 100rpx;
  margin-left: 30rpx;  
}
.person-info .status{
  line-height: 100rpx;
  color: #19b1f1;
}
.person-info .status .arrow{
  position: absolute;
  top: 60rpx;
  right: 60rpx;
}
.link-list{
  width: 680rpx;
  margin: 0 auto;
  margin-top: 40rpx;
  border-bottom: none;
}
.link-list .link-item{
  display: flex;
  flex-direction: row;
  padding: 30rpx 30rpx 20rpx 20rpx;
  border: 1px solid #e9e9e9;
  position: relative;
  border-radius: 5px;
}
.link-list .link-item .img{
  width: 60px;
  position: relative;
  top: 0rpx;
  margin-right: 20rpx;
}
.link-list .link-item .img image{
  width: 60px;
  height: 60px;
}
.link-list .link-item .title{
  color: #333;
  font-size: 36rpx;
  font-weight: bold;
}
.link-list .link-item .sub-title{
  color: #666;
  font-size: 32rpx;
  margin-top: 20rpx;
}
.link-list .link-item .arrow{
  position: absolute;
  top: 70rpx;
  right: 40rpx;
}



.notice-wrap{
  width: 680rpx;
  margin: 0 auto;
  background:#eee;
  padding:10rpx 70rpx 10rpx 0;
  display: flex;
  justify-content: space-between;
}
.ovh{
  overflow:hidden;
}
.font28{
  font-size:28rpx;
}
.relative{
  position:relative;
}
.notice{
  color:#333;
  width:100%;
  height:40rpx;
}
.marquee_text {
  white-space: nowrap;
  position: absolute;
  top: 0;
}
.close-icon{
  position:relative;
  left: 20px;
}
.icon40{
  width:40rpx;
  height:40rpx;
}   
.right{
  float:right;
}
.icon{
  display:inline-block;
  width:32rpx;
  height:32rpx;
  background-size:contain;
}