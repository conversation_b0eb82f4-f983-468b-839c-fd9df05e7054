// miniprogram/pages/user/login.js
var app = getApp();
var url = app.globalData.requestUrl;
Page({
  /**
   * 页面的初始数据
   */
  data: {
    canIUse: wx.canIUse('button.open-type.getPhoneNumber'),
    isAgree: false,
    isShowAgreeTips: false,
    getPhone: false,
    bindKeyPhone: '',
    intText: '获取验证码',
    color: '#1AAD19',
    intNum: 60,
    isSend: false,
    checkd: false,
    dsiabled: true,
    h5: ""
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    

    this.setData({
      h5: options.h5
    });

  },

  toPage: function () {
    let self = this;
    
    let url = decodeURIComponent(decodeURIComponent(self.data.h5));
    let url2 = '/pages/myWebview/myWebview?h5=';//decodeURIComponent();
    let token = wx.getStorageSync('token'); 
    if (url.indexOf("utoken" < 0)) { 
      let array = url.split("?"); 
      if(array.length>1){
        url = url2 + encodeURIComponent( url+'&utoken=' + token);
      }else{
        url = url2 + encodeURIComponent( url+'?utoken=' + token);
      }
    }
    wx.redirectTo({
      url: url,
    })
  },
  /*微信获取号码*/
  getPhoneNumber: function (e) {
    let self = this;
    if (self.data.isAgree == false) {
      self.setData({
        isShowAgreeTips: true
      })
      return true;
    }
    var app = getApp();
    var url = app.globalData.requestUrl;
    if (e.detail.iv) {
      wx.login({
        success: res => {
          console.log(res)
          let jscode = res.code
          let encryptedData = e.detail.encryptedData
          let iv = e.detail.iv
          wx.request({
            method: 'POST',
            url: url + '/storemgmt/base/user/login',
            data: {
              js_code: jscode,
              iv: iv,
              encrypted_data: encryptedData
            },
            header: {
              'content-type': 'application/x-www-form-urlencoded' // 默认值
            },
            success(res) {
               
              if (res.data.success == 1) {
                wx.setStorageSync('token', res.data.data.token);
                self.toPage();
              } else {
                wx.showToast({
                  title: res.data.dsc,
                  icon: 'none',
                  duration: 2000
                })
              }
            }
          })
        }
      })
    } else {
      wx.showToast({
        title: '获取信息失败',
        icon: 'none',
        duration: 2000
      })
    }
  },
  toIndex: function () {
    wx.redirectTo({
      url: '../index/index'
    })
  },
  bindAgreeChange: function (e) {
    this.setData({
      isAgree: !!e.detail.value.length
    });
  },
  bindCheckIsAgree: function (e) {
    var that = this;
    if (!that.isAgree) {
      this.setData({
        isShowAgreeTips: true
      });
    }
  },
  /* 返回上一页 */
  bindReturnBack() {
    wx.switchTab({
      url: '/pages/new/homeNew/homeNew',
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },


})