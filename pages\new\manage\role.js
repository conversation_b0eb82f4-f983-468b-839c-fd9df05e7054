// pages/new/manage/view.js
var app = getApp();
var url = app.globalData.requestUrl;
var user = require('../../../pages/new/utils/user.js')
var common = require('../../../pages/new/utils/common.js')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    user: {},
    url: url,
    roleShow: false,
    roleTree: [],
    roleList: []
  },
  getUserInfo: function(e) {
    var that = this;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/back/user/detail/byId?id=' + that.options.id,
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          var data = {
            user: that.data.user
          }
          data.user = res.data.data;
          that.setData(data);
          that.getRoleList();
        } else {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },
  removeByValue: function(arr, val) {
    for (var i = 0; i < arr.length; i++) {
      if (arr[i].id == val.id) {
        arr.splice(i, 1);
        break;
      }
    }
  },
  bindRoleShow: function(e) {
    var temp = false;
    if (e.currentTarget.dataset.status == "1") {
      temp = true;
    }
    this.setData({
      roleShow: temp
    });
  },
  bindRoleCancel: function(e) {
    this.bindSetRole(e.currentTarget.dataset.item, 0);
  },
  bindRoleClick: function(e) {
    this.bindSetRole(e.currentTarget.dataset.item, -1);
  },
  bindSetRole: function(item, checked) {
    if (item.code == '') {
      return;
    }
    var roleList = this.data.roleList;
    var roleTree = this.data.roleTree;
    for (let i = 0; i < roleList.length; i++) {
      if (roleList[i].id == item.id) {
        if (checked == -1) {
          if (roleList[i].checked == false) {
            roleList[i].checked = true;
          } else {
            roleList[i].checked = false;
          }
        } else if (checked == 1) {
          roleList[i].checked = true;
        } else if (checked == 0) {
          roleList[i].checked = false;
        } else {}
        break;
      }
    }
    roleTree = common.toTree(roleList);
    this.setData({
      roleList: roleList,
      roleTree: roleTree
    });
  },
  getRoleList: function(e) {
    var that = this;
    var currentUserRoles = that.data.user.roles == null ? [] : that.data.user.roles.split(',');
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/back/role/list',
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          var roleList = that.data.roleList;
          var roleTree = that.data.roleTree;
          for (let i = 0; i < res.data.data.length; i++) {
            var code = '';
            if (res.data.data[i].code != null && res.data.data[i].code != '') {
              code = res.data.data[i].code;
            }
            let checked = false;
            if (currentUserRoles.indexOf(code) != -1) {
              checked = true;
            }
            roleList.push({
              id: res.data.data[i].id,
              parent_id: res.data.data[i].parent_id,
              name: res.data.data[i].name,
              code: code,
              status: res.data.data[i].status,
              checked: checked
            });
          }
          roleTree = common.toTree(roleList);
          that.setData({
            roleList: roleList,
            roleTree: roleTree
          });
        } else {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },
  bindSave: function(e) {
    var that = this;
    var role = [];
    for (let i = 0; i < that.data.roleList.length; i++) {
      if (that.data.roleList[i].checked) {
        role.push(that.data.roleList[i].code);
      }
    }
    role = role.join(',');
    wx.request({
      method: 'POST',
      url: url + '/storemgmt/back/roles/grant?user_id=' + that.options.id + '&roles=' + role,
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          wx.showModal({
            title: '提示',
            content: '保存成功',
            showCancel: false,
            success(res) {
              if (res.confirm) {
                wx.navigateTo({
                  url: '/pages/new/manage/list',
                })
              }
            }
          })
        } else {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.getUserInfo();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

 
})