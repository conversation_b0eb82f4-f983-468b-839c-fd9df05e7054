// miniprogram/pages/user/login.js
var app = getApp();
var url = app.globalData.requestUrl;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    canIUse: wx.canIUse('button.open-type.getPhoneNumber'),
    isAgree: false,
    isShowAgreeTips: false,
    getPhone: false,
    bindKeyPhone: '',
    intText: '获取验证码',
    color: '#1AAD19',
    intNum: 60,
    isSend: false,
    checkd: false,
    dsiabled: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if (options._info != '') {
      wx.showToast({
        title: options._info,
        icon: 'none',
        duration: 2000
      })
    }
  },
  /*微信获取号码*/
  getPhoneNumber: function (e) {
    let self = this;
    if (self.data.isAgree == false) {
      self.setData({
        isShowAgreeTips: true
      })
      return true;
    }
    debugger
    var app = getApp();
    var wxlog = wx.login();
    console.log("wxlog", wxlog)

    var url = app.globalData.requestUrl;
    if (e.detail.iv) {
      wx.login({
        success: res => {
          console.log("res111", res)
          let jscode = res.code
          let encryptedData = e.detail.encryptedData
          let iv = e.detail.iv
          wx.request({
            method: 'POST',
            url: url + '/storemgmt/base/user/login',
            data: {
              js_code: jscode,
              iv: iv,
              encrypted_data: encryptedData
            },
            header: {
              'content-type': 'application/x-www-form-urlencoded' // 默认值
            },
            success(res) {
              if (res.data.success == 1) {
                wx.setStorageSync('token', res.data.data.token);
                let soucePage = wx.getStorageSync('soucePage');

                if (soucePage == 'expandWebView4') {
                  wx.redirectTo({
                    url: '/pages/expandWebView4/expandWebView4'
                  })
                } else if (soucePage == 'lkzzbs') {
                  wx.redirectTo({
                    url: '/pages/new/lkzzbs/lkzzbs?utoken='+ res.data.data.token
                  })
                } else if (soucePage == 'visit') {
                  wx.redirectTo({
                    url: '/pages/building/visit'
                  })
                }else if (soucePage == 'szzth') {
                  wx.redirectTo({
                    url: '/pages/new/szzth/szzth'
                  })
                } else if (soucePage == 'yqfwd') {
                  wx.redirectTo({
                    url: '/pages/new/yqfwd/yqfwd'
                  })
                }
                 else {
                  wx.switchTab({
                    url: '/pages/new/mine/mine'
                  })
                }
              } else {
                wx.showToast({
                  title: res.data.dsc,
                  icon: 'none',
                  duration: 2000
                })
              }
            },
            fail(res) {
              wx.showToast({
                title: res.data.dsc,
                icon: 'none',
                duration: 2000
              })
            }
          })
        },
        fail: (res) => {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      })
    } else {
      wx.showToast({
        title: '获取信息失败',
        icon: 'none',
        duration: 2000
      })
    }
  },
  toIndex: function () {
    wx.redirectTo({
      url: '../index/index'
    })
  },
  bindAgreeChange: function (e) {
    this.setData({
      isAgree: !!e.detail.value.length
    });
  },
  bindCheckIsAgree: function (e) {
    var that = this;
    if (!that.isAgree) {
      this.setData({
        isShowAgreeTips: true
      });
    }
  },
  /* 返回上一页 */
  bindReturnBack() {
    wx.switchTab({
      url: '/pages/new/homeNew/homeNew',
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },


})