<van-cell
  center
  title="{{ title }}"
  label="{{ label }}"
  border="{{ border }}"
  icon="{{ icon }}"
  custom-class="van-switch-cell"
  use-label-slot="{{ useLabelSlot }}"
>
  <slot slot="icon" name="icon" />
  <slot slot="title" name="title" />
  <slot slot="label" name="label" />
  <van-switch
    size="{{ size }}"
    checked="{{ checked }}"
    loading="{{ loading }}"
    disabled="{{ disabled }}"
    active-color="{{ activeColor }}"
    inactive-color="{{ inactiveColor }}"
    active-value="{{ activeValue }}"
    inactive-value="{{ inactiveValue }}"
    custom-class="van-switch-cell__switch"
    bind:change="onChange"
  />
</van-cell>
