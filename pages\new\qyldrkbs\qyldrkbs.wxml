<!--pages/new/zkdj/zkdj.wxml-->
<!--pages/building/visit.wxml-->
<view class="container" style="background:#fff;">
  <watermark></watermark>
  <view class="title">企业流动人口自主申报</view>
  <view>
    <form catchsubmit="formSubmit">
      <view><span class="red">*</span>1.公司名称</view>
      <view>
        <input name="company_name" value="{{company_name}}" disabled="true" class="ipt" placeholder="" />
      </view>
      <view><span class="red">*</span>2.公司所在社区</view>
      <view>
        <input name="area_location_name" value="{{area_location_name}}"  disabled="true" class="ipt" placeholder="" />
      </view>
      <view><span class="red">*</span>3.公司详细地址</view>
      <view>
        <input name="address" value="{{detail_address}}"  disabled="true" class="ipt" placeholder="" />
      </view>
      <view><span class="red">*</span>4.请选择入职日期</view>
      <view class="section">
        <picker mode="date" value="{{rent_unrent_time}}" name="rent_unrent_time"  bindchange="bindDateChange">
          <view class="picker">
            当前选择: {{rent_unrent_time}}
          </view>
        </picker>
      </view>
      <view><span class="red">*</span>5.姓名：</view>
      <view>
        <input name="name" value="{{name}}" bindinput="getname" class="ipt" placeholder="" />
      </view>
      <view><span class="red">*</span>6.证件号码</view>
      <view>
        <input name="identity" value="{{identity}}" bindinput="getidentity" class="ipt" type="idcard" placeholder="" />
      </view>
      <view><span class="red">*</span>7.手机号码：</view>
      <view>
        <input name="phone" value="{{phone}}" bindinput="getphone" class="ipt" type="number" placeholder="" />
      </view>
      <view><span class="red">*</span>8.请上传本人头像：(参考一寸证件照拍摄)</view>
      <view class='load-img'>
        <view class='load-box'>
          <view class='img-item' wx:for="{{face_img_file}}" wx:key="index">
            <image src="{{item.path}}" data-src="{{item}}" mode="aspectFill" data-list="{{face_img_file}}"></image>
            <icon class='icon' type="clear" size="20" color='#EF4444' catchtap='_onDelFace' data-idx="{{index}}" wx:if="{{!prevent}}" />
          </view>
          <image class='img-add' bindtap='getface' wx:if="{{face_img_file.length<1}}"></image>
        </view>
      </view>
      <view><span class="red">*</span>9.请上传身份证照片(请上传有头像的身份证照片)</view>
      <view class='load-img'>
        <view class='load-box'>
          <view class='img-item' wx:for="{{rent_contract_img_file}}" wx:key="index">
            <image src="{{item.path}}" data-src="{{item}}" mode="aspectFill" data-list="{{rent_contract_img_file}}"></image>
            <icon class='icon' type="clear" size="20" color='#EF4444' catchtap='_onDelTab' data-idx="{{index}}" wx:if="{{!prevent}}" />
          </view>
          <image class='img-add' bindtap='getrent' wx:if="{{rent_contract_img_file.length<1}}"></image>
        </view>
      </view>


     


      <view class="btn-area">
        <button style="margin: 30rpx 0;background:#0081ff;color:#fff;" formType="submit" disabled="{{disabled}}">提交</button>
      </view>
      <br/> 
      <br/> 
    </form>
  </view>
</view>