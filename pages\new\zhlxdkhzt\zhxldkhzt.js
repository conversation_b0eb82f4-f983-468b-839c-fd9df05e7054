// pages/new/zhlxdkhzt/zhxldkhzt.js
const http = require("../../new/utils/httpUtils.js");
Page({

  /**
   * 页面的初始数据
   */
  data: {
    latitude: 30.74744,
    longitude: 120.78483,
    show_dk: true,
    scale: 15,
    markers: [{
        id: 1,
        latitude: 30.755194,
        longitude: 120.762703,
        iconPath: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/qidian.png',
        // callout: {
        //   content: '文本内容',
        //   color: '#ff0000',
        //   fontSize: 14,
        //   borderWidth: 2,
        //   borderRadius: 10,
        //   borderColor: '#000000',
        //   bgColor: '#fff',
        //   padding: 5,
        //   display: 'ALWAYS',
        //   textAlign: 'center'
        // }
      },
      {
        id: 2,
        latitude: 30.758120,
        longitude: 120.762703,
        iconPath: 'https://zzsbapi.jxjkga.cn/wxzzsb/new/zhongdian.png',
      },

    ],
    polyline: [{
      points: [{
          latitude: 30.74744,
          longitude: 120.78483
        },
        {
          latitude: 23.098994,
          longitude: 113.323520,
        },
        {
          latitude: 23.098994,
          longitude: 113.325520,
        }
      ], //坐标点集合
      color: "#0068FF", //线条的颜色
      width: 5, //线条的宽度
    }],
    RecordTrackList: [],
    marker: {},
    parentoptions: {},
    patrol_record_id: '',
  },

  bindmarkertap(e) {
    var that = this;
    console.log(e)
    return
    let marker = this.data.markers.find(item => {
      return item.id == e.detail.markerId
    });
    this.setData({
      mapDetail: marker,
      viewShow: true
    })
  },
  generatePolyline(data) { //创建线条
    let polyline = {}
    let points = []
    for (let ite of data) {
      points.push({
        longitude: ite.longitude,
        latitude: ite.latitude
      })
    }
    this.data.polyline = [{
      strokeWidth: 5,
      strokeColor: '#0068FF',
      points: points, //坐标点集合
    }]
    this.setData({
      polyline: [{
        points: points, //坐标点集合
        color: "#0068FF", //线条的颜色
        width: 5, //线条的宽度
        borderWidth: 2,
        // colorList:[],             //彩虹线，使用时会忽略color的值
        // dottedLine:true,          //是否虚线
        arrowLine: true, //是否有箭头
        //arrowIconPath:'',         //箭头更换时的图标地址，arrline为true时生效
        //borderColor:'blue',       //线条边框的颜色
        //borderWidth:3,            //线条边框的宽度
        // abovelabels:'abovelabels'   //压盖层级：('aboveroads':道路之上楼块之下，'abovebuildings':楼块之上POI之下，'abovelabels':所有的POI之上，默认是这个)    
      }]
    })
    let markersdata = this.data.markers
    let num = data.length
    console.log(num,'num');
    markersdata[0].latitude = data[0].latitude
    markersdata[0].longitude = data[0].longitude
    markersdata[1].latitude = data[num-1].latitude
    markersdata[1].longitude = data[num-1].longitude
    console.log(markersdata,'markersdata');
    this.setData({
      markers:markersdata
    })
  },

  // 获取巡逻任务下的巡逻日志的轨迹
  // 地址：host+company/getManagerPatrolRecordTrackList
  getManagerPatrolRecordTrackList() {
    let that = this
    http.get('company/getManagerPatrolRecordTrackList', {
      patrol_record_detail_id: that.data.patrol_record_id
    }, res => {
      if (res.data.success == 1) {
        that.data.RecordTrackList = res.data.data
        console.log(that.data.RecordTrackList, '  this.data.RecordTrackList');
        that.generatePolyline(that.data.RecordTrackList)
      }
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.data.parentoptions = JSON.parse(options.Record)
    this.setData({
      parentoptions: JSON.parse(options.Record)
    })
    console.log(this.data.parentoptions, ' this.data.parentoptions');
    this.data.patrol_record_id = this.data.parentoptions.id
    this.getManagerPatrolRecordTrackList()
    // this.imgUrl_Get()
  },


  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})