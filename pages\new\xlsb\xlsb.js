// pages/new/xlsb/xlsb.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    examine_power:0,
    led_power:0,

  },
  gotoxl:function(e){
    wx.navigateTo({
      url: '../xlmap/xlmap'
    })
  },
  zhxlClick:function(e){
    wx.navigateTo({
      url: '../../zhxldk/zhxldk'
    })
  },
  xlrzClick:function(e){
    wx.navigateTo({
      url: '../../xldkrz/xldkrz'
    })
  },
  getpower(){
    let that = this
    wx.request({
      method: 'get',
      url: 'https://zzsbapi.jxjkga.cn/storemgmt/company/getCompanyPower',
      header: {
        token: wx.getStorageSync('token'),
      },
      success(res) {
        if (res.data.success == 1) {
          that.setData({
            examine_power:res.data.data.examine_power,
            led_power:res.data.data.led_power
          })
        } 
      },
      fail(res) {
        console.log(res)
      }
    })
    
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
   this.getpower()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})