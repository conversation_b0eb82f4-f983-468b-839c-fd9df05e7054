/* miniprogram/pages/order/order.wxss */
@import "/css/colorui/main.wxss";
@import "/css/colorui/animation.wxss";
@import "/css/colorui/icon.wxss";

page {
  width: 100%;
  height: 100%;
}

.product-sku {
  color: rgba(0, 0, 0, 0.3);
}

.product-present-price {
  color: rgba(0, 0, 0, 0.6);
}


.product-count {
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
}

.product-status {
  color: #fd6500;
  float: right;
}
.check-btn-status1 {
  width: 50%;
  float: left;
  text-align: center;
  line-height: 80rpx;
  height: 80rpx;
  color: #fff;
  background-color: #f60;
}

.check-btn-status-2 {
  width: 50%;
  float: left;
  text-align: center;
  line-height: 80rpx;
  height: 80rpx;
  color: #fff;
  background-color: #11d111;
}
.corner-tag {
  width: 30px;
  height: 0;
  border-width: 0 15px 15px 15px;
  border-style: none solid solid;
  border-color: transparent transparent #187de8;
  transform: rotate(-45deg);
  position: absolute;
  left: -20px;
  top: 8px;
  opacity: 0.8;
}

.corner-tag-text {
  color: #fff;
  font-size: 10px;
  width: 35px;
  text-align: center;
  display: inline-block;
  position: absolute;
  top: 0px;
}

.search-tag {
  background-color: #07c160;
  color: #fff;
  padding: 1px 3px;
  display: inline-block;
  border-radius: 2px;
  margin-right: 15px;
  margin-bottom: 5px;
  margin-top: 5px;
  position: relative;
}

.search-more .weui-cell__hd {
  margin-bottom: 6px;
  margin-top: 6px;
}

.search-tag .search-tag-close {
  position: absolute;
  right: -6px;
  top: -6px;
  color:#ff0000;
  font-size:10px;
  content: "\e687";
}

.weui-cell {
  padding: 10px;
}

.weui-check__hd_in-checkbox {
  padding-right: 10px;
}

.weui-cell__bd {
  text-align: left;
}

.arrow-up {
  transform: rotate(180deg);
}

.arrow-down {
  transform: rotate(0deg);
}

.cu-item {
  background-color: #f7f7f7 !important;
}

.cu-dialog {
  scroll-y: scroll;
}
