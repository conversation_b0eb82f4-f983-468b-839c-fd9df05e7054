// pages/new/manage/view.js
var app = getApp();
var url = app.globalData.requestUrl;
var user = require('../../../pages/new/utils/user.js')
var common = require('../../../pages/new/utils/common.js')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    user: {},
    url: url,
    functionShow: false,
    functionTree: [],
    functionList: []
  },
  getUserInfo: function (e) {
    var that = this;
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/back/user/detail/byId?id=' + that.options.id,
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          var data = {
            user: that.data.user
          }
          data.user = res.data.data;
          that.setData(data);
          that.getFunctionList();
        } else {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },
  removeByValue: function (arr, val) {
    for (var i = 0; i < arr.length; i++) {
      if (arr[i].id == val.id) {
        arr.splice(i, 1);
        break;
      }
    }
  },
  bindFunctionShow: function (e) {
    var temp = false;
    if (e.currentTarget.dataset.status == "1") {
      temp = true;
    }
    this.setData({
      functionShow: temp
    });
  },
  bindFunctionCancel: function (e) {
    this.bindSetFunction(e.currentTarget.dataset.item, 0);
  },
  bindFunctionClick: function (e) {
    this.bindSetFunction(e.currentTarget.dataset.item, -1);
  },
  bindSetFunction: function (item, checked) {
    if (item.code == '') {
      return;
    }
    var functionList = this.data.functionList;
    var functionTree = this.data.functionTree;
    for (let i = 0; i < functionList.length; i++) {
      if (functionList[i].id == item.id) {
        if (checked == -1) {
          if (functionList[i].checked == false) {
            functionList[i].checked = true;
          } else {
            functionList[i].checked = false;
          }
        } else if (checked == 1) {
          functionList[i].checked = true;
        } else if (checked == 0) {
          functionList[i].checked = false;
        } else { }
        break;
      }
    }
    functionTree = common.toTree(functionList);
    this.setData({
      functionList: functionList,
      functionTree: functionTree
    });
  },
  getFunctionList: function (e) {
    var that = this;
    var currentUserFunctions = that.data.user.functions == null ? [] : that.data.user.functions.split(',');
    wx.request({
      method: 'GET',
      url: url + '/storemgmt/back/function/list',
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          var functionList = that.data.functionList;
          var functionTree = that.data.functionTree;
          for (let i = 0; i < res.data.data.length; i++) {
            let code = '';
            if (res.data.data[i].code != null && res.data.data[i].code != '') {
              code = res.data.data[i].code;
            }
            let checked = false;
            if (currentUserFunctions.indexOf(code) != -1) {
              checked = true;
            }
            functionList.push({
              id: res.data.data[i].id,
              parent_id: res.data.data[i].parent_id,
              name: res.data.data[i].name,
              code: code,
              status: res.data.data[i].status,
              checked: checked
            });
          }
          functionTree = common.toTree(functionList);
          that.setData({
            functionList: functionList,
            functionTree: functionTree
          });
        } else {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },
  bindSave: function (e) {
    var that = this;
    var functions = [];
    for (let i = 0; i < that.data.functionList.length; i++) {
      if (that.data.functionList[i].checked) {
        functions.push(that.data.functionList[i].code);
      }
    }
    functions = functions.join(',');
    wx.request({
      method: 'POST',
      url: url + '/storemgmt/back/functions/grant?user_id=' + that.options.id + '&functions=' + functions,
      header: {
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {
          wx.showModal({
            title: '提示',
            content: '保存成功',
            showCancel: false,
            success(res) {
              if (res.confirm) {
                wx.navigateTo({
                  url: '/pages/new/manage/list',
                })
              }
            }
          })
        } else {
          wx.showToast({
            title: res.data.dsc,
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.getUserInfo();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

 
})