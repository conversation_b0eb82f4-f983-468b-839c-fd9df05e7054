// pages/new/zkdj/zkdj.js
const http = require("../utils/httpUtils.js");
const FormData = require("../utils/formData.js");
var user = require('../utils/user.js')
var app = getApp();
var url = app.globalData.requestUrl;
var columnsArr = app.globalData.columnsArr;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    face_file: "",
    user_name: "",
    user_identity: "",
    user_phone: "",
    industry_name: "",
    industry_id: "",
    company_name: "",
    companyList: ["娱乐场所", "棋牌室", "足浴店", '网约房', "其他场所"],
    companyIndex: 0,
    disabled: false,
    identitytype: false,
    longitude: '',
    latitude: '',
    face_file_name: '',
    communityList: [],
    communityIndex: '',
    address_detail: '',
    live_date: '',
    leave_date: ''
  },
  formSubmit: function (e) {
    console.log(e.detail.value)
    let that = this;
    if (e.detail.value.user_name == '') {
      wx.showToast({
        title: "请填写姓名",
        icon: "none",
      });
      return false;
    }
    if (e.detail.value.user_identity == '') {
      wx.showToast({
        title: "请填写身份证号",
        icon: "none",
      });
      return false;
    }
    if (!this.data.identitytype) {
      wx.showToast({
        title: "请填写正确的身份证号",
        icon: "none",
      });
      return false;
    }
    if (e.detail.value.user_phone == "") {
      wx.showToast({
        title: "请输入联系电话",
        icon: "none",
      });
      return false;
    }
    if (e.detail.value.address_detail == "") {
      wx.showToast({
        title: "请输入详细地址",
        icon: "none",
      });
      return false;
    }
    if (e.detail.value.address_detail == "") {
      wx.showToast({
        title: "请选择入住时间",
        icon: "none",
      });
      return false;
    }
    if (e.detail.value.address_detail == "") {
      wx.showToast({
        title: "请选择离开时间",
        icon: "none",
      });
      return false;
    }
    // if (e.detail.value.industry_name == "") {
    //   wx.showToast({
    //     title: "请选择行业类型",
    //     icon: "none",
    //   });
    //   return false;
    // }
    // if (e.detail.value.company_name == "") {
    //   wx.showToast({
    //     title: "请输入场所名称",
    //     icon: "none",
    //   });
    //   return false;
    // }
    if (that.data.face_img_file == '') {
      wx.showToast({
        title: "请上传头像",
        icon: "none",
      });
      return false;
    }


    // 验证电话格式
    if (!/^(\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}$/.test(e.detail.value.user_phone)) {
      wx.showToast({
        title: "手机号码有误",
        duration: 2000,
        icon: "none",
      });

      return false;
    }
    that.setData({
      disabled: true
    })

    let formData = new FormData();
    // formData.append('industry_name', e.detail.value.industry_name)
    // formData.append("company_name", e.detail.value.company_name);
    formData.append('user_identity', e.detail.value.user_identity)
    formData.append('user_name', e.detail.value.user_name)
    formData.append('user_phone', e.detail.value.user_phone)
    formData.appendFile('face_file', that.data.face_file[0].path, that.data.face_file_name)
    // formData.append('industry_id', that.data.industry_id)
    formData.append('latitude', that.data.latitude)
    formData.append('longitude', that.data.longitude)

    formData.append('address_detail ', that.data.address_detail)
    formData.append('live_date ', that.data.live_date)
    formData.append('leave_date ', that.data.leave_date)
    formData.append('community_id ', that.data.communityList[that.data.communityIndex].id)
    formData.append('community_name ', that.data.communityList[that.data.communityIndex].name)


    formData.append('area_location_id', '330499001008')



    let datas = formData.getData();
    wx.request({
      method: 'post',
      url: 'https://zzsbapi.jxjkga.cn/storemgmt/base/addChengnanUserRegister',
      header: {
        token: wx.getStorageSync('token'),
        'content-type': datas.contentType,
      },
      data: datas.buffer,
      success(res) {
        if (res.data.success == 1) {

          wx.showToast({
            title: '提交成功',
            icon: 'success',
            duration: 2000,
            success: function () {
              setTimeout(() => {
                wx.switchTab({
                  url: '../homeNew/homeNew'
                })
              }, 1000);
            }
          })
        } else {
          that.setData({
            disabled: false
          })
          wx.showToast({
            title: '提交失败',
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail(res) {
        console.log(res)
      }
    })
  },
  getface: function () {
    let _this = this;
    wx.chooseImage({
      count: 1,
      success(res) {
        console.log(res);
        //此处会返回图片暂存路径和文件大小
        const data = res.tempFiles;
        _this.setData({
          face_file: data,
          face_file_name: res.tempFiles[0].path.split('/').pop()
        })

      }
    })
  },

  getphone: function (e) {
    this.setData({
      user_phone: e.detail.value
    })
  },

  getdetail_address: function (e) {
    this.setData({
      detail_address: e.detail.value
    })
  },
  getdetail_bl1: function (e) {
    this.setData({
      bl1: e.detail.value
    })
  },


  getidentity: function (e) {
    this.setData({
      user_identity: e.detail.value
    })
    this.verifyIdentity_PD(e.detail.value)
  },
  getname: function (e) {
    this.setData({
      user_name: e.detail.value
    })
  },
  getcompany: function (e) {
    this.setData({
      company_name: e.detail.value
    })
  },

  companyChange: function (e) {
    console.log(+e.detail.value + 1);
    this.setData({
      industry_id: +e.detail.value + 1,
      industry_name: this.data.companyList[+e.detail.value]
    })
  },
  // ckldClick 查看流动人口跳转页面
  ckldClick() {
    // 页面跳转 ---
    wx.switchTab({
      url: '/pages/new/homeNew/homeNew'
    })
  },

  _onDelFace(e) {
    // 获取图片索引  获取图片文件流
    let idx = e.currentTarget.dataset.idx;
    let delFile = this.data.face_file[idx];
    console.log(delFile);
    this.data.face_file.splice(idx, 1);
    this.setData({
      face_file: this.data.face_file
    })
  },
  // 校验身份证
  verifyIdentity_PD(val) {
    var that = this;
    if (val.length == 18) {
      let param = new FormData() // 创建form对象
      param.append('identity', val) // 通过append向form对象添加数据
      let datas = param.getData();
      wx.request({
        method: 'post',
        url: 'https://zzsbapi.jxjkga.cn/storemgmt/base/verifyIdentity',
        header: {
          token: wx.getStorageSync('token'),
          'content-type': datas.contentType
        },
        data: datas.buffer,
        success(res) {
          if (res.data.success == 1) {
            wx.showToast({
                title: '身份证格式正确',
                icon: 'success',
                duration: 2000
              }),
              that.setData({
                identitytype: true
              });
          } else {
            wx.showToast({
                title: '身份证格式错误',
                icon: 'none',
                duration: 2000
              }),
              that.setData({
                identitytype: false
              });
          }
        },
        fail(res) {
          console.log(res)
        }
      })
    } else {
      wx.showToast({
          title: '身份证格式错误',
          icon: 'none',
          duration: 2000
        }),
        that.setData({
          identitytype: false
        });
    }
  },
  communityChange(e) {
    this.setData({
      communityIndex: e.detail.value
    })
  },
  getaddress_detail(e) {
    this.setData({
      address_detail: e.detail.value
    })
  },
  live_dateChange(e) {
    this.setData({
      live_date: e.detail.value
    })
  },
  leave_dateChange(e) {
    this.setData({
      leave_date: e.detail.value
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (query) {},
  // 用户隐私信息认证代码
  alertSix() {
    console.log('没有获取到SixTip进行路由跳转');
    wx.redirectTo({
      url: '/pages/Bullet/index/index'
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    let than = this
    wx.getLocation({
      type: 'gcj02',
      success(res) {
        than.setData({
          longitude: res.longitude,
          latitude: res.latitude
        })
      }
    })
    wx.request({
      method: 'get',
      url: 'https://zzsbapi.jxjkga.cn/storemgmt/base/area/list?parent_id=330499001',
      header: {
        token: wx.getStorageSync('token'),
      },
      success(res) {
        if (res.data.success == 1) {
          than.setData({
            communityList: res.data.data.slice(0)
          })
        }
      }
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },


})