/* pages/wyfsb/wyfbzdj.wxss */

.container {
  padding: 20px;
  padding-bottom: 100px;
}

.pt10 {
  padding-top: 10px;
}

.pt15 {
  padding-top: 15px;
}

.pt20 {
  padding-top: 20px;
}

.pb10 {
  padding-bottom: 10px;
}

.fz15 {
  font-size: 15px;
}

.fw {
  font-weight: bold;
}

.red {
  color: red;
}

/* 头像相关样式 */
.avatar-image {
  width: 100px;
  height: 100px;
  border-radius: 50px;
  display: block;
  margin: 0 auto;
}

.upload-section {
  text-align: center;
}

.uploader-wrapper {
  display: inline-block;
  position: relative;
}

.upload-placeholder {
  width: 100px;
  height: 100px;
  border: 2px dashed #ddd;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
}

.upload-text {
  color: #999;
  font-size: 12px;
}

.uploaded-image {
  position: relative;
  display: inline-block;
}

.uploaded-img {
  width: 100px;
  height: 100px;
  border-radius: 10px;
}

.delete-btn {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  background: red;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 表单样式 */
.form-group {
  margin-top: 20px;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}

.form-label {
  width: 100px;
  font-weight: bold;
  color: #333;
  flex-shrink: 0;
}

.form-input {
  flex: 1;
  padding: 10px;
  border: none;
  background: transparent;
  font-size: 14px;
}

.picker-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
}

.picker-text {
  color: #333;
  font-size: 14px;
}

.picker-arrow {
  color: #999;
  font-size: 12px;
}

/* 分割线 */
.divider {
  height: 1px;
  background: #eee;
  margin: 20px 0;
}

/* 底部按钮 */
.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 1px solid #eee;
  padding: 10px 20px;
  padding-bottom: calc(10px + env(safe-area-inset-bottom));
}

.button-group {
  display: flex;
  gap: 15px;
}

.submit-btn, .cancel-btn {
  flex: 1;
  height: 45px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  border: none;
}

.submit-btn {
  background: #3beb61;
  color: white;
}

.cancel-btn {
  background: #ff4444;
  color: white;
}

.btn-icon {
  margin-right: 5px;
  font-size: 18px;
}

/* 选择器弹窗样式 */
.picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.picker-popup {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 20px 20px 0 0;
  z-index: 1001;
  max-height: 50vh;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.picker-cancel, .picker-confirm {
  color: #5062FF;
  font-size: 16px;
}

.picker-title {
  font-weight: bold;
  font-size: 16px;
}

.picker-view {
  height: 200px;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  font-size: 16px;
}