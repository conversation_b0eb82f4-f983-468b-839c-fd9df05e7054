<!--pages/new/shop/info.wxml-->
<watermark></watermark>
<view class="page">
	<view style="position:fixed;top:0px;width:100%;z-index: 99;" class="weui-flex">
		<view class="weui-flex__item">
			<view class="placeholder">
				<view class="weui-search-bar">
					<view class="weui-search-bar__form">
						<view class="weui-search-bar__box">
							<icon class="weui-icon-search_in-box" type="search" size="14"></icon>
							<input type="text" class="weui-search-bar__input" placeholder="手机号" value="{{inputVal}}" focus="{{inputShowed}}" bindinput="inputTyping" bindconfirm="getNewUserList" />
							<view class="weui-icon-clear" wx:if="{{inputVal.length > 0}}" bindtap="clearInput">
								<icon type="clear" size="14"></icon>
							</view>
						</view>
						<label class="weui-search-bar__label" hidden="{{inputShowed}}" bindtap="showInput">
							<icon class="weui-icon-search" type="search" size="14"></icon>
							<view class="weui-search-bar__text">模糊搜索</view>
						</label>
					</view>
					<view class="weui-search-bar__cancel-btn" hidden="{{!inputShowed}}" bindtap="hideInput">取消</view>
				</view>
			</view>
		</view>
		<view>
			<view class="placeholder" bindtap="bindSearchMore" style="padding:8px; line-height:32px;background-color:#f2f2f2;">高级</view>
		</view>
	</view>
	<view style="position:fixed;top:48px;width:100%;z-index: 99;background-color: #f2f2f2"  class="page__bd search-more" wx:if="{{searchMore}}">
		<view class="weui-cell weui-cell_access">
			<view class="weui-cell__hd" data-status='1' bindtap="bindDataShopShow">
				店铺数据：
			</view>
			<view class="weui-cell__bd">
				<block wx:for="{{dataShopList}}" wx:for-item="item" wx:key="id">
					<view class="search-tag" wx:if="{{item.checked}}" bindtap="bindDataShopCancel" data-item="{{item}}">
						<label class="search-tag-close iconfont icon-fail"></label>{{item.name}}</view>
				</block>
			</view>
		</view>
		<view class="weui-cell weui-cell_access">
			<view class="weui-cell__hd" data-status='1' bindtap="bindDataHouseShow">
				小区数据：
			</view>
			<view class="weui-cell__bd">
				<block wx:for="{{dataHouseList}}" wx:for-item="item" wx:key="id">
					<view class="search-tag" wx:if="{{item.checked}}" bindtap="bindDataHouseCancel" data-item="{{item}}">
						<label class="search-tag-close iconfont icon-fail"></label>{{item.name}}</view>
				</block>
			</view>
		</view>
		<view class="weui-cell weui-cell_access">
			<view class="weui-cell__hd" data-status='1' bindtap="bindRoleShow">
				拥有角色：
			</view>
			<view class="weui-cell__bd">
				<block wx:for="{{roleList}}" wx:for-item="item" wx:key="id">
					<view class="search-tag" wx:if="{{item.checked}}" bindtap="bindRoleCancel" data-item="{{item}}">
						<label class="search-tag-close iconfont icon-fail"></label>{{item.name}}</view>
				</block>
			</view>
		</view>
		<view class="weui-cell weui-cell_access">
			<view class="weui-cell__hd" data-status='1' bindtap="bindFunctionShow">
				功能权限：
			</view>
			<view class="weui-cell__bd">
				<block wx:for="{{functionList}}" wx:for-item="item" wx:key="id">
					<view class="search-tag" wx:if="{{item.checked}}" bindtap="bindFunctionCancel" data-item="{{item}}">
						<label class="search-tag-close iconfont icon-fail"></label>{{item.name}}</view>
				</block>
			</view>
		</view>
		<view   class="check-btn-box">
	<view class="check-btn-line"> 
		<view bindtap="bindSearchMore"   class="check-btn-status1">
			<label>取消</label>
		</view>
		<view bindtap="getNewUserList"  class="check-btn-status-2">
			<label>查询</label>
		</view>
			</view>
	</view>
	</view>
   <view style="height:48px">
   </view>
	<view   class="page__bd">
		<scroll-view scroll-y="true" style="height:{{scrollHeight}}px" bindscrolltolower="scrollChange">
    <view>
			<view class="weui-panel weui-panel_access myweui-row">
				<view class="weui-panel__bd myweui-row-body">
					<block wx:for="{{user}}" wx:for-item="item" wx:key="id">
						<view bindtap="bindOp" data-item="{{item}}" class="weui-media-box weui-media-box_appmsg" hover-class="weui-cell_active">
							<view class="corner-tag" style="display:none;">
								<label class="corner-tag-text">{{item.house_type_name}}</label>
							</view>
							<view class="weui-media-box__hd weui-media-box__hd_in-appmsg">
								<image class="weui-media-box__thumb" src="{{url}}{{item.best_frame_url}}" />
							</view>
							<view class="weui-media-box__bd weui-media-box__bd_in-appmsg">
								<view class="weui-media-box__title">{{item.name}} {{item.phone}}
								</view>
								<view class="weui-media-box__desc">
									<label class='product-sku'>{{item.address}} {{item.owner_mobilephone_number}}</label>
								</view>
							</view>
							<view class="weui-cell__ft weui-cell__ft_in-access"></view>
						</view>
					</block>
				</view>
			</view>
      		<view class="weui-loadmore">
								<view class="weui-loadmore__tips weui-loadmore__tips_in-line">
									{{loadingMessage}}</view>
							</view>
      </view>
		</scroll-view>
	</view>
</view>
<view class="cu-modal drawer-modal justify-start {{dataHouseShow? 'show': ''}}" data-status='0' bindtap="bindDataHouseShow">
	<view class="cu-dialog basis-lg" catchtap>
		<scroll-view style="height:100%;" scroll-y="true">
			<view class="cu-list menu text-left">
				<view class="cu-item">
					<view class="content">
						<view>小区模块数据权限</view>
					</view>
				</view>
			</view>
			<view class="weui-cells weui-cells_after-title">
				<view class="multi-item" wx:for="{{dataHouseTree}}" wx:for-item="item" wx:key="id">
					<view class="weui-cell weui-cell_access" data-item='{{item}}' bindtap="bindDataHouseClick">
						<view class="weui-cell__hd weui-check__hd_in-checkbox">
							<icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item.checked}}"></icon>
							<icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item.checked}}"></icon>
						</view>
						<view class="weui-cell__bd">{{item.name}}</view>
					</view>
					<view class="multi-item" wx:if="{{item.children.length>0}}" wx:for="{{item.children}}" wx:for-item="item2" wx:key="id">
						<view class="weui-cell weui-cell_access" data-item='{{item2}}' bindtap="bindDataHouseClick">
							<view class="weui-cell__hd weui-check__hd_in-checkbox">
								<icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item2.checked}}"></icon>
								<icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item2.checked}}"></icon>
							</view>
							<view class="weui-cell__bd" space="emsp">　{{item2.name}}</view>
						</view>
						<view class="multi-item" wx:if="{{item2.children.length>0}}" wx:for="{{item2.children}}" wx:for-item="item3" wx:key="id">
							<view class="weui-cell weui-cell_access">
								<view class="weui-cell__hd weui-check__hd_in-checkbox" data-item='{{item3}}' bindtap="bindDataHouseClick">
									<icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item3.checked}}"></icon>
									<icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item3.checked}}"></icon>
								</view>
								<view class="weui-cell__bd" space="emsp" data-item='{{item3}}' bindtap="bindDataHouseClick">　　{{item3.name}}</view>
								<view class="weui-cell__ft cuIcon-unfold arrow-down" data-item="{{item3}}" data-level="1" bindtap="getCommunityList" hover-class="weui-cell_active"></view>
							</view>
							<view class="multi-item" wx:if="{{item3.children.length>0}}" wx:for="{{item3.children}}" wx:for-item="item4" wx:key="id">
								<view class="weui-cell weui-cell_access">
									<view class="weui-cell__hd weui-check__hd_in-checkbox" data-item='{{item4}}' bindtap="bindDataHouseClick">
										<icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item4.checked}}"></icon>
										<icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item4.checked}}"></icon>
									</view>
									<view class="weui-cell__bd" space="emsp" data-item='{{item4}}' bindtap="bindDataHouseClick">　　　{{item4.name}}</view>
									<view class="weui-cell__ft cuIcon-unfold arrow-down" data-item="{{item3}}" data-level="2" bindtap="getCommunityList" hover-class="weui-cell_active"></view>
								</view>
								<view class="multi-item" wx:if="{{item4.children.length>0}}" wx:for="{{item4.children}}" wx:for-item="item5" wx:key="id">
									<view class="weui-cell weui-cell_access" data-item='{{item5}}' bindtap="bindDataHouseClick">
										<view class="weui-cell__hd weui-check__hd_in-checkbox">
											<icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item5.checked}}"></icon>
											<icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item5.checked}}"></icon>
										</view>
										<view class="weui-cell__bd" space="emsp">　　　　{{item5.name}}</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</view>
<view class="cu-modal drawer-modal justify-start {{dataShopShow? 'show': ''}}" data-status='0' bindtap="bindDataShopShow">
	<view class="cu-dialog basis-lg" catchtap>
		<scroll-view style="height:100%;" scroll-y="true">
			<view class="cu-list menu text-left">
				<view class="cu-item">
					<view class="content">
						<view>店铺模块数据权限</view>
					</view>
				</view>
			</view>
			<view class="weui-cells weui-cells_after-title">
				<view class="multi-item" wx:for="{{dataShopTree}}" wx:for-item="item" wx:key="id">
					<view class="weui-cell weui-cell_access" data-item='{{item}}' bindtap="bindDataHouseClick">
						<view class="weui-cell__hd weui-check__hd_in-checkbox">
							<icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item.checked}}"></icon>
							<icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item.checked}}"></icon>
						</view>
						<view class="weui-cell__bd">{{item.name}}</view>
					</view>
					<view class="multi-item" wx:if="{{item.children.length>0}}" wx:for="{{item.children}}" wx:for-item="item2" wx:key="id">
						<view class="weui-cell weui-cell_access" data-item='{{item2}}' bindtap="bindDataHouseClick">
							<view class="weui-cell__hd weui-check__hd_in-checkbox">
								<icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item2.checked}}"></icon>
								<icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item2.checked}}"></icon>
							</view>
							<view class="weui-cell__bd" space="emsp">　{{item2.name}}</view>
						</view>
						<view class="multi-item" wx:if="{{item2.children.length>0}}" wx:for="{{item2.children}}" wx:for-item="item3" wx:key="id">
							<view class="weui-cell weui-cell_access">
								<view class="weui-cell__hd weui-check__hd_in-checkbox" data-item='{{item3}}' bindtap="bindDataHouseClick">
									<icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item3.checked}}"></icon>
									<icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item3.checked}}"></icon>
								</view>
								<view class="weui-cell__bd" space="emsp" data-item='{{item3}}' bindtap="bindDataHouseClick">　　{{item3.name}}</view>
								<view class="weui-cell__ft cuIcon-unfold arrow-down" data-item="{{item3}}" data-level="1" bindtap="getCommunityList" hover-class="weui-cell_active"></view>
							</view>
							<view class="multi-item" wx:if="{{item3.children.length>0}}" wx:for="{{item3.children}}" wx:for-item="item4" wx:key="id">
								<view class="weui-cell weui-cell_access">
									<view class="weui-cell__hd weui-check__hd_in-checkbox" data-item='{{item4}}' bindtap="bindDataHouseClick">
										<icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item4.checked}}"></icon>
										<icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item4.checked}}"></icon>
									</view>
									<view class="weui-cell__bd" space="emsp" data-item='{{item4}}' bindtap="bindDataHouseClick">　　　{{item4.name}}</view>
									<view class="weui-cell__ft cuIcon-unfold arrow-down" data-item="{{item3}}" data-level="2" bindtap="getCommunityList" hover-class="weui-cell_active"></view>
								</view>
								<view class="multi-item" wx:if="{{item4.children.length>0}}" wx:for="{{item4.children}}" wx:for-item="item5" wx:key="id">
									<view class="weui-cell weui-cell_access" data-item='{{item5}}' bindtap="bindDataHouseClick">
										<view class="weui-cell__hd weui-check__hd_in-checkbox">
											<icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item5.checked}}"></icon>
											<icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item5.checked}}"></icon>
										</view>
										<view class="weui-cell__bd" space="emsp">　　　　{{item5.name}}</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</view>
<view class="cu-modal drawer-modal justify-start {{functionShow? 'show': ''}}" data-status='0' bindtap="bindFunctionShow">
	<view class="cu-dialog basis-lg" catchtap>
		<scroll-view style="height:100%;" scroll-y="true">
			<view class="cu-list menu text-left">
				<view class="cu-item">
					<view class="content">
						<view>功能权限</view>
					</view>
				</view>
			</view>
			<view class="weui-cells weui-cells_after-title">
				<view class="multi-item" wx:for="{{functionTree}}" wx:for-item="item" wx:key="id">
					<view class="weui-cell weui-cell_access" data-item='{{item}}' bindtap="bindFunctionClick">
						<view class="weui-cell__hd weui-check__hd_in-checkbox">
							<block wx:if="{{item.code!=''}}">
								<icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item.checked}}"></icon>
								<icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item.checked}}"></icon>
							</block>
							<block wx:if="{{item.code==''}}">
								<icon class="weui-icon-checkbox_circle" type="circle" size="23" style="visibility:hidden;"></icon>
							</block>
						</view>
						<view class="weui-cell__bd">{{item.name}}</view>
					</view>
					<view class="multi-item" wx:if="{{item.children.length>0}}" wx:for="{{item.children}}" wx:for-item="item2" wx:key="id">
						<view class="weui-cell weui-cell_access" data-item='{{item2}}' bindtap="bindFunctionClick">
							<view class="weui-cell__hd weui-check__hd_in-checkbox">
								<block wx:if="{{item2.code!=''}}">
									<icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item2.checked}}"></icon>
									<icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item2.checked}}"></icon>
								</block>
								<block wx:if="{{item2.code==''}}">
									<icon class="weui-icon-checkbox_circle" type="circle" size="23" style="visibility:hidden;"></icon>
								</block>
							</view>
							<view class="weui-cell__bd" space="emsp">　{{item2.name}}</view>
						</view>
						<view class="multi-item" wx:if="{{item2.children.length>0}}" wx:for="{{item2.children}}" wx:for-item="item3" wx:key="id">
							<view class="weui-cell weui-cell_access" data-item='{{item3}}' bindtap="bindFunctionClick">
								<view class="weui-cell__hd weui-check__hd_in-checkbox">
									<block wx:if="{{item3.code!=''}}">
										<icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item3.checked}}"></icon>
										<icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item3.checked}}"></icon>
									</block>
									<block wx:if="{{item3.code==''}}">
										<icon class="weui-icon-checkbox_circle" type="circle" size="23" style="visibility:hidden;"></icon>
									</block>
								</view>
								<view class="weui-cell__bd" space="emsp">　　{{item3.name}}</view>
							</view>
							<view class="multi-item" wx:if="{{item3.children.length>0}}" wx:for="{{item3.children}}" wx:for-item="item4" wx:key="id">
								<view class="weui-cell weui-cell_access" data-item='{{item4}}' bindtap="bindFunctionClick">
									<view class="weui-cell__hd weui-check__hd_in-checkbox">
										<block wx:if="{{item4.code!=''}}">
											<icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item4.checked}}"></icon>
											<icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item4.checked}}"></icon>
										</block>
										<block wx:if="{{item4.code==''}}">
											<icon class="weui-icon-checkbox_circle" type="circle" size="23" style="visibility:hidden;"></icon>
										</block>
									</view>
									<view class="weui-cell__bd" space="emsp">　　　{{item4.name}}</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</view>
<view class="cu-modal drawer-modal justify-start {{roleShow? 'show': ''}}" data-status='0' bindtap="bindRoleShow">
	<view class="cu-dialog basis-lg" catchtap>
		<scroll-view style="height:100%;" scroll-y="true">
			<view class="cu-list menu text-left">
				<view class="cu-item">
					<view class="content">
						<view>拥有角色</view>
					</view>
				</view>
			</view>
			<view class="weui-cells weui-cells_after-title">
				<view class="multi-item" wx:for="{{roleTree}}" wx:for-item="item" wx:key="id">
					<view class="weui-cell weui-cell_access" data-item='{{item}}' bindtap="bindRoleClick">
						<view class="weui-cell__hd weui-check__hd_in-checkbox">
							<block wx:if="{{item.code!=''}}">
								<icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item.checked}}"></icon>
								<icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item.checked}}"></icon>
							</block>
							<block wx:if="{{item.code==''}}">
								<icon class="weui-icon-checkbox_circle" type="circle" size="23" style="visibility:hidden;"></icon>
							</block>
						</view>
						<view class="weui-cell__bd">{{item.name}}</view>
					</view>
					<view class="multi-item" wx:if="{{item.children.length>0}}" wx:for="{{item.children}}" wx:for-item="item2" wx:key="id">
						<view class="weui-cell weui-cell_access" data-item='{{item2}}' bindtap="bindRoleClick">
							<view class="weui-cell__hd weui-check__hd_in-checkbox">
								<block wx:if="{{item2.code!=''}}">
									<icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item2.checked}}"></icon>
									<icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item2.checked}}"></icon>
								</block>
								<block wx:if="{{item2.code==''}}">
									<icon class="weui-icon-checkbox_circle" type="circle" size="23" style="visibility:hidden;"></icon>
								</block>
							</view>
							<view class="weui-cell__bd" space="emsp">　{{item2.name}}</view>
						</view>
						<view class="multi-item" wx:if="{{item2.children.length>0}}" wx:for="{{item2.children}}" wx:for-item="item3" wx:key="id">
							<view class="weui-cell weui-cell_access" data-item='{{item3}}' bindtap="bindRoleClick">
								<view class="weui-cell__hd weui-check__hd_in-checkbox">
									<block wx:if="{{item3.code!=''}}">
										<icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item3.checked}}"></icon>
										<icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item3.checked}}"></icon>
									</block>
									<block wx:if="{{item3.code==''}}">
										<icon class="weui-icon-checkbox_circle" type="circle" size="23" style="visibility:hidden;"></icon>
									</block>
								</view>
								<view class="weui-cell__bd" space="emsp">　　{{item3.name}}</view>
							</view>
							<view class="multi-item" wx:if="{{item3.children.length>0}}" wx:for="{{item3.children}}" wx:for-item="item4" wx:key="id">
								<view class="weui-cell weui-cell_access" data-item='{{item4}}' bindtap="bindRoleClick">
									<view class="weui-cell__hd weui-check__hd_in-checkbox">
										<block wx:if="{{item4.code!=''}}">
											<icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item4.checked}}"></icon>
											<icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item4.checked}}"></icon>
										</block>
										<block wx:if="{{item4.code==''}}">
											<icon class="weui-icon-checkbox_circle" type="circle" size="23" style="visibility:hidden;"></icon>
										</block>
									</view>
									<view class="weui-cell__bd" space="emsp">　　　{{item4.name}}</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</view>