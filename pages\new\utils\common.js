var getObjectsFromJson = function(obj, key, val) {
  var objects = [];
  for (var i in obj) {
    if (!obj.hasOwnProperty(i)) continue;
    if (typeof obj[i] == 'object') {
      objects = objects.concat(getObjectsFromJson(obj[i], key, val));
    } else
      //if key matches and value matches or if key matches and value is not passed (eliminating the case where key matches but passed value does not)
      if (i == key && obj[i] == val || i == key && val == '') { //
        objects.push(obj);
      } else if (obj[i] == val && key == '') {
      //only add if the object is not already in the array
      if (objects.lastIndexOf(obj) == -1) {
        objects.push(obj);
      }
    }
  }
  return objects;
}

//return an array of values that match on a certain key
var getValuesFromJson = function(obj, key) {
  var objects = [];
  for (var i in obj) {
    if (!obj.hasOwnProperty(i)) continue;
    if (typeof obj[i] == 'object') {
      objects = objects.concat(getValuesFromJson(obj[i], key));
    } else if (i == key) {
      objects.push(obj[i]);
    }
  }
  return objects;
}

//return an array of keys that match on a certain value
var getKeysFromJson = function(obj, val) {
  var objects = [];
  for (var i in obj) {
    if (!obj.hasOwnProperty(i)) continue;
    if (typeof obj[i] == 'object') {
      objects = objects.concat(getKeysFromJson(obj[i], val));
    } else if (obj[i] == val) {
      objects.push(i);
    }
  }
  return objects;
}

function handleSearch(tree, value) {
  //不满足搜索条件的待删除元素索引数组
  let removeArr = []
  let lastArr = []
  // eslint-disable-next-line no-eval
  //满足条件的字符串匹配为以下内容，红色显示，可以根据自己需要调整以下字符串
  for (let i = 0; i < tree.length; i++) {
    let node = tree[i]
    searchTreeLike(node, i, value, removeArr, lastArr)
  }
  //遍历删除不满足条件的节点
  for (let j = removeArr.length - 1; j >= 0; j--) {
    tree.splice(removeArr[j], 1)
  }
  //return {
  //  tree: tree,
  //  lastArr: lastArr
  //};
  //return;
  //console.log(tree);
  //return;
  //return;
  //方法执行完成后tree只留下符合搜索条件的节点
  for (let i = 0; i < lastArr.length; i++) {
    var a = handleMatch(tree, lastArr[i].id);
    console.log(JSON.stringify(lastArr[i]));
    console.log(lastArr[i].name);
    console.log(getcascade(a, lastArr[i].name));
  }
}

function searchTreeLike(node, index, value, removeArr, lastArr) {
  let children = node.children
  //针对非叶子节点，需要递归其children节点
  if (children && children.length > 0) {
    let innderArr = []
    for (let i = 0; i < children.length; i++) {
      searchTreeLike(children[i], i, value, innderArr, lastArr)
    }
    //如果当前节点不满足搜索条件，则对其children不满足条件的节点执行删除操作
    if (node.name.indexOf(value) === -1) {
      for (let j = innderArr.length - 1; j >= 0; j--) {
        children.splice(innderArr[j], 1)
      }
      /*
       *children节点删除结束后，如果children length为0，
       *并且当前节点也不满足搜索条件，则当前节点也加入删除数组
       */
      if (node.children.length === 0) {
        removeArr.push(index)
      }
    } else {
      //当前节点非叶子节点，将满足条件内容做标记显示
      node.name = node.name;
      searchTree2(node, lastArr);
    }
  } else {
    //叶子节点，直接进行匹配
    if (node.name.indexOf(value) === -1) {
      removeArr.push(index)
    } else {
      //将满足条件内容做标记显示
      node.name = node.name;
      lastArr.push(node);
    }
  }
}

//查找最后一个节点
function searchTree2(node, lastArr) {
  let children = node.children
  //针对非叶子节点，需要递归其children节点
  if (children && children.length > 0) {
    for (let i = 0; i < children.length; i++) {
      searchTree2(children[i], lastArr);
    }
  } else {
    lastArr.push(node);
  }
}

function handleMatch(tree, value) {
  //不满足搜索条件的待删除元素索引数组
  let removeArr = []
  // eslint-disable-next-line no-eval
  //满足条件的字符串匹配为以下内容，红色显示，可以根据自己需要调整以下字符串
  for (let i = 0; i < tree.length; i++) {
    let node = tree[i]
    searchTreeMatch(node, i, value, removeArr)
  }
  //遍历删除不满足条件的节点
  for (let j = removeArr.length - 1; j >= 0; j--) {
    tree.splice(removeArr[j], 1)
  }
  return tree;
}

function searchTreeMatch(node, index, value, removeArr) {
  let children = node.children
  //针对非叶子节点，需要递归其children节点
  if (children && children.length > 0) {
    let innderArr = []
    for (let i = 0; i < children.length; i++) {
      searchTreeMatch(children[i], i, value, innderArr)
    }
    //如果当前节点不满足搜索条件，则对其children不满足条件的节点执行删除操作
    if (node.id != value) {
      for (let j = innderArr.length - 1; j >= 0; j--) {
        children.splice(innderArr[j], 1);
      }
      /*
       *children节点删除结束后，如果children length为0，
       *并且当前节点也不满足搜索条件，则当前节点也加入删除数组
       */
      if (node.children.length === 0) {
        removeArr.push(index)
        //console.log('叶子节点，直接进行匹配a' + index);
        //console.log(node);
      }
    } else {
      //当前节点非叶子节点，将满足条件内容做标记显示
      node.name = node.name;
    }
  } else {
    //叶子节点，直接进行匹配
    if (node.id != value) {
      removeArr.push(index)
      //console.log('叶子节点，直接进行匹配' + index);
      //console.log(node);
    } else {
      //将满足条件内容做标记显示
      node.name = node.name;
    }
  }
}

function getcascade(opts, opt, path) {
  if (path === undefined) {
    path = []
  }
  for (var i = 0; i < opts.length; i++) {
    var temPath = path.concat()
    temPath.push(opts[i].name)
    if (opt == opts[i].name) {
      return temPath
    }
    if (opts[i].children) {
      var findResult = getcascade(opts[i].children, opt, temPath)
      if (findResult) {
        return findResult
      }
    }
  }
}

var toTree = function(data) {
  // 删除 所有 children,以防止多次调用
  data.forEach(function(item) {
    delete item.children;
  });

  // 将数据存储为 以 id 为 KEY 的 map 索引数据列
  var map = {};
  data.forEach(function(item) {
    map[item.id] = item;
  });
  //        console.log(map);

  var val = [];
  data.forEach(function(item) {
    // 以当前遍历项，的pid,去map对象中找到索引的id
    var parent = map[item.parent_id];

    // 如果找到索引，那么说明此项不在顶级当中,那么需要把此项添加到，他对应的父级中
    if (parent) {
      (parent.children || (parent.children = [])).push(item);
    } else {
      //如果没有在map中找到对应的索引ID,那么直接把 当前的item添加到 val结果集中，作为顶级
      val.push(item);
    }
  });
  // wx.showToast({
  //   title: 'tree init',
  //   icon: 'none',
  //   duration: 2000
  // })
  return val;
}

var toList = function(tree){
  var queen = [];
  var out = [];
  queen = queen.concat(tree);
  while (queen.length) {
    var first = queen.shift();
    if (first.children) {
      queen = queen.concat(first.children)
      delete first['children'];
    }
    out.push(first);
  }
  return out;
}


// data:json， nodeId:节点
function getParent(data2, nodeId2) {
  var arrRes = [];
  if (data2.length == 0) {
    if (!!nodeId2) {
      arrRes.unshift(nodeId2);
    }
    return arrRes;
  }
  let rev = (data, nodeId) => {
    for (var i = 0, length = data.length; i < length; i++) {
      let node = data[i];
      if (node.name == nodeId) {
        arrRes.unshift(nodeId);
        rev(data2, node);
        break;
      } else {
        if (!!node.children) {
          rev(node.children, nodeId);
        }
      }
    }
    return arrRes;
  };
  arrRes = rev(data2, nodeId2);
  return arrRes;
}

var getNewJson = function(json) {
  var newJson = [];
  for (let i = 0; i < json.length; i++) {
    let node = json[i]
    searchNew(node, newJson)
  }
}
var searchNew = function(node, newJson) {
  let children = node.children
  if (children && children.length > 0) {
    for (let i = 0; i < children.length; i++) {
      searchNew(children[i], newJson);
    }
  } else {
    getParent(node, node.name);
  }
}

const wxuuid = function () {
  var s = [];
  var hexDigits = "0123456789abcdef";
  for (var i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[14] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23] = "";//split symbol

  var uuid = s.join("");
  return uuid
}

/* output */
module.exports = {
  getObjectsFromJson: getObjectsFromJson,
  getValuesFromJson: getValuesFromJson,
  getKeysFromJson: getKeysFromJson,
  getJsonTree: handleSearch,
  getJsonTree2: handleMatch,
  getNewJson: getNewJson,
  getParent: getParent,
  getcascade: getcascade,
  toTree: toTree,
  toList: toList,
  wxuuid: wxuuid
}