/* pages/meetingQrcode/meetingQrcode.wxss */
.bg{
  height: 100%;
  width: 100%;
  position: absolute;
  z-index: -1;
}
.bg image{
  width: 100%;
  height: 100%;
  display: block;
}
.title{
  margin-top: 20%;
  display: flex;
  flex-direction: column;
  text-align: center;
  color: #fff;
}
.title .top{
  font-size: 56rpx;
}
.title .bottom{
  font-size: 36rpx;
  margin-top: 20rpx;
}
.headImg{
  width: 35%;
  height: 35vw;
  margin: 0 auto;
  overflow: hidden;
  border-radius: 50%;
  border: 1px solid #ff0000;
  padding: 10rpx;
  margin-top: 60rpx;
}
.headImg image{
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.name{
  width: 40%;
  margin: 0 auto;
  border-bottom: 1px solid #fff;
  color: #fff;
  text-align: center;
  margin-top: 20rpx;
  padding-bottom: 15rpx;
}
.qrcode{
  margin-top: 25rpx;
  text-align: center;
  color: rgba(255,255,255,.8);
}
.qrcode canvas{
  margin: 0 auto;
  margin-bottom: 20rpx;
}