<!--pages/new/xlmap/xlmap.wxml-->
<watermark></watermark>
<view class="map_container">
  <map id='map' class='map' longitude='{{longitude}}' latitude='{{latitude}}' scale='{{scale}}' markers='{{markers}}' bindmarkertap='bindmarkertap' bindlabeltap='bindmarkertap' catchtap="bindNormal" show-location ></map>
</view>
<view wx:if="{{viewShow}}" class="view-row-show">
   <view class="view-detail-content">
    <view class="content-box" bindtap="getMapDetail" data-id="{{mapDetail.id}}" data-address="{{mapDetail.address}}" data-name="{{mapDetail.companyName}}">

<view style="display:flex;align-items: center;justify-content: space-between;" >
<view style="display:flex;align-items: center;">
<view>
  <image wx:if="{{mapDetail.img_url}}"
      style="width: 48px; height: 48px; margin-top: 13px;border-radius: 5px;"
      src="{{'https://zzsbapi.jxjkga.cn'+mapDetail.img_url}}"
    />
</view>         
<view style="margin-left:10px;">
<view> <span style="font-weight: 800;">单位名称：</span>{{mapDetail.companyName || '--'}}</view>
<!-- <view> <span style="font-weight: 800;">设备名称：</span>{{itemName.led_name || '--'}}</view> -->
<view> <span style="font-weight: 800;">单位地址：</span>{{ mapDetail.address ||'--'}}</view>
</view>
<view><view class="btn_cha">查看</view></view>
</view>
</view>
</view>

   </view>
    
 </view>
 <view class='ticket-container' style="height:32.5%;">
            <!--Tab布局--> 
        <view class="tabbox">
        <view class='title'>
        <view class="{{0 == currentIndex ? 'title-sel-selected' : 'title-sel'}}" bindtap='titleClick' data-idx='0'>
          <view>全部</view>
        </view>
        <!-- <view class="{{1 == currentIndex ? 'title-sel-selected' : 'title-sel'}}" bindtap='titleClick' data-idx='1'>
          <view style=" font-size:24rpx;">LED巡逻公司</view>
        </view> -->
      </view>
      </view>
      <!--内容布局-->
      <swiper class='swiper' bindchange='pagechange' current='{{currentIndex}}'>
      
        <swiper-item class='swiper'>
        
          <image class="mapparkingbg" src="../../icons/mapparking-bg.png"></image>
          <scroll-view style="height:100%;width: 100%;" scroll-y='true'>
              <view class="contentbox">
                <view class="content" wx:for="{{companyList}}" wx:for-index="idx" wx:for-item="itemName" wx:key="index" >
                <view class="content-box" bindtap="getMapDetail" data-id="{{itemName.id}}" data-address="{{itemName.address}}" data-name="{{itemName.company_name}}">

                  <view style="display:flex;align-items: center;justify-content: space-between;" >
                <view style="display:flex;align-items: center;">
                  <view>
                    <image wx:if="{{itemName.img_url}}"
                        style="width: 48px; height: 48px; margin-top: 13px;border-radius: 5px;"
                        src="{{'https://zzsbapi.jxjkga.cn'+itemName.img_url}}"
                      />
                  </view>         
                <view style="margin-left:10px;">
                  <view> <span style="font-weight: 800;">单位名称：</span>{{itemName.company_name || '--'}}</view>
                  <!-- <view> <span style="font-weight: 800;">设备名称：</span>{{itemName.led_name || '--'}}</view> -->
                  <view> <span style="font-weight: 800;">单位地址：</span>{{ itemName.address ||'--'}}</view>

                </view>
                <view><view class="btn_cha">查看</view></view>
              </view>
              </view>
                </view>
                </view>
              </view>
          </scroll-view>
        </swiper-item>
      

        <swiper-item class='swiper'>
          <image class="mapparkingbg" src="../../icons/mapparking-bg.png"></image>
          <scroll-view style="height:100%;width: 100%;" scroll-y='true'>
              <view class="contentbox">
                <view class="content" wx:for="{{circleParkList}}" wx:for-index="idx" wx:for-item="itemName" wx:key="index">
                <view class="content-box" bindtap="getMapDetail" data-latitude="{{itemName.pklCoAmapLat}}" data-longitude="{{itemName.pklCoAmapLng}}" data-distance="{{itemName.distance}}" data-pklName="{{itemName.pklName}}" data-pklType="{{itemName.pkl_in_out}}" data-pklRuleDetail="{{itemName.pklRuleDetail}}" data-pklLotsLeft="{{itemName.pklLotsLeft}}">
                  <view>
                    <view class="parkingname">{{itemName.pklName}}</view>
                    <view class="ku">停车场(库)</view>
                  </view>
                  <view class="distanceFlex">
                    <view><image class="dingwei" src="../../icons/dingwei.png"></image></view>
                    <view class="km">{{itemName.distance}}</view>
                  </view>
                </view>
                <view class="bottom-box">
                  <!-- <view><image class="carmap" src="../../icons/carmap.png"></image> <text class="lable"> 可停车</text></view> -->
                  <view><image class="carmap" src="{{itemName.pklLotsLeft>0 ? '../../icons/carmap.png' :'../../icons/nostaion.png'}}"></image> <text class="lable"> {{itemName.pklLotsLeft>0 ? '可停车' :'暂无车位'}}</text></view>
                  <view>剩余车位：{{itemName.pklLotsLeft}}</view>
                </view>
                </view>
              </view>
          </scroll-view>
        </swiper-item>
        <swiper-item class='swiper'>          
          <image class="mapparkingbg" src="../../icons/mapparking-bg.png"></image>
          <scroll-view style="height:100%;width: 100%;" scroll-y='true'>
              <view class="contentbox">
                <view class="content" wx:for="{{roadParkList}}" wx:for-index="idx" wx:for-item="itemName" wx:key="index">
                <view class="content-box" bindtap="getMapDetail" data-latitude="{{itemName.pklCoAmapLat}}" data-longitude="{{itemName.pklCoAmapLng}}" data-distance="{{itemName.distance}}" data-pklName="{{itemName.pklName}}" data-pklType="{{itemName.pkl_in_out}}" data-pklRuleDetail="{{itemName.pklRuleDetail}}" data-pklLotsLeft="{{itemName.pklLotsLeft}}">
                  <view>
                    <view class="parkingname">{{itemName.pklName}}</view>
                    <view class="ku">道路停车场</view>
                  </view>
                  <view class="distanceFlex">
                    <view><image class="dingwei" src="../../icons/dingwei.png"></image></view>
                    <view class="km">{{itemName.distance}}</view>
                  </view>
                </view>
                <view class="bottom-box">
                  <!-- <view><image class="carmap" src="../../icons/carmap.png"></image> <text class="lable"> 可停车</text></view> -->
                  <view><image class="carmap" src="{{itemName.pklLotsLeft>0 ? '../../icons/carmap.png' :'../../icons/nostaion.png'}}"></image> <text class="lable"> {{itemName.pklLotsLeft>0 ? '可停车' :'暂无车位'}}</text></view>
                  <view>剩余车位：{{itemName.pklLotsLeft}}</view>
                </view>
                </view>
              </view>
          </scroll-view>
        </swiper-item>
      </swiper>

      
    <!-- </scroll-view> -->
</view>
 


