/* miniprogram/pages/user/login.wxss */

.page>view {
  margin: 20rpx;
}

.return-btn {
  font-size: 14px;
  margin-top: 10px;
  color: rgba(0, 0, 0, 0.3);
}

.return-btn:hover {
  background-color: transparent;
}

.return-btn:after {
  border: 0;
}

.weui-btn {
  height: 52px;
  line-height: 52px;
  border-radius: 30px;
}

button[type=default] {
  background-color: #ccc !important;
  color: #fff !important;
}

.title {
  padding: 20px 20px 40px 20px;
  text-align: center;
  font-size: 26px;
  letter-spacing: 2px;
}

.user-agreement {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 100rpx;
  line-height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  background-color: #fff;
}

.weui-agree {
  margin-left: 10rpx;
  float: left;
  padding: 15px 0px 15px 15px;
}

.weui-agree__link {
  float: left;
  font-size: 13px;
  display: block;
  padding: 15px 15px 15px 0px;
}

.user-agreement-tips {
  position: absolute;
  top: -25rpx;
  left: 25rpx;
  padding: 3px;
  background-color: #fd6500;
  color: #fff;
  border-radius: 3px;
}

.user-agreement-tips icon {
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid #fd6500;
  position: absolute;
  bottom: -10rpx;
}

.weixin-update-tips {
  padding: 40px;
}

icon {
  margin-right: 13px;
}

.icon-box {
  margin-bottom: 25px;
  display: flex;
  align-items: center;
}

.icon-box__ctn {
  flex-shrink: 100;
}

.icon-box__title {
  font-size: 20px;
}

.icon-box__desc {
  margin-top: 6px;
  font-size: 14px;
  color: #888;
}

.icon_sp_area {
  margin-top: 10px;
  text-align: left;
}
