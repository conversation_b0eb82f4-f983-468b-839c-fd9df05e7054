# 网约房报送功能 - 微信小程序版本

## 功能说明

本功能将原先在Vue中开发的网约房报送功能迁移到微信小程序原生语言，提升用户体验和加载速度。

## 页面结构

### 1. 主页面 (wyfsb)
- **文件位置**: `pages/wyfsb/wyfsb.*`
- **功能**: 网约房报送主页，包含三个主要功能入口和房间二维码生成
- **主要功能**:
  - 本人住宿登记
  - 帮助登记
  - 网约房客服
  - 房间选择和二维码生成

### 2. 登记页面 (wyfbzdj)
- **文件位置**: `pages/wyfsb/wyfbzdj.*`
- **功能**: 人员信息登记页面
- **主要功能**:
  - 个人信息填写（姓名、手机、证件等）
  - 照片上传（帮助登记模式）
  - 监护人信息填写（未成年人）
  - 身份证验证

## 主要转换内容

### Vue → 微信小程序转换对照

| Vue组件/语法 | 微信小程序 | 说明 |
|-------------|-----------|------|
| `<template>` | `<view>` | 页面结构 |
| `<van-image>` | `<image>` | 图片组件 |
| `<van-field>` | `<input>` | 输入框 |
| `<van-picker>` | `<picker>` | 选择器 |
| `<van-button>` | `<button>` | 按钮 |
| `v-for` | `wx:for` | 列表渲染 |
| `v-if` | `wx:if` | 条件渲染 |
| `@click` | `bindtap` | 点击事件 |
| `this.$axios` | `wx.request` | 网络请求 |
| `this.$Toast` | `wx.showToast` | 提示信息 |
| `this.$Dialog` | `wx.showModal` | 对话框 |
| `this.$router` | `wx.navigateTo` | 页面跳转 |

### 主要功能实现

1. **网络请求**: 使用`wx.request`替代axios
2. **图片上传**: 使用`wx.chooseImage`和`wx.uploadFile`
3. **数据绑定**: 使用`setData`更新页面数据
4. **页面跳转**: 使用`wx.navigateTo`和`wx.navigateBack`
5. **用户反馈**: 使用`wx.showToast`和`wx.showModal`

## API接口

需要确保以下API接口可用：
- `/company/getMyBookOnlineRoomList` - 获取房间列表
- `/company/generateOnlineRoomFloorQrCode` - 生成楼层二维码
- `/base/user/info/byToken` - 获取用户信息
- `/base/verifyIdentity` - 验证身份证
- `/company/registerOnlineRoom` - 注册住宿信息

## 注意事项

1. **API地址**: 请在代码中修改`HOST`变量为实际的API地址
2. **图片处理**: 小程序中的图片上传和Vue中略有不同，已做相应适配
3. **样式适配**: 已将Vue的CSS样式转换为小程序的WXSS
4. **权限配置**: 如需要获取用户位置等权限，请在app.json中配置

## 使用方法

1. 确保页面已在`app.json`中注册
2. 修改代码中的API地址
3. 测试各项功能是否正常
4. 根据实际需求调整样式和交互

## 测试建议

建议测试以下功能点：
- [ ] 页面正常加载和显示
- [ ] 房间列表获取和选择
- [ ] 二维码生成和刷新
- [ ] 本人住宿登记流程
- [ ] 帮助登记流程（包含图片上传）
- [ ] 表单验证和提交
- [ ] 页面跳转和返回
