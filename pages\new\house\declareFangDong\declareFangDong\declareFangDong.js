// pages/new/house/declareFangDong/declareFangDong/declareFangDong.js
var common = require('../../../utils/common.js');
var user = require('../../../utils/user.js');
const watch = require("../../../utils/watchUtils.js");
const http = require("../../../utils/httpUtils.js");
var app = getApp();
var url = app.globalData.requestUrl;
Page({
  /**
   * 页面的初始数据
   */
  data: {
    //临时缓存
    lsdata: {},
    inputXQShowed: false,
    inputXQVal: '',
    inputMPShowed: false,
    inputMPVal: '',
    submit: {
      code: -1,
      msg: '申报小区',
      data: {},
      next: ''
    },
    areaArrayFirstName: '请选择',
    areaSearchResult: [],
    mpSearchResult: [],
    areaInfo: [],
    //门牌选择器的中文展示
    mpInfo: [],
    areaIndex: [0, 0, 0, 0],
    mpIndex: [0, 0],
    areaArray: [
      [],
      [],
      [],
      []
    ],
    areaArrayValue: [
      [],
      [],
      [],
      []
    ],
    mpArray: [
      ['请先选择小区'],
      ['请选择']
    ],
    mpArrayValue: [
      [0],
      [0]
    ],
    mpStatusArray: [

    ],
    uuidsArray: [

    ],
    areaData: [],
    house: {
      id: 0,
      house_uuid: '',
      uuid: '',
      area_location_id: 0,
      area_location_name: '请选择',
      img_url: '',
      address: '',
      address_id: '0',
      building_id: '0',
      address_status: 0,
      house_type_name: '',
      is_rent: 0,
      remark: '',
      owner_id: 0,
      owner_photo: '',
      owner_realname: '',
      owner_mobilephone_number: '',
      status: -2
    },
    apply: {},
    files: []
  },
  bindSave: function () {
    var that = this;
    if (that.data.house.area_location_id == 0) {
      wx.showToast({
        title: '请选择小区',
        icon: 'none',
        duration: 2000
      })
      return;
    }
    if (that.data.house.address_id == '0') {
      wx.showToast({
        title: '请选择门牌号',
        icon: 'none',
        duration: 2000
      })
      return;
    }
    // if (that.data.files.length == 0) {
    //   wx.showToast({
    //     title: '请至少上传一张门头照',
    //     icon: 'none',
    //     duration: 2000
    //   })
    //   return;
    // }
    
    //该房屋已经被批准过
    if (that.data.house.address_status == -1 || that.data.house.address_status == 1) {
      //且为自住房
      if (that.data.house.is_rent == 0) {
        let  member_photo = that.data.house.owner_photo.replace(url, '');
        wx.showModal({
          title: '提示',
          content:  that.data.house.address_status == 1?'该房屋已被注册,可申请加入或由户主邀请':'该房屋已被申请,待审核通过后加入',
          cancelText: '取消',
          cancelColor: '#ff0000',
          confirmText: '申请加入',
          confirmColor: '#008000',
          success(res) {
            if (res.confirm) {
              http.jsonpost('house/renter/add', {
                 type:3,
                 member_photo:member_photo,
                 img_url: member_photo,
                 room_number: that.data.house.address,
                 house_uuid: that.data.house.house_uuid,
                 uuid:common.wxuuid(),
                 member_mobilephone_number:that.data.house.owner_mobilephone_number,
                 member_realname:that.data.house.owner_realname,
                 member_id :that.data.house.owner_id,
                 status:-1
                },
                res => {
                  if (res.data.success == 1) {
                    wx.showModal({
                      title: '提示',
                      content: '提交成功，请耐心等待户主审核',
                      showCancel: false,
                      success(res) {
                        if (res.confirm) {
                          wx.switchTab({
                            url: '/pages/new/declareRecord/declareRecord/declareRecord',
                          })
                        }
                      }
                    })
                  }
                }
              )
            } else if (res.cancel) {
              return
            }
          }
        })

      }
      //且为出租房
      else if (that.data.house.is_rent == 1) {
        wx.showToast({
          title:  that.data.house.address_status == 1?'该房屋已被注册,可申请加入或由户主邀请':'该房屋已被申请,待审核通过后加入',
          icon: 'none',
          duration: 2000
        })
        return;
      }
    } else {
      that.data.house.owner_photo = that.data.house.owner_photo.replace(url, '');
      wx.request({
        method: 'POST',
        url: url + '/storemgmt/house/add',
        data: that.data.house,
        header: {
          'content-type': 'application/json',
          token: wx.getStorageSync('token')
        },
        body: {

        },
        success(res) {
          if (res.data.success == 1) {
            let applyid = res.data.data.apply_uuid;
            wx.showModal({
              title: '提示',
              content: '保存成功，是否马上提交审核？',
              cancelText: '暂不提交',
              cancelColor: '#ff0000',
              confirmText: '提交审核',
              confirmColor: '#008000',
              success(res) {
                if (res.confirm) {
                  http.post('house/apply/verify', {
                      uuid: applyid,
                      status: -1,
                      content: ''
                    },
                    res => {
                      if (res.data.success == 1) {
                        wx.showModal({
                          title: '提示',
                          content: '提交成功，请耐心等待管理员审核',
                          showCancel: false,
                          success(res) {
                            if (res.confirm) {
                              wx.switchTab({
                                url: '/pages/new/declareRecord/declareRecord/declareRecord',
                              })
                            }
                          }
                        })
                      }
                    }
                  )
                } else if (res.cancel) {
                  wx.switchTab({
                    url: '/pages/new/declareRecord/declareRecord/declareRecord'
                  })
                }
              }
            })
          } else {
            wx.showToast({
              title: res.data.dsc,
              icon: 'none',
              duration: 2000
            })
          }
        }
      })
    }


  },
  /**
   * 点击选择模糊查询出的小区结果
   */
  getAreaSearchResult: function (e) {

    var that = this;
    var data = {
      house: that.data.house,
      areaInfo: that.data.areaInfo,
      inputXQShowed: false,
      inputXQVal: ''
    };
    var area_location_name = '';
    var result = e.currentTarget.dataset.result;
    data.areaInfo = result;
    for (let i = 0; i < result.length; i++) {
      area_location_name += result[i].name;
      if (i < result.length - 1) {
        area_location_name += '/';
      }
    }
    if (result.length == 4) {
      data.house.area_location_id = result[3].id;
      
    } else {
      data.house.area_location_id = 0;
    }
    data.house.address = '';
    data.house.address_id = 0;
    data.house.address_status = 0;
    data.house.area_location_name = area_location_name;
    that.setData(data);
    console.log(data);
  },
  /**
   * 点击选择模糊查询出的门牌结果
   */
  getMPSearchResult: function (e) {

    var that = this;
    var data = {
      house: that.data.house,
      inputMPShowed: false,
      inputMPVal: ''
    };
    var result = e.currentTarget.dataset.result;
    
    data.house.address = result.building_room_name;
    data.house.building_id = result.building_id;
    data.house.address_id = result.room_id;
    data.house.address_status = result.status === null ? 0 : result.status;
    data.house.house_uuid = result.uuid === null ? 0 : result.uuid;
    console.log("状态是" + data.house.address_status)
    that.setData(data);
    console.log(data);
  },
  /**
   * 关键字搜索小区执行搜索
   * @param {*} e 
   */
  inputXQTyping: function (e) {
    if (e.detail.value.length < 2) {
      wx.showToast({
        title: '请至少输入两个字',
        icon: 'none',
        duration: 2000
      })
      return;
    }
    var that = this;
    that.setData({
      inputXQVal: e.detail.value
    });
    var areaData = that.data.areaData;
    areaData = JSON.parse(JSON.stringify(getApp().globalData.areaData));
    var searchResult = that.handleSearch(areaData, e.detail.value);
    var data = {
      areaSearchResult: that.data.areaSearchResult,
    };
    data.areaSearchResult = searchResult;
    
    that.setData(data);
  },
  inputMPTyping: function (e) {
    var that = this;

    if (e.detail.value.length < 2) {
      wx.showToast({
        title: '请至少输入两个字',
        icon: 'none',
        duration: 2000
      })
      return;
    }
    //输入门牌信息后进行模糊查询 
    that.setData({
      inputMPVal: e.detail.value
    });
    http.get('/house/community/building/room/like', {
      param: e.detail.value,
      area_location_id: that.data.house.area_location_id,
    }, res => {
      if (res.data.success == 1) {
        var data = {
          mpSearchResult: that.data.mpSearchResult,
        };

        data.mpSearchResult = res.data.data;
        that.setData(data);
      }
    })
  },

  handleSearch: function (tree, searchText) {
    let removeArr = []
    let lastArr = []
    for (let i = 0; i < tree.length; i++) {
      let node = tree[i];
      this.searchTreeLike(node, i, searchText, removeArr, lastArr)
    }
    for (let j = removeArr.length - 1; j >= 0; j--) {
      tree.splice(removeArr[j], 1)
    }
    let newTree = tree;
    let lastNode = lastArr;
    let areaSearchResult = [];
    for (let m = 0; m < lastNode.length; m++) {
      areaSearchResult.push(
        this.findParent(newTree, JSON.parse(lastNode[m]).id, [], searchText)
      );
    }
    return areaSearchResult
  },
  searchTreeLike: function (node, index, searchText, removeArr, lastArr) {
    let children = node.children;
    if (children && children.length > 0) {
      let removeArrInner = []
      for (let i = 0; i < children.length; i++) {
        this.searchTreeLike(JSON.parse(JSON.stringify(children[i])), i, searchText, removeArrInner, lastArr)
      }
      if (node.name.indexOf(searchText) === -1) {
        for (let j = removeArrInner.length - 1; j >= 0; j--) {
          children.splice(removeArrInner[j], 1)
        }
        if (node.children.length === 0) {
          removeArr.push(index)
        }
      } else {
        node.name = node.name;
        this.findLastNode(node, lastArr);
      }
    } else {
      if (node.name.indexOf(searchText) === -1) {
        removeArr.push(index)
      } else {
        node.name = node.name;
        if (lastArr.indexOf(JSON.stringify(node)) == -1) {
          lastArr.push(JSON.stringify(node));
        }
      }
    }
  },
  findLastNode: function (node, lastArr) {
    let children = node.children
    if (children && children.length > 0) {
      for (let i = 0; i < children.length; i++) {
        this.findLastNode(children[i], lastArr);
      }
    } else {
      if (lastArr.indexOf(JSON.stringify(node)) == -1) {
        lastArr.push(JSON.stringify(node));
      }
    }
  },
  findParent: function (data, id, indexArray, searchText) {
    let arr = Array.from(indexArray)
    for (let i = 0, len = data.length; i < len; i++) {
      if (data[i].name.indexOf(searchText) != -1) {
        arr.push({
          id: data[i].id,
          name: data[i].name,
          is_match: 1
        })
      } else {
        arr.push({
          id: data[i].id,
          name: data[i].name,
          is_match: 0
        })
      }
      if (data[i].id === id) {
        return arr
      }
      let children = data[i].children
      if (children && children.length) {
        let result = this.findParent(children, id, arr, searchText)
        if (result) return result
      }
      arr.pop();
    }
  },
  bindGotoList: function () {
    wx.switchTab({
      url: '/pages/new/declareRecord/declareRecord/declareRecord?status=-1',
    })
  },
  showXQInput: function () {
    this.setData({
      inputXQShowed: true
    });
  },
  showMPInput: function () {
    if (this.data.house.area_location_id == 0) {
      wx.showToast({
        title: '请先选择小区',
        icon: 'none',
        duration: 2000
      })
      return;
    }
    this.setData({
      inputMPShowed: true
    });

  },

  hideXQInput: function () {
    this.setData({
      inputXQVal: "",
      inputXQShowed: false
    });
  },
  hideMPInput: function () {
    this.setData({
      inputMPVal: "",
      inputMPShowed: false
    });
  },
  clearXQInput: function () {
    this.setData({
      inputXQVal: ""
    });
  },
  clearMPInput: function () {
    this.setData({
      inputMPVal: ""
    });
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    watch.setWatcher(this); // 设置监听器，建议在onLoad下调用
    var that = this;
    var data = {
      house: that.data.house,
      areaData: that.data.areaData,
      areaArray: that.data.areaArray,
      areaArrayValue: that.data.areaArrayValue,
      mpArray: that.data.mpArray,
      mpArrayValue: that.data.mpArrayValue
    };
    var house = that.data.house;
    var areaData = that.data.areaData;
    if (options.type == 1) {
      house.house_type_name = '出租';
      house.is_rent = 1;
    } else {
      house.house_type_name = '自住';
      wx.setNavigationBarTitle({
        title: '自住'
      })
      house.is_rent = 0;
    }
    if (options.uuid == undefined || options.uuid == '') {
      house.uuid = common.wxuuid();
    }
    user.getUserInfo().then((res) => {
      var userInfo = getApp().globalData.userInfo;
      house.owner_id = userInfo.id;
      house.owner_mobilephone_number = userInfo.mobilephone_number;
      house.owner_realname = userInfo.realname;
      house.owner_photo = userInfo.photo;
      areaData = getApp().globalData.areaData;
      
      data.house = house;
      data.areaData = areaData;
      var lvl1 = areaData;
      var lvl1Name = [that.data.areaArrayFirstName];

      lvl1.map(function (d) {
        return lvl1Name.push(d.name);
      });
      var lvl1Value = [0];
      lvl1.map(function (d) {
        return lvl1Value.push(d.id);
      });
      data.areaArray[0] = lvl1Name;
      data.areaArray[1] = [that.data.areaArrayFirstName];
      data.areaArray[2] = [that.data.areaArrayFirstName];
      data.areaArray[3] = [that.data.areaArrayFirstName];
      data.areaArrayValue[0] = lvl1Value;
      data.areaArrayValue[1] = [0];
      data.areaArrayValue[2] = [0];
      data.areaArrayValue[3] = [0];
      that.setData(data);
    });
  },
  //动态监听
  watch: {
    'house.area_location_id': function (newVal, oldVal) {
      console.log("watch..." + newVal)
      
      if (typeof (newVal) != "undefined") {
        if (newVal != "0") {
          http.get('house/community/building', {
            area_location_id: newVal
          }, res => {
            if (res.data.success == 1) {
              let arrayA = ['请选择'];
              let arrayB = ['0']
              res.data.data.map(function (d) {
                arrayA.push(d.name);
                arrayB.push(d.building_id);
              });
              this.setData({
                mpArray: [
                  arrayA,
                  []
                ],
                mpArrayValue: [
                  arrayB,
                  []
                ],

              });
            }
          })
        } else {
          //重置楼栋和门派选择器
          this.setData({
            mpArray: [
              ['请先选择小区'],
              ['请选择']
            ],
            mpArrayValue: [
              [0],
              [0]
            ],
          });
        }
      }


    }
  },
  bindIsRent: function (e) {
    var house = this.data.house;
    if (e.detail.value) {
      house.is_rent = 1;
    } else {
      house.is_rent = 0;
    }
    this.setData({
      house: house
    });
  },
  // bindAddress: function (e) {
  //   var house = this.data.house;
  //   house.address = e.detail.value;
  //   this.setData({
  //     house: house
  //   });
  // },
  bindRemark: function (e) {
    var house = this.data.house;
    house.remark = e.detail.value;
    this.setData({
      house: house
    });
  },
  bindGetHouseInfo: function (uuid) {
    var that = this;
    wx.request({
      method: 'POST',
      url: url + '/storemgmt/house/info?uuid=' + uuid,
      data: that.data.house,
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        token: wx.getStorageSync('token')
      },
      success(res) {
        if (res.data.success == 1) {

        }
      }
    })
  },
  bindChooseImage: function () {
    var that = this;
    wx.chooseImage({
      success(res) {
        const tempFilePaths = res.tempFilePaths;
        var data = {
          files: that.data.files
        }
        for (let i = 0; i < res.tempFilePaths.length; i++) {
          data.files.push({
            uuid: '',
            user_id: 0,
            table: '',
            table_name: '',
            tag: '',
            file_path: res.tempFilePaths[i],
            file_name: '',
            file_localpath: res.tempFilePaths[i],
            status: -1,
            progress: 0
          });
        }
        that.setData(data);
        for (let j = 0; j < data.files.length; j++) {
          if (data.files[j].status != -1) {
            continue;
          }
          const uploadTask = wx.uploadFile({
            url: url + '/storemgmt/file/upload',
            filePath: data.files[j].file_localpath,
            header: {
              token: wx.getStorageSync('token')
            },
            dataType: 'json',
            name: 'files',
            formData: {
              'table': 'house',
              'table_uuid': that.data.house.uuid,
              'tag': ''
            },
            success(res) {
              console.log(res.data);
              const data = JSON.parse(res.data);
              console.log(data);
              let status = 0;
              if (data.success == 1) {
                status = 1;
              }
              let files = that.data.files;
              files[j] = {
                uuid: '',
                user_id: 0,
                table: '',
                table_name: '',
                tag: '',
                file_path: files[j].file_localpath,
                file_name: '',
                file_localpath: files[j].file_localpath,
                status: status,
                progress: 100
              }
              let house = that.data.house;
              if (house.img_url == '') {
                house.img_url = data.data[0].file_path;
                that.setData({
                  house: house
                });
              }
              that.setData({
                files: files
              })
            },
            fail(res) {
              const data = res.data
              console.log(res);
              let files = that.data.files;
              files[j] = {
                uuid: '',
                user_id: 0,
                table: '',
                table_name: '',
                tag: '',
                file_path: files[j].file_localpath,
                file_name: '',
                file_localpath: files[j].file_localpath,
                status: 0,
                progress: 100
              }
              that.setData({
                files: files
              })
            }
          })
          uploadTask.onProgressUpdate((res) => {
            var files = that.data.files;
            if (res.progress == 100) {
              files[j].status = 1;
            } else {
              files[j].status = -1;
            }
            files[j].progress = res.progress;
            that.setData({
              files: files
            })
            console.log('上传进度', res.progress)
            console.log('已经上传的数据长度', res.totalBytesSent)
            console.log('预期需要上传的数据总长度', res.totalBytesExpectedToSend)
          })
        }
      }
    })
  },

  bindcancel: function (e) {

    if (this.data.lsdata.mpIndex || this.data.lsdata.areaIndex) {
      this.setData(
        this.data.lsdata
      );
      this.setData({
        lsdata: {}
      })
    }
  },
  bindconfirm: function (e) {
    console.log("点击确认按钮")
    this.setData({
      lsdata: {}
    })

  },

  bindMultiPickerMPChange: function (e) {

    var that = this;
    let mydata = {
      mpArray: this.data.mpArray,
      mpArrayValue: this.data.mpArrayValue,
      mpStatusArray: this.data.mpStatusArray,
      uuidsArray: this.data.uuidsArray,
      mpIndex: this.data.mpIndex,
      mpInfo: this.data.mpInfo,
      house: this.data.house,
    };
    if (!that.data.lsdata.mpIndex) {
      this.setData({
        lsdata: JSON.parse(JSON.stringify(mydata))
      });
    }
    switch (e.detail.column) {
      case 0:
        //选择有效的幢
        mydata.mpIndex[1] = 0;
        if (e.detail.value > 0) {
          mydata.mpIndex[0] = e.detail.value;
          http.get('house/community/building/room', {
            area_location_id: that.data.house.area_location_id,
            id: that.data.mpArrayValue[0][e.detail.value]
          }, res => {
            if (res.data.success == 1) {
              let arrayA = ['请选择'];
              let arrayB = ['0'];
              let arrayC = ['0'];
              let arrayD = ['0']
              res.data.data.map(function (d) {
                arrayA.push(d.name);
                arrayB.push(d.id);
                arrayC.push((d.status === null ? 0 : d.status));
                arrayD.push((d.uuid === null ? 0 : d.uuid));
              });
              mydata.mpArray[1] = arrayA;
              mydata.mpArrayValue[1] = arrayB;
              mydata.mpStatusArray = arrayC;
              mydata.uuidsArray = arrayD;
              this.setData(
                mydata
              )
            }
          })
        } else {
          mydata.mpIndex[0] = 0;
          let arrayA = ['请选择'];
          let arrayB = ['0'];
          let arrayC = ['0'];
          mydata.mpArray[1] = arrayA;
          mydata.mpArrayValue[1] = arrayB;
          mydata.mpStatusArray = arrayC;
          mydata.uuidsArray = arrayC;
          this.setData(
            mydata
          )
        }
        break;
      case 1:
        mydata.mpIndex[1] = e.detail.value;
        mydata.house.address_id = that.data.mpArrayValue[1][e.detail.value];
        console.log("门牌是id" + that.data.mpArrayValue[1][e.detail.value])
        mydata.house.address_status = that.data.mpStatusArray[e.detail.value];
        console.log("状态是" + mydata.house.address_status)
        mydata.house.address = that.data.mpArray[0][that.data.mpIndex[0]] + that.data.mpArray[1][that.data.mpIndex[1]];
        console.log("楼和门牌是" + mydata.house.address);
        mydata.house.building_id = that.data.mpArrayValue[0][that.data.mpIndex[0]];
        mydata.house.house_uuid =  that.data.uuidsArray[e.detail.value];
        console.log("uuid是" + mydata.house.house_uuid);
        this.setData(
          mydata
        )
        break;

        //   house.address = e.detail.value;

    }
  },
  bindMultiPickerColumnChange: function (e) {
    var that = this;
    var data = {
      areaArray: this.data.areaArray,
      areaArrayValue: this.data.areaArrayValue,
      areaIndex: this.data.areaIndex,
      areaInfo: this.data.areaInfo,
      house: that.data.house,
    };
    if (!that.data.lsdata.areaIndex) {
      this.setData({
        lsdata: JSON.parse(JSON.stringify(data))
      });
    }
    var areaData = that.data.areaData;
    data.areaIndex[e.detail.column] = e.detail.value;
    switch (e.detail.column) {
      case 0:
        if (e.detail.value > 0) {
          //第一层
          data.areaIndex[0] = e.detail.value;
          //第二层
          data.areaIndex[1] = 0;
          if (areaData && areaData.length > 0) {
            var lvl2 = this.getAreaList(areaData[e.detail.value - 1]);
            var lvl2Name = [that.data.areaArrayFirstName];
            lvl2.map(function (d) {
              return lvl2Name.push(d.name);
            });
            var lvl2Value = [0];
            lvl2.map(function (d) {
              return lvl2Value.push(d.id);
            });
            data.areaArray[1] = lvl2Name;
            data.areaArrayValue[1] = lvl2Value;
          } else {
            data.areaArray[1] = [that.data.areaArrayFirstName];
            data.areaArrayValue[1] = [0];
          }
          //第三层
          data.areaArray[2] = [that.data.areaArrayFirstName];
          data.areaIndex[2] = 0;
          //第四层
          data.areaArray[3] = [that.data.areaArrayFirstName];
          data.areaIndex[3] = 0;
        } else {
          //第二层
          data.areaArray[1] = [that.data.areaArrayFirstName];
          data.areaIndex[1] = 0;
          //第三层
          data.areaArray[2] = [that.data.areaArrayFirstName];
          data.areaIndex[2] = 0;
          //第四层
          data.areaArray[3] = [that.data.areaArrayFirstName];
          data.areaIndex[3] = 0;
        }
        break;
      case 1:
        //第一层

        if (e.detail.value > 0) {
          //第二层
          data.areaIndex[1] = e.detail.value;
          //第三层
          data.areaIndex[2] = 0;
          if (areaData[data.areaIndex[0] - 1].children && areaData[data.areaIndex[0] - 1].children.length > 0) {
            var lvl3 = this.getAreaList(areaData[data.areaIndex[0] - 1].children[e.detail.value - 1]);
            var lvl3Name = [that.data.areaArrayFirstName];
            lvl3.map(function (d) {
              return lvl3Name.push(d.name);
            });
            var lvl3Value = [0];
            lvl3.map(function (d) {
              return lvl3Value.push(d.id);
            });
            data.areaArray[2] = lvl3Name;
            data.areaArrayValue[2] = lvl3Value;
          } else {
            data.areaArray[2] = [that.data.areaArrayFirstName];
            data.areaArrayValue[2] = [0];
          }
          //第四层
          data.areaArray[3] = [that.data.areaArrayFirstName];
          data.areaIndex[3] = 0;
        } else {
          //第三层
          data.areaArray[2] = [that.data.areaArrayFirstName];
          data.areaIndex[2] = 0;
          //第四层
          data.areaArray[3] = [that.data.areaArrayFirstName];
          data.areaIndex[3] = 0;
        }
        break;
      case 2:
        //第一层

        //第二层

        //第三层
        if (e.detail.value > 0) {
          data.areaIndex[2] = e.detail.value;
          //第四层
          var lvl4 = this.getAreaList(areaData[data.areaIndex[0] - 1].children[data.areaIndex[1] - 1].children[data.areaIndex[2] - 1]);
          var lvl4Name = [that.data.areaArrayFirstName];
          lvl4.map(function (d) {
            return lvl4Name.push(d.name);
          });
          var lvl4Value = [0];
          lvl4.map(function (d) {
            return lvl4Value.push(d.id);
          });
          data.areaArray[3] = lvl4Name;
          data.areaArrayValue[3] = lvl4Value;
          data.areaIndex[3] = 0;
        } else {
          var lvl4Name = [that.data.areaArrayFirstName];
          data.areaArray[3] = lvl4Name;
          data.areaArrayValue[3] = [0];
          data.areaIndex[3] = 0;
        }
        break;
      case 3:
        //第一层

        //第二层

        //第三层

        //第四层
        data.areaIndex[3] = e.detail.value;
        break;
    }
    data.areaInfo = [{
      name: data.areaArray[0][data.areaIndex[0]]
    }, {
      name: data.areaArray[1][data.areaIndex[1]]
    }, {
      name: data.areaArray[2][data.areaIndex[2]]
    }, {
      name: data.areaArray[3][data.areaIndex[3]]
    }];
    

    if (e.detail.column == 3) {
      //获取到小区id
      data.house.area_location_id = data.areaArrayValue[3][data.areaIndex[3]];
      //小区id带出小区的幢
      data.house.area_location_name =
      data.areaArray[0][data.areaIndex[0]] + "/" +
       data.areaArray[1][data.areaIndex[1]] + "/" +
       data.areaArray[2][data.areaIndex[2]] + "/" +
        data.areaArray[3][data.areaIndex[3]];
    } else {
      data.house.area_location_id = 0;
      data.house.area_location_name = '';
    }
    data.house.address = '';
    data.house.address_id = 0;
    this.setData(data);
    console.log(data);
  },
  // getZhuangList: function(xqID) {

  //   var data = {
  //     mpArray:  [
  //       [],
  //       []
  //     ]
  //   }

  //   if(xqID){
  //     wx.request({
  //       method: 'GET',
  //       url: url + '/house/community/building?area_location_id=' + xqID,
  //       header: {
  //         token: wx.getStorageSync('token')
  //       },
  //       success(res) {
  //         if (res.data.success == 1) {
  //           var data = {
  //             house: that.data.house
  //           }
  //           var house = [];
  //           for (let i = 0; i < res.data.data.data_list.length; i++) {
  //             if (res.data.data.data_list[i].img_url == null || res.data.data.data_list[i].img_url == '') {
  //               res.data.data.data_list[i].img_url = '/images/image_null.png';
  //             } else {
  //               res.data.data.data_list[i].img_url = url + res.data.data.data_list[i].img_url;
  //             }
  //             house.push(res.data.data.data_list[i]);
  //           }
  //           data.house = house;
  //           that.setData(data);
  //         } else {
  //           wx.showToast({
  //             title: res.data.dsc,
  //             icon: 'none',
  //             duration: 2000
  //           })
  //         }
  //     mpArray[0] = 
  //   }else{

  //   }
  //   return 
  // },

  getAreaList: function (data) {
    if (!data.children) return [{
      name: '',
      code: data.code
    }]; // 有可能某些县级市没有区
    return this.areaFormat(data.children);
  },
  areaFormat: function (data) {
    var result = [];
    for (var i = 0; i < data.length; i++) {
      var d = data[i];
      if (/^请选择|全部/.test(d.name)) continue;
      result.push(d);
    }
    if (result.length) return result;
    return [];
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

 
})