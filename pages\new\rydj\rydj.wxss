/* pages/new/zkdj/zkdj.wxss */
/* pages/building/visit.wxss */
.container{
  background: #fff;
}
.title{
  font-size: 18px;
  font-weight: bold;
  text-align: left;
  padding: 0 5px;
}
.title_list{
  padding: 10px 5px;
  text-align: center;
  font-weight: 700;
  color: rgb(0, 140, 255);
}
.btn-area{
width: 95%;

  position: absolute;
  /* bottom: 1px; */
  margin:10px;
}
.red{
  color:red;
}
.textaddress{
  border:1px solid #ccc;
  margin:0 auto;
  padding: 10px;
}
.ipt{
  border-bottom:1px solid #ccc;
  margin:10px;
  padding: 10px;
}
.texrar {
  width: 90%;
  /* height: 50px; */
}
.section{
  border:1px solid #ccc;
  margin:10px;
  padding: 10px
}
.load-name {
  height: 80rpx;
  line-height: 80rpx;
  font-size: 30rpx;
}
.load-box {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
.img-item, .img-add {
  position: relative;
  width: 140rpx;
  height: 140rpx;
  margin: 20rpx;
}
.img-add {
  border: 1px solid #ccc;
}
.img-add:after{
  width: 1rpx;
  height: 50rpx;
  content: " ";
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  background-color: #ccc;
}
.img-add:before{
  position: absolute;
  top: 50%;
  right: 31%;
  width: 50rpx;
  height: 1rpx;
  content: " ";
  display: inline-block;
  background-color: #ccc;  
}

.img-item {
  margin-right: 20rpx;
}
.img-item image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.icon {
  position: absolute;
  top: 0;
  right: 0;
}