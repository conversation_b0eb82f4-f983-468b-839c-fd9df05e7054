<!--pages/new/shop/info.wxml-->
<watermark></watermark>
<wxs module="splits" src="./declareRecord.wxs"></wxs>
<view class="page">
	<view class="weui-search-bar">
		<view class="weui-search-bar__form">
			<view class="weui-search-bar__box">
				<icon class="weui-icon-search_in-box" type="search" size="14"></icon>
				<input type="text" class="weui-search-bar__input" placeholder="楼栋/房屋门牌号" value="{{inputVal}}" focus="{{inputShowed}}" bindinput="inputTyping" bindconfirm="searchHouse" />
				<view class="weui-icon-clear" wx:if="{{inputVal.length > 0}}" bindtap="clearInput">
					<icon type="clear" size="14"></icon>
				</view>
			</view>
			<label class="weui-search-bar__label" hidden="{{inputShowed}}" bindtap="showInput">
				<icon class="weui-icon-search" type="search" size="14"></icon>
				<view class="weui-search-bar__text">模糊搜索</view>
			</label>
		</view>
		<view class="weui-search-bar__cancel-btn" hidden="{{!inputShowed}}" bindtap="hideInput">取消</view>
	</view>
	<!-- tab导航栏 -->
	<!-- scroll-left属性可以控制滚动条位置 -->
	<!-- scroll-with-animation滚动添加动画过渡 -->
	<scroll-view scroll-x="true" class="top-tab" scroll-left="{{topTab.curScroolLeft}}" scroll-with-animation="{{true}}">
		<block wx:for="{{topTab.tabs}}" wx:for-index="idx" wx:for-item="topTabItem" wx:key="idx">
			<view class="top-tab-item {{topTab.curIndex == idx ?'active':''}}" data-current="{{idx}}" bindtap="switchTopTab">{{topTabItem.name}}</view>
		</block>
	</scroll-view>
	<!-- 页面内容 -->
	<swiper class="top-tab-content" current="{{topTab.curIndex}}" duration="200" bindchange="switchTopTabItem">
		<swiper-item wx:for="{{topTab.tabs}}" wx:for-item="tabItem" wx:for-index="idx" wx:key="idx" class="top-tab-content-item">
			<scroll-view scroll-y="true" style="height:100%; " bindscrolltolower="scrollChange">
				<view>
					<view class="weui-panel weui-panel_access myweui-row">
						<view class="weui-panel__bd myweui-row-body">
							<block wx:for="{{apply[idx]}}" wx:for-item="item" wx:key="this">
								<navigator  bindlongpress='test'  bindtap="bindUrl" data-table_uuid="{{item.table_uuid}}" data-table="{{item.table}}" data-uuid="{{item.uuid}}" class="weui-media-box weui-media-box_appmsg" hover-class="weui-cell_active {{tabItem.name}}">
									<view wx:if="{{item.tag=='房屋'}}" style="border-color:transparent transparent #0fb3aa;"   class="corner-tag">
										<label class="corner-tag-text">{{item.tag}}</label>
									</view>
										<view wx:if="{{item.tag=='租客'}}" style="border-color:transparent transparent #187de8;"   class="corner-tag">
										<label class="corner-tag-text">{{item.tag}}</label>
									</view>
									<view wx:if="{{item.tag=='续租'}}" style="border-color:transparent transparent #b3710f;"   class="corner-tag">
										<label class="corner-tag-text">{{item.tag}}</label>
									</view>
											<view wx:if="{{item.tag=='家人'}}" style="border-color:transparent transparent #ce7d4f;"   class="corner-tag">
										<label class="corner-tag-text">{{item.tag}}</label>
									</view>
									<view class="weui-media-box__hd weui-media-box__hd_in-appmsg">
										<image class="weui-media-box__thumb" src="{{item.img_url}}" />
									</view>
									<view class="weui-media-box__bd weui-media-box__bd_in-appmsg">
										<view class="weui-media-box__title">{{ splits.splitAddress(item)}}
										</view>
										<view class="weui-media-box__desc">
											<label class='product-sku'>{{item.apply_info}}</label>
										</view>
										<view class="weui-media-box__info">
											<view wx:if="{{item.status==-2}}" class="weui-media-box__info__meta">
												<label class="myweui-tag myweui-tag-yellow">待提交</label>
											</view>
											<view wx:if="{{item.status==-1}}" class="weui-media-box__info__meta">
												<label class="myweui-tag myweui-tag-gray">待审核</label>
											</view>
											<view wx:if="{{item.status==1}}" class="weui-media-box__info__meta">
												<label class="myweui-tag myweui-tag-green">已通过</label>
											</view>
											<view wx:if="{{item.status==0}}" class="weui-media-box__info__meta">
												<label class="myweui-tag myweui-tag-red">未通过</label>
											</view>
											<view class="weui-media-box__info__meta weui-media-box__info__meta_extra">
												<label class="myweui-tag">{{item.intime}}</label>
											</view>
										</view>
									</view>
									<view class="weui-cell__ft weui-cell__ft_in-access"></view>
								</navigator>
							</block>
						</view>
					</view>
          		<view class="weui-loadmore">
								<view class="weui-loadmore__tips weui-loadmore__tips_in-line">
									{{loadingMessage[idx]}}</view>
							</view>
							<view style="height:150px;"></view>
				</view>

			</scroll-view>
		</swiper-item>
	</swiper>
</view>