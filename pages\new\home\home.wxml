<view>
  <watermark></watermark>
  <view>
    <swiper style='height:{{SwiperHeight}};' indicator-dots="{{indicatorDots}}" autoplay="{{autoplay}}"
      interval="{{interval}}" duration="{{duration}}">
      <block>
        <swiper-item wx:for="{{imgs}}" wx:for-index="idx" wx:for-item="img" wx:key="idx">
          <image data-bgcolor="#ffffff" data-fontcolor="#000000" data-type="h5" bindtap="toPage"
            data-url="{{img.target_url}}" src="{{img.url}}" class="slide-image" bindload='bindSwiperHeight' />
        </swiper-item>

      </block>
    </swiper>
  </view>
  <!-- <view class="gift-div" style="" bindtap="goPrize">
    <view class="gift">
      <image  style="width:40rpx;height:40rpx;" src="../../../images/lw.png" mode="widthFix"></image>
    </view>
    {{lottery == '1' ? '  我的奖品' : ' 领取奖品'}}
  </view> -->
  <view class="page__bd page__bd_spacing">
    <view class="weui-panel weui-panel_access myweui-row" style="display:none;">
      <view class="weui-panel__hd">最新通告</view>
      <view class="weui-panel__bd myweui-row-body">
        <navigator url="/pages/mall/product/view?guid=35E1E7A7-0CA1-4E22-BAD6-C49040DB08D4"
          class="weui-media-box weui-media-box_appmsg" hover-class="weui-cell_active">
          <view class="weui-media-box__hd weui-media-box__hd_in-appmsg">
            <!-- <image class="weui-media-box__thumb" src="/images/gongantoutiao.png" /> -->
          </view>
          <view class="weui-media-box__bd weui-media-box__bd_in-appmsg">
            <view class="weui-media-box__title">申报流程说明
            </view>
            <view class="weui-media-box__desc">
              <label class='product-sku'>经开公安自主申报系统正式上线 </label>
            </view>
          </view>
        </navigator>
      </view>
    </view>

    <view class="weui-panel weui-panel_access">

      <view class="weui-panel__bd">

        <view style="height:25px" wx:if="{{ noticeList && noticeList.length }}" data-bgcolor="#ffffff" bindtap="toPage"
          data-url="https://zzsbui.jxjkga.cn/#/notice/sendtomenoticelist" data-fontcolor="#000000" data-title="通知公告"
          data-type="h5" class="weui-media-box weui-media-box_appmsg" hover-class="weui-cell_active">
          <view class="weui-media-box__hd weui-media-box__hd_in-appmsg">
            <image style="width:42px;height:40px;margin-top:10px" class="weui-media-box__thumb"
              src="/images/tzButton.png" />
          </view>
          <view class="weui-media-box__bd weui-media-box__bd_in-appmsg">
            <!-- <view  style="font-size:12px" class="weui-media-box__title">【重要通知】明天天气不怎么样</view> -->
            <view wx:for="{{ noticeList }}" wx:key="index" style="font-size:12px" class="weui-media-box__title">
              {{'【公告通知】' + item.title }}</view>
          </view>
          <view class="weui-cell__ft weui-cell__ft_in-access"></view>
        </view>
        <!-- 测试 -->
      </view>
    </view>

    <view class="weui-panel weui-panel_access">
      <view class="weui-panel__hd">自主申报</view>
      <view class="weui-panel__bd">
        <!-- <view style="height:20px" bindtap="toPage" data-bgcolor="#ffffff" data-fontcolor="#000000" data-title="嘉兴第十八届家博会预约入口" data-url="https://zzsbui.jxjkga.cn/#/d18jiabohui" data-type="h5" class="weui-media-box weui-media-box_appmsg" hover-class="weui-cell_active">
          <view class="weui-media-box__hd weui-media-box__hd_in-appmsg">
            <image style="width:35px;height:35px;margin-top:12px" class="weui-media-box__thumb" src="/images/bolanhui.png" />
          </view>
          <view class="weui-media-box__bd weui-media-box__bd_in-appmsg">
            <view style="color:red" class="weui-media-box__title">嘉兴第十八届家博会预约入口</view> 
          </view>
          <view class="weui-cell__ft weui-cell__ft_in-access"></view>
        </view> -->
        <view bindtap="toPage" data-bgcolor="#ffffff" data-fontcolor="#000000" data-title="商铺自主申报"
          data-url="https://zzsbui.jxjkga.cn/#/dpygzzsb" data-type="h5" class="weui-media-box weui-media-box_appmsg"
          hover-class="weui-cell_active">
          <view class="weui-media-box__hd weui-media-box__hd_in-appmsg">
            <image class="weui-media-box__thumb" src="/images/dianpuButton.png" />
          </view>
          <view class="weui-media-box__bd weui-media-box__bd_in-appmsg">
            <view style="  font-weight: 800;" class="weui-media-box__title">店铺自主申报</view>
            <view class="weui-media-box__desc">商铺业主信息申报，员工信息申报</view>
          </view>
          <view class="weui-cell__ft weui-cell__ft_in-access"></view>
        </view>

        <view bindtap="toPage" data-bgcolor="#ffffff" data-fontcolor="#000000" data-title="小区自主申报"
          data-url="https://zzsbui.jxjkga.cn/#/xqzzsb" data-type="h5" class="weui-media-box weui-media-box_appmsg"
          hover-class="weui-cell_active">
          <view class="weui-media-box__hd weui-media-box__hd_in-appmsg">
            <image class="weui-media-box__thumb" src="/images/xiaoquButton.png" />
          </view>
          <view class="weui-media-box__bd weui-media-box__bd_in-appmsg">
            <view style="  font-weight: 800;" class="weui-media-box__title">小区自主申报</view>
            <view class="weui-media-box__desc">小区业主房东信息申报，租客信息申报</view>
          </view>
          <view class="weui-cell__ft weui-cell__ft_in-access"></view>
        </view>
        <view bindtap="toPage" data-bgcolor="#ffffff" data-fontcolor="#000000" data-title="企事业自主申报"
          data-url="https://zzsbui.jxjkga.cn/#/qyzzsb" data-type="h5" class="weui-media-box weui-media-box_appmsg"
          hover-class="weui-cell_active">
          <view class="weui-media-box__hd weui-media-box__hd_in-appmsg">
            <image class="weui-media-box__thumb" src="/images/qiyeButton.png" />
          </view>
          <view class="weui-media-box__bd weui-media-box__bd_in-appmsg">
            <view style="  font-weight: 800;" class="weui-media-box__title">企事业自主申报</view>
            <view class="weui-media-box__desc">企事业单位信息申报，职员信息申报</view>
          </view>
          <view class="weui-cell__ft weui-cell__ft_in-access"></view>
        </view>
        <view bindtap="toPage" wx:if="{{wgyr_status==1}}" data-bgcolor="#ffffff" data-fontcolor="#000000"
          data-title="境外人员申报" data-url="https://zzsbui.jxjkga.cn/#/jwzzsb" data-type="h5"
          class="weui-media-box weui-media-box_appmsg" hover-class="weui-cell_active">
          <view class="weui-media-box__hd weui-media-box__hd_in-appmsg">
            <image class="weui-media-box__thumb" src="/images/wgyrzzsbButton.png" />
          </view>
          <view class="weui-media-box__bd weui-media-box__bd_in-appmsg">
            <view style="  font-weight: 800;" class="weui-media-box__title">境外人员申报</view>
            <view class="weui-media-box__desc">帮助境外人员进行各项申报</view>
          </view>
          <view class="weui-cell__ft weui-cell__ft_in-access"></view>
        </view>
      </view>
    </view>
    <!-- <view class='rhasta' style="display:none;">
      <view class='rhasta-item'>
        <view class="rhasta-item-row" style="border:0;">
          自主申报
        </view>
        <view class='rhasta-grid-line rhasta-item-line'>
          <view class='rhasta-grid' bindtap="toPage" data-bgcolor="#ffffff" data-fontcolor="#000000" data-title="商铺自主申报"
            data-url="https://www.51sexmall.com/#/dpygzzsb" data-type="h5">
            <view class='rhasta-grid-image'>
              <image src='/images/dianpu.png'>
              </image>
            </view>
            <label>店铺申报</label>
          </view>
          <view class='rhasta-grid' bindtap="bindHouseSelectRole">
            <view class='rhasta-grid-image'>
              <image src='/images/xiaoqu.png'>
              </image>
            </view>
            <label>小区申报</label>
          </view>
          <view class='rhasta-grid' bindtap="doLogin">
            <view class='rhasta-grid-image'>
              <image src='/images/danwei.png'>
              </image>
            </view>
            <label>企事业单位申报</label>
          </view>
        </view>
      </view>
    </view> -->
  </view>
</view>