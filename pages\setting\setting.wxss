/* pages/login/login.wxss */
.btn{
  width: 80%;
  margin: 0 auto;
  margin-top: 60rpx;
  border-radius: 10px;
}
.from-list{
  width: 84%;
  margin: 0 auto;
  display: flex;
  font-size: 16px;
  margin-bottom: 20px;
}
.from-list text{
  height: 30px;
  line-height: 30px;
  width: 20%;
}
.from-list input{
  border-bottom: 1px solid #ccc;
  height: 30px;
  width: 80%;
}
.from-list .man{
  margin-right: 15px;
}
.from-list input#vcode{
  width: 40%;
}
.from-list button{
  height: 30px;
  line-height: 30px;
  border-radius: 0px; 
  font-size: 14px;
}
.tips{
  width: 80%;
  margin: 0 auto;
  font-size: 24rpx;
  display: flex;
  flex-direction: row;
  margin-top: 30rpx;
}
.tips .checkbox{
  zoom: 0.5;
  position: relative;
  top: -3px;
}
.tips navigator{
  color: #7192fd;
}