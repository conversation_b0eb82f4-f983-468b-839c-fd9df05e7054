
var host = getApp().globalData.requestUrl;

function post(url, data, success) {
  wx.request({
    url: host+'/storemgmt/'+url,
    header: {
      'content-type': 'application/x-www-form-urlencoded',
      "accept": "application/json",
      'token': wx.getStorageSync('token'),
    },
    method: 'POST',
    data: data,
    success(res) {
      if(res.data.success != 1){
        wx.showToast({
          title: '接口['+url+']调用失败：'+res.data.dsc,
          icon: 'none',
          duration: 2000
        })
        success(res);
      }else{
        success(res);
      }
    },
    fail(res) {
      wx.showToast({
        title: '接口['+url+']调用失败：',
        icon: 'none',
        duration: 2000
      })
    }
  });
}

function jsonpost(url, data, success) {
  wx.request({
    url: host+'/storemgmt/'+url,
    header: {
      'content-type': 'application/json', 
      'token': wx.getStorageSync('token')
    },
    method: 'POST',
    data: data,
    success(res) {
      if(res.data.success != 1){
        wx.showToast({
          title: '接口['+url+']调用失败：'+res.data.dsc,
          icon: 'none',
          duration: 2000
        })
        success(res);
      }else{
        success(res);
      }
    },
    fail(res) {
      wx.showToast({
        title: '接口['+url+']调用失败：',
        icon: 'none',
        duration: 2000
      })
    }
  });
}

function get(url, data, success) {
  wx.request({
    url: host+'/storemgmt/'+url,
    header: {
      'content-type': 'application/json',
      'token': wx.getStorageSync('token'),
    },
    method: 'GET',
    data: data,
    success(res) {
      if(res.data.success != 1){
        wx.showToast({
          title: '接口['+url+']调用失败：'+res.data.dsc,
          icon: 'none',
          duration: 2000
        })
        success(res);
      }else{
        success(res);
      }
    },
    fail(res) {
      console.log(res,'報錯調試----fail********************************************');
      wx.showToast({
        title: '接口['+url+']调用失败',
        icon: 'none',
        duration: 2000
      })
    }
  });
}

module.exports = {
  post: post,
  get:get,
  jsonpost:jsonpost
  }