<!--pages/new/zkdj/zkdj.wxml-->
<!--pages/building/visit.wxml-->
<view class="container" style="background:#fff;">
  <watermark></watermark>
  <view class="title">自主报送信息</view>
  <view>
    <form catchsubmit="formSubmit">
      <view><span class="red">*</span>1.请选择日期</view>
      <view class="section">
        <picker mode="date" value="{{rent_unrent_time}}" name="rent_unrent_time" bindchange="bindDateChange">
          <view class="picker">
            当前选择: {{rent_unrent_time}}
          </view>
        </picker>
      </view>
      <view><span class="red">*</span>2.房屋性质</view>
      <view class="section">
        <radio-group name="house_type" bindchange="radioChange">
          <label class="weui-cell weui-check__label">
            <radio value="1" />自住房
          </label>
          <label class="weui-cell weui-check__label">
            <radio value="2" />出租房
          </label>
        </radio-group>
      </view>
      <view><span class="red">*</span>3.请上传图片：租房合同拍照上传【自住房拍门牌号】不是拍身份证照片</view>
      <view class='load-img'>
        <view class='load-box'>
          <view class='img-item' wx:for="{{rent_contract_img_file}}" wx:key="index">
            <image src="{{item.path}}" data-src="{{item}}" mode="aspectFill" data-list="{{rent_contract_img_file}}"></image>
            <icon class='icon' type="clear" size="20" color='#EF4444' catchtap='_onDelTab' data-idx="{{index}}" wx:if="{{!prevent}}" />
          </view>
          <image class='img-add' bindtap='getrent' wx:if="{{rent_contract_img_file.length<1}}"></image>
        </view>
      </view>

      <view><span class="red">*</span>4.请上传图片：租客【居民】用手机自拍头部一寸照片</view>
      <view class='load-img'>
        <view class='load-box'>
          <view class='img-item' wx:for="{{face_img_file}}" wx:key="index">
            <image src="{{item.path}}" data-src="{{item}}" mode="aspectFill" data-list="{{face_img_file}}"></image>
            <icon class='icon' type="clear" size="20" color='#EF4444' catchtap='_onDelFace' data-idx="{{index}}" wx:if="{{!prevent}}" />
          </view>
          <image class='img-add' bindtap='getface' wx:if="{{face_img_file.length<1}}"></image>
        </view>
      </view>
      <view><span class="red">*</span>5.姓名：</view>
      <view>
        <input name="name" value="{{name}}" bindinput="getname" class="ipt" placeholder="" />
      </view>
      <view><span class="red">*</span>6.身份证号码</view>
      <view>
        <input name="identity" value="{{identity}}" bindinput="getidentity" class="ipt" type="idcard" placeholder="" />
      </view>
      <view><span class="red">*</span>7.请输入您的手机号码：</view>
      <view>
        <input name="phone" value="{{phone}}" bindinput="getphone" class="ipt" type="number" placeholder="" />
      </view>
      <view><span class="red">*</span>8.请选择您所在的社区：</view>
      <view class="section">
        <picker mode="multiSelector" bindchange="bindMultiPickerChange" bindcolumnchange="bindMultiPickerColumnChange" range="{{defaultarray}}" value="{{multiIndex}}" range-key="text">
          已选中：{{area_location_name}}
        </picker>
      </view>
      <view><span class="red">*</span>9.现详细住址如：晴湾几幢几零几室（要填到几幢几零几）</view>
      <view>
        <textarea name="detail_address" value="{{detail_address}}" bindinput="getaddress" class="textaddress" />
      </view>

      <view class="btn-area">
        <button style="margin: 30rpx 0;background:#0081ff;color:#fff;" formType="submit" disabled="{{disabled}}">提交</button>
      </view>
    </form>
  </view>
</view>