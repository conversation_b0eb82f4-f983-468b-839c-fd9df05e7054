 Component({
   data: {
     title: "用户隐私保护提示",
     desc1: "感谢您使用嘉兴经开公安自主申报小程序",
     urlTitle: "《用户隐私保护指引》",
     desc2: "当您点击同意并开始时用产品服务时，即表示你已理解并同息该条款内容，该条款将对您产生法律约束力。如您拒绝，将无法进入嘉兴经开公安自主申报小程序。",
     innerShow: false,
     height: 50,
   },
   lifetimes: {
     attached: function () {
       wx.getPrivacySetting({
         success: res => {
           console.log("是否需要授权：", res.needAuthorization, "隐私协议的名称为：", res.privacyContractName)
           if (res.needAuthorization) {
             this.popUp()
           } else {
             this.triggerEvent("agree")
           }
         },
         fail: () => {},
         complete: () => {},
       })
     },
   },
   methods: {
     handleDisagree(e) {
       this.triggerEvent("disagree")
       this.disPopUp()
     },
     handleAgree(e) {
       console.log(e, '点击同意按钮后的数据返回');
       this.triggerEvent("agree")

       this.disPopUp()
     },
     popUp() {
       this.setData({
         innerShow: true
       })
     },
     disPopUp() {
       this.setData({
         innerShow: false
       })
     },
     handleOpenPrivacyContract() {
       // 打开隐私协议页面
       wx.openPrivacyContract({
         success: () => {}, // 打开成功
         fail: () => {}, // 打开失败
         complete: () => {}
       })
     },
   }
 })