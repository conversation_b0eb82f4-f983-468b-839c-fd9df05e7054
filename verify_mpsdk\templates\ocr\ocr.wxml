<template name="verifyOcr"><view class="verify-gray-container"><view wx:if="{{!page.ocr.isManualInput && !ocr.isShowTakePhoto && !ocr.isShowResult}}" class="verify-white-bg"><view id="ocr-top"><view id="ocr-title">身份证识别</view><image src="{{Common.IsWxNative ? '/verify_mpsdk/images/hint-shibieNative.png' : 'https://s.beta.gtimg.com/GodIdent/huiyan-ui-new/images-wx/hint-shibie-green.png'}}"></image><view id="ocr-text"><text class="text" wx:if="{{page.ocr.backend}}">1. 拍摄身份证原件正反面</text><text class="text" wx:else>1. 拍摄身份证原件正面</text><text class="text">2. 保证照片清晰文字可辨</text><text class="text">3. 避免照片不全、遮挡、反光</text></view><view id="ocr-startbtn" class="{{Common.IsWxNative ? 'index-btnNative' : 'index-btn'}}"><button type="primary" bindtap="ocrStartTakePhoto" hover-class="{{Common.IsWxNative ? 'btn-hoverNative' : 'btn-hover'}}">开始识别</button></view></view></view><view wx:if="{{ocr.isShowTakePhoto && !ocr.isShowResult}}" class="verify-absolute-bg"><camera device-position="back" flash="off" binderror="ocrCameraError" class="verify-absolute-bg" wx:if="{{!ocr.isShowPhotoPreView}}"><cover-view class="verify-ocr-title {{ocr.isInfinityDisplayOcrTitle}}"><cover-view class="{{Common.IsWxNative ? 'color-green' : ''}}">{{ocr.ocrTitle}}</cover-view></cover-view><cover-view class="ocr-camera-left {{ocr.isInfinityDisplayOcrMiddle}}"></cover-view><cover-view class="ocr-camera-right {{ocr.isInfinityDisplayOcrMiddle}}"></cover-view><cover-view class="verify-ocr-frameArea {{ocr.isInfinityDisplayOcrMiddle}}"><cover-view class="vof-bg" wx:if="{{!ocr.isShowPhotoPreView}}"><cover-image src="{{Common.IsWxNative ? '/verify_mpsdk/images/shibiebgNative.png' : 'http://beta.gtimg.com/GodIdent/huiyan/img/shibiebg.png'}}" class="full-weight-height"></cover-image></cover-view><cover-view wx:if="{{!ocr.isShowPhotoPreView && ocr.isFrontIdCard}}" style="margin-top:80rpx;margin-left:350rpx;"><cover-image src="http://beta.gtimg.com/GodIdent/huiyan/img/ico-head.png" style="width:254rpx;height:285rpx;"></cover-image></cover-view><cover-view wx:if="{{!ocr.isShowPhotoPreView && !ocr.isFrontIdCard}}" style="margin-top:80rpx;margin-left:90rpx;"><cover-image src="http://beta.gtimg.com/GodIdent/huiyan/img/ico-guohui.png" style="width:139rpx;height:144rpx:;"></cover-image></cover-view></cover-view><cover-view class="verify-ocr-pre {{ocr.isInfinityDisplayOcrBottom}}"><cover-view class="verify-ocr-tools" wx:if="{{ocr.isToolsShow}}"><cover-view class="{{Common.IsWxNative ? 'vot-album color-green' : 'vot-album'}}" bindtap="chooseImg">相册</cover-view><cover-view class="vot-middle"><cover-image src="{{Common.IsWxNative ? '/verify_mpsdk/images/ico-cameNative.png' : '/verify_mpsdk/images/ico-came.png'}}" wx:if="{{!ocr.isShowPhotoPreView}}" bindtap="takePhotoWithCamera"></cover-image></cover-view></cover-view></cover-view></camera><view wx:if="{{ocr.isShowPhotoPreView}}"><view class="verify-ocr-title" style="background:none"><view class="{{Common.IsWxNative ? 'color-green' : ''}}">{{ocr.ocrTitle}}</view></view><image src="{{ocr.tempImagePath}}" mode="aspectFit" id="vof-preview"></image><view class="verify-ocr-pre"><view class="verify-ocr-tools" wx:if="{{ocr.isToolsShow}}"><view class="{{Common.IsWxNative ? 'vot-album color-green' : 'vot-album'}}" bindtap="chooseImg">相册</view><view class="vot-middle"><image src="{{Common.IsWxNative ? '/verify_mpsdk/images/ico-sucNative.png' : '/verify_mpsdk/images/ico-suc.png'}}" wx:if="{{ocr.isShowPhotoPreView}}" bindtap="startUploadAndOcr"></image></view><view wx:if="{{ocr.isShowPhotoPreView}}" class="{{Common.IsWxNative ? 'vot-right color-green' : 'vot-right'}}" bindtap="reTakePhoto">重拍</view></view></view></view></view><view wx:if="{{ocr.isShowResult}}"><view class="ocr-result-title" wx:if="{{!page.ocr.isIdnameAllowEdit && !page.ocr.isIdnumberAllowEdit && !page.ocr.isIdaddressAllowEdit}}">请确认您的身份信息</view><view class="ocr-result-title" wx:else>请确认您的身份信息，若有误请手动修改</view><view id="sms-top"><view class="sms-top-sec ocr-result-sec"><view class="sms-input-title"><label>姓名</label></view><view class="sms-ts-input"><input type="text" bindinput="idnameInputChanged" placeholder="请输入姓名" disabled="{{!page.ocr.isIdnameAllowEdit}}" value="{{ocr.idname}}"/></view></view><view class="sms-top-sec ocr-result-sec"><view class="sms-input-title"><label>身份证</label></view><view class="sms-ts-input"><input type="idcard" bindinput="idcartInputChanged" maxlength="18" placeholder="请输入身份证号" value="{{ocr.idcard}}" disabled="{{!page.ocr.isIdnumberAllowEdit}}"/></view></view><view class="sms-top-sec ocr-result-sec" wx:if="{{page.ocr.isAddress}}"><view class="sms-input-title"><label>住址</label></view><view class="sms-ts-input" style="padding:16rpx 0"><textarea type="text" bindinput="idaddressInputChanged" placeholder="请输入住址" value="{{ocr.idaddress}}" disabled="{{!page.ocr.isIdaddressAllowEdit}}" auto-height="true" style="line-height:1.5em;width:100%;" fixed="true"></textarea></view></view></view><view class="hint-error">{{ocr.hintErrorResult}}</view><view class="{{Common.IsWxNative ? 'index-btnNative' : 'index-btn'}}"><button type="primary" bindtap="ocrInputGoNext" disabled="{{ocr.isForbiddenResultBtn}}" hover-class="{{Common.IsWxNative ? 'btn-hoverNative' : 'btn-hover'}}">下一步</button></view></view><view wx:if="{{page.ocr.isManualInput}}"><view id="sms-top"><view id="sms-top-phone" class="sms-top-sec"><view class="sms-ts-input"><input type="idcard" auto-focus="true" bindinput="idcartManualInputChanged" maxlength="18" placeholder="请输入身份证号"/></view><view class="{{Common.IsWxNative ? 'sms-ts-btn sms-ts-btn-enable color-green' : 'sms-ts-btn sms-ts-btn-enable'}}" bindtap="manualInputTakePhone" wx:if="{{!page.ocr.isHideTakePhoto}}" style="font-size:17px;margin-right:5px;">拍照</view></view><view class="sms-top-sec"><view class="sms-ts-input"><input type="text" placeholder="请输入姓名" bindinput="idnameManualInputChanged"/></view></view></view><view class="hint-error">{{ocr.hintError}}</view><view class="{{Common.IsWxNative ? 'index-btnNative' : 'index-btn'}}"><button type="primary" bindtap="manualInputGoNext" disabled="{{ocr.isForbiddenManualBtn}}" hover-class="{{Common.IsWxNative ? 'btn-hoverNative' : 'btn-hover'}}">下一步</button></view></view><view class="verify-footer" wx:if="{{!ocr.isShowTakePhoto || ocr.isShowResult}}"><view class="verify-footer-logo" style="margin:6rpx 0;" wx:if="{{!page.index.isHideTipsLogo}}"><image src="http://beta.gtimg.com/GodIdent/huiyan/img/hint-logo.png"></image></view></view></view></template>