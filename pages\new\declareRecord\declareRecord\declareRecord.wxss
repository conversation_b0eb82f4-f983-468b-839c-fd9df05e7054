/* miniprogram/pages/order/order.wxss */

page {
  width: 100%;
  height: 100%; 
}

.product-sku {
  color: rgba(0, 0, 0, 0.3);
}

.product-present-price {
  color: rgba(0, 0, 0, 0.6);
}

.product-count {
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
}

.product-status {
  color: #fd6500;
  float: right;
}

.weui-search-bar {
  position: fixed;
  top: 0px;
  width: 100%;
  z-index: 99;
}

.top-tab {
  height: 50px;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  line-height: 100rpx;
  background: #f7f7f7;
  font-size: 16px;
  white-space: nowrap;
  position: fixed;
  top: 48px;
  left: 0;
  z-index: 99;
}

.top-tab-item {
  width: 20%;
  display: inline-block;
  text-align: center;
}

.top-tab-item.active {
  color: red;
  font-weight: bold;
}

.top-tab-content {
  top: 98px;
  position: fixed;
  width: 100%;
  height: 100%;
  overflow: scroll;
}

.top-tab-content-item {
  overflow-y: scroll;
  height: 100%;
}

.corner-tag {
  width: 30px;
  height: 0;
  border-width: 0 15px 15px 15px;
  border-style: none solid solid;
  border-color: transparent transparent #b3710f;
  transform: rotate(-45deg);
  position: absolute;
  left: -20px;
  top: 8px;
  opacity: 0.8;
}

.corner-tag-text {
  color: #fff;
  font-size: 10px;
  width: 35px;
  text-align: center;
  display: inline-block;
  position: absolute;
  top: 0px;
}
