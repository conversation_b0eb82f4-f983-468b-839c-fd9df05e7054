/* pages/meetApply/meetApply.wxss */
.bg{
  height: 100%;
  width: 100%;
  position: absolute;
  z-index: -1;
}
.bg image{
  width: 100%;
  height: 100%;
  display: block;
}
.title{
  margin-top: 15%;
  display: flex;
  flex-direction: column;
  text-align: center;
  color: #fff;
}
.title .top{
  font-size: 56rpx;
}
.title .bottom{
  font-size: 36rpx;
  margin-top: 20rpx;
}
.form{
  margin-top: 60rpx;
}
.form-list{
  width: 80%;
  margin: 0 auto;
  margin-top: 30rpx;
  display: flex;
  color: #fff;
  justify-content: space-between;
}
.form-list label{
  min-width: 100px;
}
.form-list input{
  background: #fff;
  border-radius: 2px;
  color: #333;
  padding: 8rpx 0; 
  padding-left: 20rpx;
  width: calc(100% - 100px);
}
.form-list .datePicker{
  width: calc(100% - 100px);
}
.form-list .picker{
  background: #fff;
  color: #888;
  border-radius: 2px;
  padding: 8rpx 0; 
  width: calc(100% - 10px);
  padding-left: 20rpx;
  margin-bottom: 20rpx;
  height: 23px;
  line-height: 23px;
}
.form-list textarea{
  background: #fff;
  border-radius: 2px;
  color: #333;
  padding: 6rpx 0;
  padding-left: 20rpx;
  width: calc(100% - 100px);
  height: 100px;
}
.btn{
  width: 80%;
  margin: 0 auto;
  margin-top: 100rpx;
}