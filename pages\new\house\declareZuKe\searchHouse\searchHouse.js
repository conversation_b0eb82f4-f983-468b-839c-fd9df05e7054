// pages/new/house/declareZuKe/serachHouse.js
var app = getApp();
var url = app.globalData.requestUrl;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    inputShowed: false,
    inputVal: '',
    code: -1,
    house: []
  },
  checkMobilphoneNumber: function(MobilphoneNumber) {
    let str = /^1\d{10}$/
    if (str.test(MobilphoneNumber)) {
      return true
    } else {
      return false
    }
  },
  bindInvite: function() {
    wx.navigateTo({
      url: '/pages/new/house/inviteJoin/inviteJoin?type=zkyq&phone='+this.data.inputVal,
    })
  },
  getHouseList: function(e) {
    var that = this;

  },
  searchHouse: function() {
    var that = this;
    var data = {
      house: that.data.house,
      code: that.data.code,
    };
    if (this.checkMobilphoneNumber(that.data.inputVal)) {
      wx.request({
        method: 'GET',
        url: url + '/storemgmt/house/list?page_num=1&page_size=0&status=1&other_phone=' + this.data.inputVal,
        header: {
          token: wx.getStorageSync('token')
        },
        success(res) {
          if (res.data.success == 1) {
            var data = {
              code: that.data.code,
              house: that.data.house
            }
            if (res.data.data.data_list.length > 0) {
              data.code = 1;
              for (let i = 0; i < res.data.data.data_list.length; i++) {
                if (res.data.data.data_list[i].img_url == null || res.data.data.data_list[i].img_url == '') {
                  res.data.data.data_list[i].img_url = '/images/image_null.png';
                } else {
                  res.data.data.data_list[i].img_url = url + res.data.data.data_list[i].img_url;
                }
                data.house.push(res.data.data.data_list[i]);
              }
            } else {
              data.code = 0;
            }
            that.setData(data);
          } else {
            data.code = 0;
            wx.showToast({
              title: res.data.dsc,
              icon: 'none',
              duration: 2000
            })
          }
        },
        fail(res) {
          console.log(res)
        }
      })
    } else {
      data.code = -1;
      data.house = [];
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none',
        duration: 2000
      })
    }
    that.setData(data);
  },
  inputTyping: function(e) {
    var that = this;
    var data = {
      inputVal: that.data.inputVal,
      house: that.data.house
    };
    data.code = -1;
    data.house = [];
    data.inputVal = e.detail.value;
    that.setData(data);
  },
  showInput: function() {
    this.setData({
      inputShowed: true
    });
  },
  hideInput: function() {
    this.setData({
      inputVal: "",
      inputShowed: false,
      code: -1,
      house: []
    });
  },
  clearInput: function() {
    this.setData({
      inputVal: "",
      code: -1,
      house: []
    });
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

 
})