<!--miniprogram/pages/user/login.wxml-->
<view class="page">
  <view wx:if="{{canIUse}}">
    <view class="title">
      <view>登录</view>
    </view>
    <!-- 需要使用 button 来授权登录 -->
    <button class="weui-btn" type="default" wx:if="{{!isAgree}}" bindtap="bindCheckIsAgree">手机号快捷登录</button>
    <button class="weui-btn" type="primary" wx:if="{{isAgree}}" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">手机号快捷登录</button>
    <button class="return-btn" bindtap="bindReturnBack">取消登录</button>
  </view>
  <view wx:if="{{!canIUse}}" class="weixin-update-tips">
    <view class="icon-box">
      <icon type="warn" size="64"></icon>
      <view class="icon-box__ctn">
        <view class="icon-box__title">请升级微信版本</view>
        <view class="icon-box__desc">当前微信版本过低</view>
      </view>
    </view>
  </view>
</view>
<view wx:if="{{canIUse}}" class="user-agreement">
  <checkbox-group bindchange="bindAgreeChange">
    <label class="weui-agree" for="weuiAgree">
      <view class="weui-agree__text">
        <view wx:if="{{isShowAgreeTips}}" class="user-agreement-tips">
          <icon></icon>请先阅读并同意协议</view>
        <checkbox class="weui-agree__checkbox" id="weuiAgree" value="agree" checked="{{isAgree}}" />
        <view class="weui-agree__checkbox-icon">
          <icon class="weui-agree__checkbox-icon-check" type="success_no_circle" size="9" wx:if="{{isAgree}}"></icon>
        </view>
        我已阅读并同意
      </view>
    </label>
     <navigator url="/pages/new/user/agreement" class="weui-agree__link">《经开公安小程序使用协议》</navigator>
  </checkbox-group>
</view>