<!--pages/new/shop/info.wxml-->
<watermark></watermark>
<view class="page">
  <view class="page__bd">
    <view class="weui-cells__title">用户信息</view>
    <view class="weui-panel weui-panel_access myweui-row">
      <view class="weui-panel__bd myweui-row-body">
        <navigator url="" class="weui-media-box weui-media-box_appmsg" hover-class="weui-cell_active">
          <view class="weui-media-box__hd weui-media-box__hd_in-appmsg">
            <image class="weui-media-box__thumb" src="{{url}}{{user.best_frame_url}}" />
          </view>
          <view class="weui-media-box__bd weui-media-box__bd_in-appmsg">
            <view class="weui-media-box__title">{{user.name}} {{user.phone}}
            </view>
            <view class="weui-media-box__desc">
              <text>{{user.address}}</text>
            </view>
            <view class="weui-media-box__info">
              <view class="weui-media-box__info__meta" style="display:none;">{{user.create_time}}</view>
              <view class="weui-media-box__info__meta">{{user.nation}}</view>
              <view class="weui-media-box__info__meta weui-media-box__info__meta_extra">{{user.identity}}</view>
            </view>
          </view>
        </navigator>
      </view>
    </view>
    <view class="weui-cell weui-cell_access">
      <view class="weui-cell__hd" data-status='1' bindtap="bindDataShopShow">
        店铺数据：
      </view>
      <view class="weui-cell__bd">
        <block wx:for="{{dataShopList}}" wx:for-item="item" wx:key="id">
          <view class="search-tag" wx:if="{{item.checked}}" bindtap="bindDataShopCancel" data-item="{{item}}">
            <label class="search-tag-close iconfont icon-fail"></label>{{item.name}}</view>
        </block>
      </view>
    </view>
    <view class="weui-cell weui-cell_access">
      <view class="weui-cell__hd" data-status='1' bindtap="bindDataHouseShow">
        小区数据：
      </view>
      <view class="weui-cell__bd">
        <block wx:for="{{dataHouseList}}" wx:for-item="item" wx:key="id">
          <view class="search-tag" wx:if="{{item.checked}}" bindtap="bindDataHouseCancel" data-item="{{item}}">
            <label class="search-tag-close iconfont icon-fail"></label>{{item.name}}</view>
        </block>
      </view>
    </view>
    <view class="weui-cell weui-cell_access">
      <view class="weui-cell__hd" data-status='1' bindtap="bindRoleShow">
        拥有角色：
      </view>
      <view class="weui-cell__bd">
        <block wx:for="{{roleList}}" wx:for-item="item" wx:key="id">
          <view class="search-tag" wx:if="{{item.checked}}" bindtap="bindRoleCancel" data-item="{{item}}">
            <label class="search-tag-close iconfont icon-fail"></label>{{item.name}}</view>
        </block>
      </view>
    </view>
    <view class="weui-cell weui-cell_access">
      <view class="weui-cell__hd" data-status='1' bindtap="bindFunctionShow">
        功能权限：
      </view>
      <view class="weui-cell__bd">
        <block wx:for="{{functionList}}" wx:for-item="item" wx:key="id">
          <view class="search-tag" wx:if="{{item.checked}}" bindtap="bindFunctionCancel" data-item="{{item}}">
            <label class="search-tag-close iconfont icon-fail"></label>{{item.name}}</view>
        </block>
      </view>
    </view>
    <view style="padding:16px;">
      <button class="weui-btn" type="primary" bindtap="bindSave">保存</button>

    </view>
  </view>
  <view class="weui-msg" wx:if="{{submit.code>=0}}">
    <view class="weui-msg__icon-area">
      <icon type="{{submit.code==0?'success':'warn'}}" size="64"></icon>
    </view>
    <view class="weui-msg__text-area">
      <view class="weui-msg__title">{{submit.code==0?'操作成功':'操作失败'}}</view>
      <view class="weui-msg__desc">{{submit.msg}}
      </view>
    </view>
    <view class="weui-msg__opr-area">
      <view class="weui-btn-area">
        <button class="weui-btn" type="primary">提交审核</button>
      </view>
    </view>
    <view class="weui-msg__opr-area">
      <view class="weui-btn-area">
        <button class="weui-btn" type="default" bindtap="bindGotoList">完成</button>
      </view>
    </view>
    <view class="weui-msg__tips-area">
      <view class="weui-msg__tips">如果所有资料已确认无误，您可以直接提交审核</view>
    </view>
  </view>
</view>
<view class="cu-modal drawer-modal justify-start {{dataHouseShow? 'show': ''}}" data-status='0' bindtap="bindDataHouseShow">
  <view class="cu-dialog basis-lg" catchtap>
    <scroll-view style="height:100%;" scroll-y="true">
      <view class="cu-list menu text-left">
        <view class="cu-item">
          <view class="content">
            <view>小区模块数据权限</view>
          </view>
        </view>
      </view>
      <view class="weui-cells weui-cells_after-title">
        <view class="multi-item" wx:for="{{dataHouseTree}}" wx:for-item="item" wx:key="id">
          <view class="weui-cell weui-cell_access" data-item='{{item}}' bindtap="bindDataHouseClick">
            <view class="weui-cell__hd weui-check__hd_in-checkbox">
              <icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item.checked}}"></icon>
              <icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item.checked}}"></icon>
            </view>
            <view class="weui-cell__bd">{{item.name}}</view>
          </view>
          <view class="multi-item" wx:if="{{item.children.length>0}}" wx:for="{{item.children}}" wx:for-item="item2" wx:key="id">
            <view class="weui-cell weui-cell_access" data-item='{{item2}}' bindtap="bindDataHouseClick">
              <view class="weui-cell__hd weui-check__hd_in-checkbox">
                <icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item2.checked}}"></icon>
                <icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item2.checked}}"></icon>
              </view>
              <view class="weui-cell__bd" space="emsp">　{{item2.name}}</view>
            </view>
            <view class="multi-item" wx:if="{{item2.children.length>0}}" wx:for="{{item2.children}}" wx:for-item="item3" wx:key="id">
              <view class="weui-cell weui-cell_access">
                <view class="weui-cell__hd weui-check__hd_in-checkbox" data-item='{{item3}}' bindtap="bindDataHouseClick">
                  <icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item3.checked}}"></icon>
                  <icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item3.checked}}"></icon>
                </view>
                <view class="weui-cell__bd" space="emsp" data-item='{{item3}}' bindtap="bindDataHouseClick">　　{{item3.name}}</view>
                <view class="weui-cell__ft cuIcon-unfold arrow-down" data-item="{{item3}}" data-level="1" bindtap="getCommunityList" hover-class="weui-cell_active"></view>
              </view>
              <view class="multi-item" wx:if="{{item3.children.length>0}}" wx:for="{{item3.children}}" wx:for-item="item4" wx:key="id">
                <view class="weui-cell weui-cell_access">
                  <view class="weui-cell__hd weui-check__hd_in-checkbox" data-item='{{item4}}' bindtap="bindDataHouseClick">
                    <icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item4.checked}}"></icon>
                    <icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item4.checked}}"></icon>
                  </view>
                  <view class="weui-cell__bd" space="emsp" data-item='{{item4}}' bindtap="bindDataHouseClick">　　　{{item4.name}}</view>
                  <view class="weui-cell__ft cuIcon-unfold arrow-down" data-item="{{item3}}" data-level="2" bindtap="getCommunityList" hover-class="weui-cell_active"></view>
                </view>
                <view class="multi-item" wx:if="{{item4.children.length>0}}" wx:for="{{item4.children}}" wx:for-item="item5" wx:key="id">
                  <view class="weui-cell weui-cell_access" data-item='{{item5}}' bindtap="bindDataHouseClick">
                    <view class="weui-cell__hd weui-check__hd_in-checkbox">
                      <icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item5.checked}}"></icon>
                      <icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item5.checked}}"></icon>
                    </view>
                    <view class="weui-cell__bd" space="emsp">　　　　{{item5.name}}</view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</view>
<view class="cu-modal drawer-modal justify-start {{dataShopShow? 'show': ''}}" data-status='0' bindtap="bindDataShopShow">
  <view class="cu-dialog basis-lg" catchtap>
    <scroll-view style="height:100%;" scroll-y="true">
      <view class="cu-list menu text-left">
        <view class="cu-item">
          <view class="content">
            <view>店铺模块数据权限</view>
          </view>
        </view>
      </view>
      <view class="weui-cells weui-cells_after-title">
        <view class="multi-item" wx:for="{{dataShopTree}}" wx:for-item="item" wx:key="id">
          <view class="weui-cell weui-cell_access" data-item='{{item}}' bindtap="bindDataHouseClick">
            <view class="weui-cell__hd weui-check__hd_in-checkbox">
              <icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item.checked}}"></icon>
              <icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item.checked}}"></icon>
            </view>
            <view class="weui-cell__bd">{{item.name}}</view>
          </view>
          <view class="multi-item" wx:if="{{item.children.length>0}}" wx:for="{{item.children}}" wx:for-item="item2" wx:key="id">
            <view class="weui-cell weui-cell_access" data-item='{{item2}}' bindtap="bindDataHouseClick">
              <view class="weui-cell__hd weui-check__hd_in-checkbox">
                <icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item2.checked}}"></icon>
                <icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item2.checked}}"></icon>
              </view>
              <view class="weui-cell__bd" space="emsp">　{{item2.name}}</view>
            </view>
            <view class="multi-item" wx:if="{{item2.children.length>0}}" wx:for="{{item2.children}}" wx:for-item="item3" wx:key="id">
              <view class="weui-cell weui-cell_access">
                <view class="weui-cell__hd weui-check__hd_in-checkbox" data-item='{{item3}}' bindtap="bindDataHouseClick">
                  <icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item3.checked}}"></icon>
                  <icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item3.checked}}"></icon>
                </view>
                <view class="weui-cell__bd" space="emsp" data-item='{{item3}}' bindtap="bindDataHouseClick">　　{{item3.name}}</view>
                <view class="weui-cell__ft cuIcon-unfold arrow-down" data-item="{{item3}}" data-level="1" bindtap="getCommunityList" hover-class="weui-cell_active"></view>
              </view>
              <view class="multi-item" wx:if="{{item3.children.length>0}}" wx:for="{{item3.children}}" wx:for-item="item4" wx:key="id">
                <view class="weui-cell weui-cell_access">
                  <view class="weui-cell__hd weui-check__hd_in-checkbox" data-item='{{item4}}' bindtap="bindDataHouseClick">
                    <icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item4.checked}}"></icon>
                    <icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item4.checked}}"></icon>
                  </view>
                  <view class="weui-cell__bd" space="emsp" data-item='{{item4}}' bindtap="bindDataHouseClick">　　　{{item4.name}}</view>
                  <view class="weui-cell__ft cuIcon-unfold arrow-down" data-item="{{item3}}" data-level="2" bindtap="getCommunityList" hover-class="weui-cell_active"></view>
                </view>
                <view class="multi-item" wx:if="{{item4.children.length>0}}" wx:for="{{item4.children}}" wx:for-item="item5" wx:key="id">
                  <view class="weui-cell weui-cell_access" data-item='{{item5}}' bindtap="bindDataHouseClick">
                    <view class="weui-cell__hd weui-check__hd_in-checkbox">
                      <icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item5.checked}}"></icon>
                      <icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item5.checked}}"></icon>
                    </view>
                    <view class="weui-cell__bd" space="emsp">　　　　{{item5.name}}</view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</view>
<view class="cu-modal drawer-modal justify-start {{functionShow? 'show': ''}}" data-status='0' bindtap="bindFunctionShow">
  <view class="cu-dialog basis-lg" catchtap>
    <scroll-view style="height:100%;" scroll-y="true">
      <view class="cu-list menu text-left">
        <view class="cu-item">
          <view class="content">
            <view>功能权限</view>
          </view>
        </view>
      </view>
      <view class="weui-cells weui-cells_after-title">
        <view class="multi-item" wx:for="{{functionTree}}" wx:for-item="item" wx:key="id">
          <view class="weui-cell weui-cell_access" data-item='{{item}}' bindtap="bindFunctionClick">
            <view class="weui-cell__hd weui-check__hd_in-checkbox">
              <block wx:if="{{item.code!=''}}">
                <icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item.checked}}"></icon>
                <icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item.checked}}"></icon>
              </block>
              <block wx:if="{{item.code==''}}">
                <icon class="weui-icon-checkbox_circle" type="circle" size="23" style="visibility:hidden;"></icon>
              </block>
            </view>
            <view class="weui-cell__bd">{{item.name}}</view>
          </view>
          <view class="multi-item" wx:if="{{item.children.length>0}}" wx:for="{{item.children}}" wx:for-item="item2" wx:key="id">
            <view class="weui-cell weui-cell_access" data-item='{{item2}}' bindtap="bindFunctionClick">
              <view class="weui-cell__hd weui-check__hd_in-checkbox">
                <block wx:if="{{item2.code!=''}}">
                  <icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item2.checked}}"></icon>
                  <icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item2.checked}}"></icon>
                </block>
                <block wx:if="{{item2.code==''}}">
                  <icon class="weui-icon-checkbox_circle" type="circle" size="23" style="visibility:hidden;"></icon>
                </block>
              </view>
              <view class="weui-cell__bd" space="emsp">　{{item2.name}}</view>
            </view>
            <view class="multi-item" wx:if="{{item2.children.length>0}}" wx:for="{{item2.children}}" wx:for-item="item3" wx:key="id">
              <view class="weui-cell weui-cell_access" data-item='{{item3}}' bindtap="bindFunctionClick">
                <view class="weui-cell__hd weui-check__hd_in-checkbox">
                  <block wx:if="{{item3.code!=''}}">
                    <icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item3.checked}}"></icon>
                    <icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item3.checked}}"></icon>
                  </block>
                  <block wx:if="{{item3.code==''}}">
                    <icon class="weui-icon-checkbox_circle" type="circle" size="23" style="visibility:hidden;"></icon>
                  </block>
                </view>
                <view class="weui-cell__bd" space="emsp">　　{{item3.name}}</view>
              </view>
              <view class="multi-item" wx:if="{{item3.children.length>0}}" wx:for="{{item3.children}}" wx:for-item="item4" wx:key="id">
                <view class="weui-cell weui-cell_access" data-item='{{item4}}' bindtap="bindFunctionClick">
                  <view class="weui-cell__hd weui-check__hd_in-checkbox">
                    <block wx:if="{{item4.code!=''}}">
                      <icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item4.checked}}"></icon>
                      <icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item4.checked}}"></icon>
                    </block>
                    <block wx:if="{{item4.code==''}}">
                      <icon class="weui-icon-checkbox_circle" type="circle" size="23" style="visibility:hidden;"></icon>
                    </block>
                  </view>
                  <view class="weui-cell__bd" space="emsp">　　　{{item4.name}}</view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</view>
<view class="cu-modal drawer-modal justify-start {{roleShow? 'show': ''}}" data-status='0' bindtap="bindRoleShow">
  <view class="cu-dialog basis-lg" catchtap>
    <scroll-view style="height:100%;" scroll-y="true">
      <view class="cu-list menu text-left">
        <view class="cu-item">
          <view class="content">
            <view>拥有角色</view>
          </view>
        </view>
      </view>
      <view class="weui-cells weui-cells_after-title">
        <view class="multi-item" wx:for="{{roleTree}}" wx:for-item="item" wx:key="id">
          <view class="weui-cell weui-cell_access" data-item='{{item}}' bindtap="bindRoleClick">
            <view class="weui-cell__hd weui-check__hd_in-checkbox">
              <block wx:if="{{item.code!=''}}">
                <icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item.checked}}"></icon>
                <icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item.checked}}"></icon>
              </block>
              <block wx:if="{{item.code==''}}">
                <icon class="weui-icon-checkbox_circle" type="circle" size="23" style="visibility:hidden;"></icon>
              </block>
            </view>
            <view class="weui-cell__bd">{{item.name}}</view>
          </view>
          <view class="multi-item" wx:if="{{item.children.length>0}}" wx:for="{{item.children}}" wx:for-item="item2" wx:key="id">
            <view class="weui-cell weui-cell_access" data-item='{{item2}}' bindtap="bindRoleClick">
              <view class="weui-cell__hd weui-check__hd_in-checkbox">
                <block wx:if="{{item2.code!=''}}">
                  <icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item2.checked}}"></icon>
                  <icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item2.checked}}"></icon>
                </block>
                <block wx:if="{{item2.code==''}}">
                  <icon class="weui-icon-checkbox_circle" type="circle" size="23" style="visibility:hidden;"></icon>
                </block>
              </view>
              <view class="weui-cell__bd" space="emsp">　{{item2.name}}</view>
            </view>
            <view class="multi-item" wx:if="{{item2.children.length>0}}" wx:for="{{item2.children}}" wx:for-item="item3" wx:key="id">
              <view class="weui-cell weui-cell_access" data-item='{{item3}}' bindtap="bindRoleClick">
                <view class="weui-cell__hd weui-check__hd_in-checkbox">
                  <block wx:if="{{item3.code!=''}}">
                    <icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item3.checked}}"></icon>
                    <icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item3.checked}}"></icon>
                  </block>
                  <block wx:if="{{item3.code==''}}">
                    <icon class="weui-icon-checkbox_circle" type="circle" size="23" style="visibility:hidden;"></icon>
                  </block>
                </view>
                <view class="weui-cell__bd" space="emsp">　　{{item3.name}}</view>
              </view>
              <view class="multi-item" wx:if="{{item3.children.length>0}}" wx:for="{{item3.children}}" wx:for-item="item4" wx:key="id">
                <view class="weui-cell weui-cell_access" data-item='{{item4}}' bindtap="bindRoleClick">
                  <view class="weui-cell__hd weui-check__hd_in-checkbox">
                    <block wx:if="{{item4.code!=''}}">
                      <icon class="weui-icon-checkbox_circle" type="circle" size="23" wx:if="{{!item4.checked}}"></icon>
                      <icon class="weui-icon-checkbox_success" type="success" size="23" wx:if="{{item4.checked}}"></icon>
                    </block>
                    <block wx:if="{{item4.code==''}}">
                      <icon class="weui-icon-checkbox_circle" type="circle" size="23" style="visibility:hidden;"></icon>
                    </block>
                  </view>
                  <view class="weui-cell__bd" space="emsp">　　　{{item4.name}}</view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</view>