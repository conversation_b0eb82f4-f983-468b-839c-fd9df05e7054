import { VantComponent } from '../common/component';
import { button } from '../mixins/button';
import { openType } from '../mixins/open-type';
VantComponent({
    mixins: [button, openType],
    classes: ['hover-class', 'loading-class'],
    props: {
        icon: String,
        color: String,
        plain: <PERSON><PERSON><PERSON>,
        block: <PERSON><PERSON><PERSON>,
        round: <PERSON><PERSON>an,
        square: <PERSON><PERSON><PERSON>,
        loading: <PERSON><PERSON><PERSON>,
        hairline: <PERSON><PERSON><PERSON>,
        disabled: <PERSON><PERSON><PERSON>,
        loadingText: String,
        type: {
            type: String,
            value: 'default'
        },
        size: {
            type: String,
            value: 'normal'
        },
        loadingSize: {
            type: String,
            value: '20px'
        }
    },
    methods: {
        onClick() {
            if (!this.data.disabled && !this.data.loading) {
                this.$emit('click');
            }
        }
    }
});
